# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_api_key

# Database Configuration (PostgreSQL)
DATABASE_URL="postgresql://username:password@localhost:5432/hvppy_central?schema=public"

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Next.js Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Environment
NODE_ENV=development

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760 # 10MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,video/mp4,audio/mpeg,audio/wav

# AI Configuration
NEXT_PUBLIC_AI_MODEL=gpt-4-turbo-preview
NEXT_PUBLIC_ENABLE_AI_FEATURES=true

# Feature Flags
NEXT_PUBLIC_ENABLE_EXPERIMENTAL_FEATURES=false
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# Auth.js
AUTH_SECRET=""
AUTH_GITHUB_ID=""
AUTH_GITHUB_SECRET=""

# Nodemailer
EMAIL_SERVER_HOST=""
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_SECURE="false"
EMAIL_SERVER_USER=""
EMAIL_SERVER_PASSWORD=""
EMAIL_FROM=""

# Google OAuth
AUTH_GOOGLE_ID=""
AUTH_GOOGLE_SECRET=""

# Facebook OAuth
AUTH_FACEBOOK_ID=""
AUTH_FACEBOOK_SECRET=""

# Appwrite
NEXT_PUBLIC_APPWRITE_ENDPOINT=""
NEXT_PUBLIC_APPWRITE_PROJECT_ID=""

# Appwrite Bucket ID
APPWRITE_BUCKET_ID=""

# Appwrite Server SDK Key
APPWRITE_API_KEY=""

# Appwrite Chat
NEXT_PUBLIC_APPWRITE_DATABASE_ID=""
NEXT_PUBLIC_APPWRITE_COLLECTION_ID_CHAT=""

# Transcoding Service
TRANSCODING_SERVICE_API_KEY=""
TRANSCODING_SERVICE_ENDPOINT="https://api.example.com/transcode"

# Moderation Service
MODERATION_SERVICE_API_KEY=""
MODERATION_SERVICE_ENDPOINT="https://api.example.com/moderate"
