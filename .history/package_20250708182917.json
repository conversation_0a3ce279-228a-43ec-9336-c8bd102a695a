{"name": "hvppy-central", "version": "0.1.0", "private": true, "description": "A media content sharing and management platform for artists and content creators with emotional-AI fan engagement", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "ai": "latest", "@ai-sdk/google": "latest", "@ai-sdk/react": "latest", "appwrite": "^16.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.11.17", "lucide-react": "^0.460.0", "next": "^15.0.0", "openai": "^4.67.3", "react": "^19.0.0", "react-day-picker": "^9.1.3", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-resizable-panels": "^2.1.4", "sonner": "^1.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.0.0", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^22.8.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^15.0.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "prisma": "^5.22.0", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}, "engines": {"node": ">=18.0.0"}}