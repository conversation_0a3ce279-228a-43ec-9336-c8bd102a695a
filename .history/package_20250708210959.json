{"name": "hvppy-central", "version": "0.1.0", "private": true, "description": "A media content sharing and management platform for artists and content creators with emotional-AI fan engagement", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node scripts/seed.js", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/google": "latest", "@ai-sdk/react": "latest", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "ai": "latest", "appwrite": "^16.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.11.17", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "next": "^15.0.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "openai": "^4.67.3", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.9", "recharts": "^3.0.2", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^22.8.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^8.57.1", "eslint-config-next": "^15.0.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "prisma": "^5.22.0", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}, "engines": {"node": ">=18.0.0"}}