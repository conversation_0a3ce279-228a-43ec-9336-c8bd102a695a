#!/usr/bin/env node

/**
 * HVPPY Central Database Seeding Script
 * 
 * This script seeds the database with realistic content including:
 * - 8 diverse users (6 creators, 2 fans)
 * - 6 creator profiles with social links
 * - 8 unique personas with different moods
 * - 13 realistic posts with stock media
 * - Authentic reactions and fan memories
 * - Realistic follow relationships
 */

// Import using dynamic import for TypeScript compatibility
let seedDatabase

async function main() {
  console.log('🌱 HVPPY Central Database Seeding')
  console.log('================================')
  console.log('')
  
  try {
    const startTime = Date.now()
    
    console.log('🚀 Starting comprehensive database seeding...')
    const result = await seedDatabase()
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log('')
    console.log('🎉 Seeding completed successfully!')
    console.log(`⏱️  Duration: ${duration}ms`)
    console.log('')
    console.log('📊 Summary:')
    console.log(`   👥 Users: ${result.summary.users}`)
    console.log(`   🎨 Creators: ${result.summary.creators}`)
    console.log(`   🎭 Personas: ${result.summary.personas}`)
    console.log(`   📝 Posts: ${result.summary.posts}`)
    console.log(`   ❤️  Reactions: ${result.summary.reactions}`)
    console.log(`   💭 Memories: ${result.summary.memories}`)
    console.log(`   👥 Follows: ${result.summary.follows}`)
    console.log('')
    console.log('🎯 What to try next:')
    console.log('   • Visit /feed/discover to see the vertical feed')
    console.log('   • Check /profile for user profiles')
    console.log('   • Explore /studio for creator dashboard')
    console.log('   • Search content at /search')
    console.log('')
    console.log('🖼️  Stock Media:')
    console.log('   • High-quality avatars from Unsplash')
    console.log('   • Music cover art and video thumbnails')
    console.log('   • Realistic content with proper mood tagging')
    console.log('')
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}
