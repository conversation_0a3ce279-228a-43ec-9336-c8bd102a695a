import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { CreateInteractionRequest } from '@/types/feed'
import { ReactionType } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const postId = params.id
    const body: CreateInteractionRequest = await request.json()
    const { type, mood, metadata } = body

    // TODO: Get user ID from authentication
    const userId = 'temp-user-id' // Replace with actual auth

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    })

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      )
    }

    // Check if user already reacted to this post
    const existingReaction = await prisma.reaction.findUnique({
      where: {
        userId_postId: {
          userId,
          postId,
        },
      },
    })

    let reaction
    if (existingReaction) {
      // Update existing reaction
      reaction = await prisma.reaction.update({
        where: {
          userId_postId: {
            userId,
            postId,
          },
        },
        data: {
          type,
          mood,
        },
      })
    } else {
      // Create new reaction
      reaction = await prisma.reaction.create({
        data: {
          userId,
          postId,
          type,
          mood,
        },
      })

      // Increment like count on post
      await prisma.post.update({
        where: { id: postId },
        data: {
          likeCount: {
            increment: 1,
          },
        },
      })
    }

    // Get updated post with counts
    const updatedPost = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        _count: {
          select: {
            reactions: true,
            memories: true,
          },
        },
      },
    })

    return NextResponse.json({
      reaction,
      post: updatedPost,
    })
  } catch (error) {
    console.error('Interaction API error:', error)
    return NextResponse.json(
      { error: 'Failed to create interaction' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const postId = params.id
    
    // TODO: Get user ID from authentication
    const userId = 'temp-user-id' // Replace with actual auth

    // Check if reaction exists
    const existingReaction = await prisma.reaction.findUnique({
      where: {
        userId_postId: {
          userId,
          postId,
        },
      },
    })

    if (!existingReaction) {
      return NextResponse.json(
        { error: 'Reaction not found' },
        { status: 404 }
      )
    }

    // Delete reaction
    await prisma.reaction.delete({
      where: {
        userId_postId: {
          userId,
          postId,
        },
      },
    })

    // Decrement like count on post
    await prisma.post.update({
      where: { id: postId },
      data: {
        likeCount: {
          decrement: 1,
        },
      },
    })

    // Get updated post with counts
    const updatedPost = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        _count: {
          select: {
            reactions: true,
            memories: true,
          },
        },
      },
    })

    return NextResponse.json({
      post: updatedPost,
    })
  } catch (error) {
    console.error('Remove interaction API error:', error)
    return NextResponse.json(
      { error: 'Failed to remove interaction' },
      { status: 500 }
    )
  }
}
