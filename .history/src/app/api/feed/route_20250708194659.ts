import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { FeedType, GetFeedRequest, FeedResponse, FeedItem } from '@/types/feed'
import { ContentType } from '@/types'

export async function POST(request: NextRequest) {
  try {
    const body: GetFeedRequest = await request.json()
    const { type, cursor, limit = 20, filters, userId } = body

    // Build the base query
    let whereClause: any = {
      isPublic: true,
      publishedAt: { not: null },
    }

    // Apply filters
    if (filters) {
      if (filters.moods?.length) {
        whereClause.moods = {
          hasSome: filters.moods,
        }
      }

      if (filters.contentTypes?.length) {
        whereClause.contentType = {
          in: filters.contentTypes,
        }
      }

      if (filters.isExperimental !== undefined) {
        whereClause.isExperimental = filters.isExperimental
      }

      if (filters.creatorIds?.length) {
        whereClause.creatorId = {
          in: filters.creatorIds,
        }
      }

      if (filters.personaIds?.length) {
        whereClause.personaId = {
          in: filters.personaIds,
        }
      }

      if (filters.timeRange) {
        const now = new Date()
        let startDate: Date

        switch (filters.timeRange) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            break
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            break
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1)
            break
          default:
            startDate = new Date(0) // All time
        }

        whereClause.publishedAt = {
          gte: startDate,
        }
      }
    }

    // Apply feed type specific logic
    switch (type) {
      case FeedType.FOLLOWING:
        if (userId) {
          // Get users that the current user follows
          const following = await prisma.follow.findMany({
            where: { followerId: userId },
            select: { followingId: true },
          })

          whereClause.userId = {
            in: following.map(f => f.followingId),
          }
        } else {
          // If no user, return empty feed
          return NextResponse.json({
            items: [],
            hasMore: false,
            nextCursor: null,
          })
        }
        break

      case FeedType.TRENDING:
        // Add trending logic (high engagement in recent time)
        whereClause.publishedAt = {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        }
        break

      case FeedType.EXPERIMENTAL:
        whereClause.isExperimental = true
        break

      case FeedType.DISCOVER:
      default:
        // Default discover feed - no additional filters
        break
    }

    // Handle cursor-based pagination
    if (cursor) {
      whereClause.id = {
        lt: cursor,
      }
    }

    // Determine sort order based on feed type
    let orderBy: any = { publishedAt: 'desc' }

    if (type === FeedType.TRENDING) {
      // Sort by engagement score for trending
      orderBy = [
        { likeCount: 'desc' },
        { viewCount: 'desc' },
        { publishedAt: 'desc' },
      ]
    }

    // Fetch posts
    const posts = await prisma.post.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
            avatar: true,
            isVerified: true,
          },
        },
        creator: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                displayName: true,
                avatar: true,
                isVerified: true,
              },
            },
          },
        },
        persona: true,
        reactions: {
          select: {
            id: true,
            type: true,
            mood: true,
            userId: true,
          },
        },
        memories: {
          select: {
            id: true,
            title: true,
            userId: true,
          },
        },
        _count: {
          select: {
            reactions: true,
            memories: true,
          },
        },
      },
      orderBy,
      take: limit + 1, // Take one extra to check if there are more
    })

    // Check if there are more items
    const hasMore = posts.length > limit
    const items = posts.slice(0, limit)

    // Convert to FeedItem format
    const feedItems: FeedItem[] = items.map(post => ({
      id: post.id,
      post: post as any,
      insertedAt: new Date(),
      feedScore: calculateFeedScore(post, type),
      moodMatch: calculateMoodMatch(post, filters?.moods),
    }))

    // Get next cursor
    const nextCursor = hasMore ? items[items.length - 1].id : null

    const response: FeedResponse = {
      items: feedItems,
      hasMore,
      nextCursor,
      totalCount: undefined, // Could add total count if needed
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Feed API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch feed' },
      { status: 500 }
    )
  }
}

// Helper function to calculate feed relevance score
function calculateFeedScore(post: any, feedType: FeedType): number {
  let score = 0

  // Base engagement score
  const engagementScore = (post.likeCount * 1) + (post.viewCount * 0.1) + (post.shareCount * 2)
  score += Math.min(engagementScore / 100, 1) // Normalize to 0-1

  // Recency score
  const ageInHours = (Date.now() - new Date(post.publishedAt).getTime()) / (1000 * 60 * 60)
  const recencyScore = Math.max(0, 1 - (ageInHours / 168)) // Decay over a week
  score += recencyScore * 0.3

  // Feed type specific scoring
  switch (feedType) {
    case FeedType.TRENDING:
      score += engagementScore / 50 // Boost engagement for trending
      break
    case FeedType.EXPERIMENTAL:
      score += post.isExperimental ? 0.5 : 0
      break
  }

  return Math.min(score, 1)
}

// Helper function to calculate mood matching score
function calculateMoodMatch(post: any, targetMoods?: string[]): number {
  if (!targetMoods?.length || !post.moods?.length) {
    return 0
  }

  const matchingMoods = post.moods.filter((mood: string) => targetMoods.includes(mood))
  return matchingMoods.length / targetMoods.length
}
