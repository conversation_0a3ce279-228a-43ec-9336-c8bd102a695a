import { NextResponse } from 'next/server'
import { seedDatabase } from '@/lib/seed-data'

export async function POST() {
  try {
    // Only allow seeding in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Seeding is only allowed in development' },
        { status: 403 }
      )
    }

    const result = await seedDatabase()
    
    return NextResponse.json({
      message: 'Database seeded successfully',
      data: {
        usersCreated: result.users.length,
        creatorsCreated: result.creators.length,
        personasCreated: result.personas.length,
        postsCreated: result.posts.length,
      },
    })
  } catch (error) {
    console.error('Seed API error:', error)
    return NextResponse.json(
      { error: 'Failed to seed database' },
      { status: 500 }
    )
  }
}
