import { NextResponse } from 'next/server'
import { seedDatabase } from '@/lib/seed-data'

export async function POST() {
  try {
    // Only allow seeding in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Seeding is only allowed in development' },
        { status: 403 }
      )
    }

    console.log('🚀 Starting database seeding via API...')
    const startTime = Date.now()

    const result = await seedDatabase()

    const endTime = Date.now()
    const duration = endTime - startTime

    return NextResponse.json({
      success: true,
      message: '🎉 Database seeded successfully with realistic content!',
      duration: `${duration}ms`,
      summary: result.summary,
      details: {
        users: result.users.map(u => ({ id: u.id, username: u.username, role: u.role })),
        creators: result.creators.map(c => ({ id: c.id, stageName: c.stageName, genre: c.genre })),
        personas: result.personas.map(p => ({ id: p.id, name: p.name, mood: p.mood })),
        posts: result.posts.map(p => ({
          id: p.id,
          title: p.title,
          contentType: p.contentType,
          moods: p.moods,
          viewCount: p.viewCount,
          likeCount: p.likeCount
        })),
      },
      stockMedia: {
        avatars: 8,
        musicCovers: 8,
        videoThumbnails: 6,
        description: 'Using high-quality stock media from Unsplash and free audio samples'
      }
    })
  } catch (error) {
    console.error('❌ Seed API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'HVPPY Central Database Seeding API',
    description: 'POST to this endpoint to seed the database with realistic content',
    features: [
      '8 diverse users (6 creators, 2 fans)',
      '6 creator profiles with social links',
      '8 unique personas with different moods',
      '13 realistic posts with stock media',
      'Authentic reactions and fan memories',
      'Realistic follow relationships',
      'High-quality stock images from Unsplash',
      'Mood-based content categorization'
    ],
    note: 'Only available in development mode'
  })
}
