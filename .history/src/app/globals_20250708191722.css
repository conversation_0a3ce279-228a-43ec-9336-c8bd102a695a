@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: oklch(0.9383 0.0042 236.4993);
    --foreground: oklch(0.3211 0 0);
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.3211 0 0);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.3211 0 0);
    --primary: oklch(0.6397 0.1720 36.4421);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9670 0.0029 264.5419);
    --secondary-foreground: oklch(0.4461 0.0263 256.8018);
    --muted: oklch(0.9846 0.0017 247.8389);
    --muted-foreground: oklch(0.5510 0.0234 264.3637);
    --accent: oklch(0.9119 0.0222 243.8174);
    --accent-foreground: oklch(0.3791 0.1378 265.5222);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9022 0.0052 247.8822);
    --input: oklch(0.9700 0.0029 264.5420);
    --ring: oklch(0.6397 0.1720 36.4421);
    --radius: 0.75rem;
    --chart-1: oklch(0.7156 0.0605 248.6845);
    --chart-2: oklch(0.7875 0.0917 35.9616);
    --chart-3: oklch(0.5778 0.0759 254.1573);
    --chart-4: oklch(0.5016 0.0849 259.4902);
    --chart-5: oklch(0.4241 0.0952 264.0306);
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --sidebar: oklch(0.9030 0.0046 258.3257);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.1;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 1px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
  }

  .dark {
    --background: oklch(0.2598 0.0306 262.6666);
    --foreground: oklch(0.9219 0 0);
    --card: oklch(0.3106 0.0301 268.6365);
    --card-foreground: oklch(0.9219 0 0);
    --popover: oklch(0.2900 0.0249 268.3986);
    --popover-foreground: oklch(0.9219 0 0);
    --primary: oklch(0.6397 0.1720 36.4421);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.3095 0.0266 266.7132);
    --secondary-foreground: oklch(0.9219 0 0);
    --muted: oklch(0.3095 0.0266 266.7132);
    --muted-foreground: oklch(0.7155 0 0);
    --accent: oklch(0.3380 0.0589 267.5867);
    --accent-foreground: oklch(0.8823 0.0571 254.1284);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3843 0.0301 269.7337);
    --input: oklch(0.3843 0.0301 269.7337);
    --ring: oklch(0.6397 0.1720 36.4421);
    --chart-1: oklch(0.7156 0.0605 248.6845);
    --chart-2: oklch(0.7693 0.0876 34.1875);
    --chart-3: oklch(0.5778 0.0759 254.1573);
    --chart-4: oklch(0.5016 0.0849 259.4902);
    --chart-5: oklch(0.4241 0.0952 264.0306);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --radius: 0.75rem;
    --sidebar: oklch(0.3100 0.0283 267.7408);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.1;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 1px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
  }
  .theme {
    --font-sans: Inter, sans-serif;
    --font-mono: JetBrains Mono, monospace;
    --font-serif: Source Serif 4, serif;
    --radius: 0.75rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom HVPPY Central styles */
@layer components {
  .hvppy-gradient {
    @apply bg-gradient-to-r from-hvppy-500 to-hvppy-700;
  }

  .mood-happy {
    @apply bg-mood-happy text-black;
  }

  .mood-chill {
    @apply bg-mood-chill text-black;
  }

  .mood-heartbroken {
    @apply bg-mood-heartbroken text-white;
  }

  .mood-inspired {
    @apply bg-mood-inspired text-white;
  }

  .mood-energetic {
    @apply bg-mood-energetic text-white;
  }

  .mood-peaceful {
    @apply bg-mood-peaceful text-black;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .content-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  .creator-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-hvppy-100 text-hvppy-800 dark:bg-hvppy-900 dark:text-hvppy-200;
  }
}