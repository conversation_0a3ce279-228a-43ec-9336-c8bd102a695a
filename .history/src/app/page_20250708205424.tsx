import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, Music, Users, Sparkles, <PERSON>, Palette } from "lucide-react"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-hvppy-50/20">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <Badge variant="secondary" className="mb-4">
            🚀 Now in Development
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 hvppy-gradient bg-clip-text text-transparent">
            Welcome to HVPPY Central
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            The future of content sharing and fan engagement. Where artists connect with fans through 
            emotional-AI powered experiences and innovative creator tools.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="hvppy-gradient">
              Join as Creator
            </Button>
            <Button size="lg" variant="outline">
              Explore as Fan
            </Button>
            <Button size="lg" variant="secondary" asChild>
              <a href="/feed/discover">Try Feed Demo</a>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">
          Revolutionary Features
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="content-card">
            <CardHeader>
              <Brain className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Emotional-AI Fan Engagement</CardTitle>
              <CardDescription>
                Fans choose their mood and get content that matches their vibe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge className="mood-happy">Happy</Badge>
                <Badge className="mood-chill">Chill</Badge>
                <Badge className="mood-heartbroken">Heartbroken</Badge>
                <Badge className="mood-inspired">Inspired</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="content-card">
            <CardHeader>
              <Users className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Persona-Based Channels</CardTitle>
              <CardDescription>
                Creators can have multiple personas under one account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Singer, Producer, Storyteller - each with their own feed and community
              </p>
            </CardContent>
          </Card>

          <Card className="content-card">
            <CardHeader>
              <Sparkles className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Experimental Content Lab</CardTitle>
              <CardDescription>
                Test new content formats and get real-time fan feedback
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Like Netflix previews meets Kickstarter for creative content
              </p>
            </CardContent>
          </Card>

          <Card className="content-card">
            <CardHeader>
              <Palette className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Content OS</CardTitle>
              <CardDescription>
                Drag-and-drop content creation with AI assistance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Modular content building with real-time collaboration
              </p>
            </CardContent>
          </Card>

          <Card className="content-card">
            <CardHeader>
              <Music className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Vibe Timelines</CardTitle>
              <CardDescription>
                Fan storyline journeys through creator eras and chapters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Visual timelines showing artistic evolution and fan memories
              </p>
            </CardContent>
          </Card>

          <Card className="content-card">
            <CardHeader>
              <Heart className="h-8 w-8 text-hvppy-500 mb-2" />
              <CardTitle>Fan Memories</CardTitle>
              <CardDescription>
                Save clips, moments, and create memory cards
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Fans can create and share their favorite moments from content
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Development Status */}
      <section className="container mx-auto px-4 py-16">
        <Card className="max-w-2xl mx-auto text-center">
          <CardHeader>
            <CardTitle>🚧 Currently in Development</CardTitle>
            <CardDescription>
              We're building the future of content sharing and fan engagement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-6">
              HVPPY Central is being developed with cutting-edge technology including
              Next.js 15, Appwrite, AI integration, and modern UI components.
            </p>
            <div className="flex flex-wrap justify-center gap-2 mb-6">
              <Badge variant="outline">Next.js 15</Badge>
              <Badge variant="outline">Appwrite</Badge>
              <Badge variant="outline">Prisma</Badge>
              <Badge variant="outline">Shadcn/UI</Badge>
              <Badge variant="outline">AI Integration</Badge>
              <Badge variant="outline">TypeScript</Badge>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <div className="border-t pt-6">
                <h4 className="font-semibold mb-4">🛠️ Development Tools</h4>
                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                  <Button variant="outline" size="sm" asChild>
                    <a href="/api/seed" target="_blank">Seed Database</a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href="/feed/discover">Feed Demo</a>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
