"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  X,
  Search,
  TrendingUp,
  Users,
  MessageCircle,
  Send,
  MoreHorizontal,
  Music,
  Heart,
  Flame,
} from 'lucide-react'

interface RightSidebarProps {
  onClose: () => void
  isMobile: boolean
}

const trendingTopics = [
  { tag: '#ChillVibes', posts: 1234, growth: '+12%' },
  { tag: '#MoodMatch', posts: 856, growth: '+8%' },
  { tag: '#NewMusic', posts: 2341, growth: '+15%' },
  { tag: '#CreatorLab', posts: 567, growth: '+25%' },
  { tag: '#HappyMood', posts: 1890, growth: '+5%' },
]

const suggestedCreators = [
  {
    id: 1,
    name: '<PERSON>',
    username: '@melodic_maya',
    avatar: '/placeholder-avatar.jpg',
    followers: '12.5K',
    isVerified: true,
    latestTrack: 'Midnight Reflections',
  },
  {
    id: 2,
    name: 'Alex Rodriguez',
    username: '@beat_master_alex',
    avatar: '/placeholder-avatar.jpg',
    followers: '8.9K',
    isVerified: true,
    latestTrack: 'Studio Session Vibes',
  },
  {
    id: 3,
    name: 'Luna Park',
    username: '@luna_sounds',
    avatar: '/placeholder-avatar.jpg',
    followers: '15.2K',
    isVerified: false,
    latestTrack: 'Dreamy Nights',
  },
]

const liveChats = [
  {
    id: 1,
    name: 'Maya Chen',
    username: '@melodic_maya',
    avatar: '/placeholder-avatar.jpg',
    lastMessage: 'Thanks for the feedback on my new track! 🎵',
    time: '2m',
    unread: 2,
    isOnline: true,
  },
  {
    id: 2,
    name: 'Alex Rodriguez',
    username: '@beat_master_alex',
    avatar: '/placeholder-avatar.jpg',
    lastMessage: 'Want to collab on something?',
    time: '1h',
    unread: 0,
    isOnline: true,
  },
  {
    id: 3,
    name: 'Luna Park',
    username: '@luna_sounds',
    avatar: '/placeholder-avatar.jpg',
    lastMessage: 'Love your latest mood match playlist!',
    time: '3h',
    unread: 1,
    isOnline: false,
  },
]

export function RightSidebar({ onClose, isMobile }: RightSidebarProps) {
  const [activeChat, setActiveChat] = useState<number | null>(null)
  const [messageInput, setMessageInput] = useState('')

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (messageInput.trim()) {
      // Handle message sending logic here
      console.log('Sending message:', messageInput)
      setMessageInput('')
    }
  }

  return (
    <aside
      className={cn(
        "fixed right-0 top-16 h-[calc(100vh-4rem)] w-80 bg-background border-l z-30 transition-transform duration-300",
        isMobile && "w-full"
      )}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="font-semibold">Activity</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="trending" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
            <TabsTrigger value="trending">Trending</TabsTrigger>
            <TabsTrigger value="creators">Creators</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
          </TabsList>

          {/* Trending Tab */}
          <TabsContent value="trending" className="flex-1 mt-4">
            <ScrollArea className="h-full px-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Trending Topics
                  </h3>
                  <div className="space-y-3">
                    {trendingTopics.map((topic, index) => (
                      <div
                        key={topic.tag}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                      >
                        <div>
                          <p className="font-medium text-sm">{topic.tag}</p>
                          <p className="text-xs text-muted-foreground">
                            {topic.posts.toLocaleString()} posts
                          </p>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {topic.growth}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <Flame className="h-4 w-4 mr-2" />
                    Hot Right Now
                  </h3>
                  <div className="space-y-3">
                    <div className="p-3 rounded-lg bg-accent/50">
                      <div className="flex items-center mb-2">
                        <Music className="h-4 w-4 mr-2 text-hvppy-500" />
                        <span className="text-sm font-medium">Midnight Reflections</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        by Maya Chen • 2.1K reactions
                      </p>
                    </div>
                    <div className="p-3 rounded-lg bg-accent/50">
                      <div className="flex items-center mb-2">
                        <Heart className="h-4 w-4 mr-2 text-red-500" />
                        <span className="text-sm font-medium">#HeartbrokenVibes</span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Trending in mood-based content
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Creators Tab */}
          <TabsContent value="creators" className="flex-1 mt-4">
            <ScrollArea className="h-full px-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search creators..."
                    className="pl-10"
                  />
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Suggested for You
                  </h3>
                  <div className="space-y-3">
                    {suggestedCreators.map((creator) => (
                      <div
                        key={creator.id}
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                      >
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={creator.avatar} />
                          <AvatarFallback>{creator.name[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center">
                            <p className="text-sm font-medium truncate">{creator.name}</p>
                            {creator.isVerified && (
                              <Badge variant="secondary" className="ml-1 h-4 w-4 p-0">
                                ✓
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground truncate">
                            {creator.username} • {creator.followers} followers
                          </p>
                          <p className="text-xs text-hvppy-600 truncate">
                            Latest: {creator.latestTrack}
                          </p>
                        </div>
                        <Button size="sm" variant="outline">
                          Follow
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Messages Tab */}
          <TabsContent value="messages" className="flex-1 mt-4 flex flex-col">
            {activeChat ? (
              // Chat View
              <div className="flex-1 flex flex-col">
                <div className="flex items-center justify-between p-4 border-b">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveChat(null)}
                    >
                      ←
                    </Button>
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder-avatar.jpg" />
                      <AvatarFallback>MC</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">Maya Chen</p>
                      <p className="text-xs text-muted-foreground">Online</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>

                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    <div className="flex justify-start">
                      <div className="bg-accent rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">Hey! Thanks for listening to my new track 🎵</p>
                        <p className="text-xs text-muted-foreground mt-1">2:30 PM</p>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <div className="bg-hvppy-500 text-white rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">Love it! The mood matching is perfect 💜</p>
                        <p className="text-xs text-hvppy-100 mt-1">2:32 PM</p>
                      </div>
                    </div>
                  </div>
                </ScrollArea>

                <form onSubmit={handleSendMessage} className="p-4 border-t">
                  <div className="flex space-x-2">
                    <Input
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      placeholder="Type a message..."
                      className="flex-1"
                    />
                    <Button type="submit" size="sm" className="hvppy-gradient">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </div>
            ) : (
              // Chat List
              <ScrollArea className="flex-1 px-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Recent Chats
                  </h3>
                  {liveChats.map((chat) => (
                    <div
                      key={chat.id}
                      onClick={() => setActiveChat(chat.id)}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                    >
                      <div className="relative">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={chat.avatar} />
                          <AvatarFallback>{chat.name[0]}</AvatarFallback>
                        </Avatar>
                        {chat.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium truncate">{chat.name}</p>
                          <div className="flex items-center space-x-1">
                            <span className="text-xs text-muted-foreground">{chat.time}</span>
                            {chat.unread > 0 && (
                              <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                                {chat.unread}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground truncate">
                          {chat.lastMessage}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </aside>
  )
}
