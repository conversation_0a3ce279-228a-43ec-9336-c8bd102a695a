"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Home,
  Compass,
  Heart,
  TrendingUp,
  Flask,
  Users,
  MessageCircle,
  Settings,
  Studio,
  User,
  ChevronLeft,
  ChevronRight,
  Music,
  Video,
  Image as ImageIcon,
  Mic,
} from 'lucide-react'
import { FeedType } from '@/types/feed'

interface SidebarProps {
  collapsed: boolean
  onToggle: () => void
  isMobile: boolean
}

const feedNavItems = [
  {
    type: FeedType.DISCOVER,
    label: 'Discover',
    icon: Compass,
    href: '/feed/discover',
    description: 'Explore new content',
  },
  {
    type: FeedType.FOLLOWING,
    label: 'Following',
    icon: Users,
    href: '/feed/following',
    description: 'Content from creators you follow',
  },
  {
    type: FeedType.MOOD_BASED,
    label: 'Mood Match',
    icon: Heart,
    href: '/feed/mood',
    description: 'Content matching your vibe',
  },
  {
    type: FeedType.TRENDING,
    label: 'Trending',
    icon: TrendingUp,
    href: '/feed/trending',
    description: 'What\'s hot right now',
  },
  {
    type: FeedType.EXPERIMENTAL,
    label: 'Lab',
    icon: Flask,
    href: '/feed/experimental',
    description: 'Experimental content',
  },
]

const contentTypes = [
  { label: 'Music', icon: Music, href: '/content/music' },
  { label: 'Videos', icon: Video, href: '/content/videos' },
  { label: 'Images', icon: ImageIcon, href: '/content/images' },
  { label: 'Podcasts', icon: Mic, href: '/content/podcasts' },
]

const quickActions = [
  { label: 'Messages', icon: MessageCircle, href: '/messages', badge: 3 },
  { label: 'Profile', icon: User, href: '/profile' },
  { label: 'Creator Studio', icon: Studio, href: '/studio' },
  { label: 'Settings', icon: Settings, href: '/settings' },
]

export function Sidebar({ collapsed, onToggle, isMobile }: SidebarProps) {
  const pathname = usePathname()

  const SidebarItem = ({
    href,
    icon: Icon,
    label,
    description,
    badge,
    isActive = false,
  }: {
    href: string
    icon: React.ElementType
    label: string
    description?: string
    badge?: number
    isActive?: boolean
  }) => (
    <Link href={href}>
      <Button
        variant={isActive ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-start h-auto p-3 transition-all duration-200",
          collapsed ? "px-3" : "px-4",
          isActive && "bg-hvppy-100 dark:bg-hvppy-900 border-r-2 border-hvppy-500"
        )}
      >
        <Icon className={cn("h-5 w-5 flex-shrink-0", isActive && "text-hvppy-600")} />
        {!collapsed && (
          <div className="ml-3 flex-1 text-left">
            <div className="flex items-center justify-between">
              <span className="font-medium">{label}</span>
              {badge && badge > 0 && (
                <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                  {badge}
                </Badge>
              )}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
        )}
      </Button>
    </Link>
  )

  return (
    <>
      <aside
        className={cn(
          "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-background border-r transition-all duration-300 ease-in-out z-30",
          collapsed ? "w-16" : "w-64",
          isMobile && collapsed && "-translate-x-full",
          isMobile && !collapsed && "w-64"
        )}
      >
        <div className="flex flex-col h-full">
          {/* HVPPY Central Branding */}
          <div className="p-4 border-b">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-lg hvppy-gradient flex items-center justify-center">
                <span className="text-white font-bold text-sm">H</span>
              </div>
              {!collapsed && (
                <div className="ml-3">
                  <h2 className="font-bold text-lg hvppy-gradient bg-clip-text text-transparent">
                    HVPPY Central
                  </h2>
                  <p className="text-xs text-muted-foreground">Your creative hub</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto py-4 space-y-6">
            {/* Feed Navigation */}
            <div className="px-2">
              {!collapsed && (
                <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                  Feeds
                </h3>
              )}
              <div className="space-y-1">
                {feedNavItems.map((item) => (
                  <SidebarItem
                    key={item.type}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    description={collapsed ? undefined : item.description}
                    isActive={pathname.startsWith(item.href)}
                  />
                ))}
              </div>
            </div>

            <Separator />

            {/* Content Types */}
            <div className="px-2">
              {!collapsed && (
                <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                  Content
                </h3>
              )}
              <div className="space-y-1">
                {contentTypes.map((item) => (
                  <SidebarItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    isActive={pathname.startsWith(item.href)}
                  />
                ))}
              </div>
            </div>

            <Separator />

            {/* Quick Actions */}
            <div className="px-2">
              {!collapsed && (
                <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                  Quick Actions
                </h3>
              )}
              <div className="space-y-1">
                {quickActions.map((item) => (
                  <SidebarItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    badge={item.badge}
                    isActive={pathname.startsWith(item.href)}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* User Profile Section */}
          <div className="p-4 border-t">
            <div className="flex items-center">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-avatar.jpg" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              {!collapsed && (
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium">John Doe</p>
                  <p className="text-xs text-muted-foreground">@johndoe</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Collapse Toggle */}
        {!isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="absolute -right-3 top-6 h-6 w-6 rounded-full border bg-background shadow-md"
          >
            {collapsed ? (
              <ChevronRight className="h-3 w-3" />
            ) : (
              <ChevronLeft className="h-3 w-3" />
            )}
          </Button>
        )}
      </aside>
    </>
  )
}
