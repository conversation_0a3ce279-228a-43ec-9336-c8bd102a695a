"use client"

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { AudioPlayer } from '@/lib/player/types'
import { formatTime } from '@/lib/player/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  SkipBack, 
  SkipForward,
  Repeat,
  Shuffle,
  Settings,
  Heart,
  Share,
  Download,
  MoreHorizontal
} from 'lucide-react'

interface AudioPlayerControlsProps {
  player: AudioPlayer
  variant?: 'default' | 'compact' | 'minimal' | 'card'
  onSettingsClick?: () => void
  showSocialActions?: boolean
  className?: string
}

export function AudioPlayerControls({
  player,
  variant = 'default',
  onSettingsClick,
  showSocialActions = true,
  className
}: AudioPlayerControlsProps) {
  const [playerState, setPlayerState] = useState(player.getState())
  const [isDragging, setIsDragging] = useState(false)
  const [dragTime, setDragTime] = useState(0)
  const volumeTimeoutRef = useRef<NodeJS.Timeout>()
  const [isLiked, setIsLiked] = useState(false)
  const [isRepeating, setIsRepeating] = useState(false)
  const [isShuffling, setIsShuffling] = useState(false)

  // Update player state
  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    const handleTimeUpdate = (currentTime: number) => {
      // Only update if not dragging to prevent interference
      if (!isDragging) {
        setPlayerState(prevState => ({
          ...prevState,
          currentTime
        }))
      }
    }

    player.on('onStateChange', handleStateChange)
    player.on('onTimeUpdate', handleTimeUpdate)

    return () => {
      player.off('onStateChange', handleStateChange)
      player.off('onTimeUpdate', handleTimeUpdate)

      // Cleanup volume timeout
      if (volumeTimeoutRef.current) {
        clearTimeout(volumeTimeoutRef.current)
      }
    }
  }, [player, isDragging])

  // Handle play/pause
  const handlePlayPause = useCallback(() => {
    if (playerState.state === 'playing') {
      player.pause()
    } else {
      player.play().catch(console.error)
    }
  }, [player, playerState.state])

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    player.setMuted(!playerState.muted)
  }, [player, playerState.muted])

  // Handle volume change
  const handleVolumeChange = useCallback((value: number[]) => {
    const volume = value[0] / 100

    // Clear previous timeout to debounce
    if (volumeTimeoutRef.current) {
      clearTimeout(volumeTimeoutRef.current)
    }

    // Debounce volume changes to prevent infinite loops
    volumeTimeoutRef.current = setTimeout(() => {
      player.setVolume(volume)
      if (volume > 0 && playerState.muted) {
        player.setMuted(false)
      }
    }, 50)
  }, [player, playerState.muted])

  // Handle seek
  const handleSeekStart = useCallback(() => {
    if (!isDragging) {
      setIsDragging(true)
    }
  }, [isDragging])

  const handleSeekChange = useCallback((value: number[]) => {
    if (!isDragging) return // Only handle changes when actively dragging
    const time = (value[0] / 100) * playerState.duration
    setDragTime(time)
    // Only update drag time during dragging, don't seek immediately
  }, [playerState.duration, isDragging])

  const handleSeekEnd = useCallback(() => {
    if (isDragging) {
      player.seek(dragTime)
      setIsDragging(false)
    }
  }, [player, dragTime, isDragging])

  // Handle skip
  const handleSkipBackward = useCallback(() => {
    const newTime = Math.max(0, playerState.currentTime - 10)
    player.seek(newTime)
  }, [player, playerState.currentTime])

  const handleSkipForward = useCallback(() => {
    const newTime = Math.min(playerState.duration, playerState.currentTime + 10)
    player.seek(newTime)
  }, [player, playerState.duration, playerState.currentTime])

  // Social actions
  const handleLike = useCallback(() => {
    setIsLiked(!isLiked)
    // TODO: Integrate with HVPPY Central reaction system
  }, [isLiked])

  const handleShare = useCallback(() => {
    // TODO: Implement share functionality
    console.log('Share audio')
  }, [])

  const handleDownload = useCallback(() => {
    // TODO: Implement download functionality
    console.log('Download audio')
  }, [])

  // Calculate progress with memoization to prevent unnecessary re-renders
  const currentTime = isDragging ? dragTime : playerState.currentTime

  const progress = useMemo(() => {
    if (playerState.duration <= 0) return 0
    return Math.min(100, Math.max(0, (currentTime / playerState.duration) * 100))
  }, [currentTime, playerState.duration])

  const bufferedProgress = useMemo(() => {
    if (playerState.duration <= 0) return 0
    return Math.min(100, Math.max(0, (playerState.bufferedTime / playerState.duration) * 100))
  }, [playerState.bufferedTime, playerState.duration])

  // Stable slider value to prevent infinite re-renders
  const sliderValue = useMemo(() => [progress], [progress])

  // Render based on variant
  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePlayPause}
          className="text-white hover:bg-white/20 p-2"
        >
          {playerState.state === 'playing' ? (
            <Pause className="w-4 h-4" />
          ) : (
            <Play className="w-4 h-4" />
          )}
        </Button>
        
        <div className="flex-1 min-w-0">
          <Slider
            value={sliderValue}
            onValueChange={handleSeekChange}
            onValueCommit={handleSeekEnd}
            onPointerDown={handleSeekStart}
            max={100}
            step={0.1}
            className="w-full"
            trackClassName="bg-white/20"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
        </div>
        
        <span className="text-white text-xs font-mono">
          {formatTime(currentTime)}
        </span>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={cn('space-y-3', className)}>
        {/* Progress Bar */}
        <div className="relative">
          <div className="absolute inset-0 bg-white/20 rounded-full h-1">
            <div 
              className="bg-white/40 h-full rounded-full transition-all duration-300"
              style={{ width: `${bufferedProgress}%` }}
            />
          </div>
          <Slider
            value={sliderValue}
            onValueChange={handleSeekChange}
            onValueCommit={handleSeekEnd}
            onPointerDown={handleSeekStart}
            max={100}
            step={0.1}
            className="relative z-10"
            trackClassName="bg-transparent"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayPause}
              className="text-white hover:bg-white/20 p-2"
            >
              {playerState.state === 'playing' ? (
                <Pause className="w-4 h-4" />
              ) : (
                <Play className="w-4 h-4" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleVolumeToggle}
              className="text-white hover:bg-white/20 p-2"
            >
              {playerState.muted || playerState.volume === 0 ? (
                <VolumeX className="w-4 h-4" />
              ) : (
                <Volume2 className="w-4 h-4" />
              )}
            </Button>
          </div>

          <div className="text-white text-xs font-mono">
            {formatTime(currentTime)} / {formatTime(playerState.duration)}
          </div>
        </div>
      </div>
    )
  }

  // Default and card variants
  return (
    <div className={cn('space-y-4', className)}>
      {/* Progress Bar with Time */}
      <div className="space-y-2">
        <div className="relative">
          <div className="absolute inset-0 bg-white/20 rounded-full h-2">
            <div 
              className="bg-white/40 h-full rounded-full transition-all duration-300"
              style={{ width: `${bufferedProgress}%` }}
            />
          </div>
          <Slider
            value={sliderValue}
            onValueChange={handleSeekChange}
            onValueCommit={handleSeekEnd}
            onPointerDown={handleSeekStart}
            max={100}
            step={0.1}
            className="relative z-10"
            trackClassName="bg-transparent"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white shadow-lg"
          />
        </div>
        
        <div className="flex justify-between text-white/70 text-sm font-mono">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(playerState.duration)}</span>
        </div>
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsShuffling(!isShuffling)}
          className={cn(
            "text-white hover:bg-white/20 p-2",
            isShuffling && "text-purple-400"
          )}
        >
          <Shuffle className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleSkipBackward}
          className="text-white hover:bg-white/20 p-2"
        >
          <SkipBack className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="lg"
          onClick={handlePlayPause}
          className="text-white hover:bg-white/20 p-3 rounded-full bg-white/10"
        >
          {playerState.state === 'playing' ? (
            <Pause className="w-6 h-6" />
          ) : (
            <Play className="w-6 h-6" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleSkipForward}
          className="text-white hover:bg-white/20 p-2"
        >
          <SkipForward className="w-5 h-5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsRepeating(!isRepeating)}
          className={cn(
            "text-white hover:bg-white/20 p-2",
            isRepeating && "text-purple-400"
          )}
        >
          <Repeat className="w-4 h-4" />
        </Button>
      </div>

      {/* Volume and Additional Controls */}
      <div className="flex items-center justify-between">
        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleVolumeToggle}
            className="text-white hover:bg-white/20 p-2"
          >
            {playerState.muted || playerState.volume === 0 ? (
              <VolumeX className="w-4 h-4" />
            ) : (
              <Volume2 className="w-4 h-4" />
            )}
          </Button>

          <div className="w-24">
            <Slider
              value={[playerState.muted ? 0 : playerState.volume * 100]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
              className="w-full"
              trackClassName="bg-white/20"
              rangeClassName="bg-white"
              thumbClassName="bg-white border-2 border-gray-300"
            />
          </div>
        </div>

        {/* Social Actions */}
        {showSocialActions && (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={cn(
                "text-white hover:bg-white/20 p-2",
                isLiked && "text-red-400"
              )}
            >
              <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white hover:bg-white/20 p-2"
            >
              <Share className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              className="text-white hover:bg-white/20 p-2"
            >
              <Download className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onSettingsClick}
              className="text-white hover:bg-white/20 p-2"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
