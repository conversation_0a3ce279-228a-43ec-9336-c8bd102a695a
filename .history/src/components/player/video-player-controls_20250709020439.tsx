"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer } from '@/lib/player/types'
import { formatTime } from '@/lib/player/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Minimize,
  PictureInPicture,
  Settings,
  SkipBack,
  SkipForward
} from 'lucide-react'

interface VideoPlayerControlsProps {
  player: VideoPlayer
  visible: boolean
  onInteraction?: () => void
  className?: string
}

export function VideoPlayerControls({
  player,
  visible,
  onInteraction,
  className
}: VideoPlayerControlsProps) {
  const [playerState, setPlayerState] = useState(player.getState())
  const [isDragging, setIsDragging] = useState(false)
  const [dragTime, setDragTime] = useState(0)

  // Update player state
  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    player.on('onTimeUpdate', handleStateChange)

    return () => {
      player.off('onStateChange', handleStateChange)
      player.off('onTimeUpdate', handleStateChange)
    }
  }, [player])

  // Handle play/pause
  const handlePlayPause = useCallback(() => {
    if (playerState.state === 'playing') {
      player.pause()
    } else {
      player.play().catch(console.error)
    }
    onInteraction?.()
  }, [player, playerState.state, onInteraction])

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    player.setMuted(!playerState.muted)
    onInteraction?.()
  }, [player, playerState.muted, onInteraction])

  // Handle volume change
  const handleVolumeChange = useCallback((value: number[]) => {
    const volume = value[0] / 100
    // Debounce volume changes to prevent infinite loops
    const timeoutId = setTimeout(() => {
      player.setVolume(volume)
      if (volume > 0 && playerState.muted) {
        player.setMuted(false)
      }
    }, 10)

    onInteraction?.()

    return () => clearTimeout(timeoutId)
  }, [player, playerState.muted, onInteraction])

  // Handle seek
  const handleSeekStart = useCallback(() => {
    setIsDragging(true)
    onInteraction?.()
  }, [onInteraction])

  const handleSeekChange = useCallback((value: number[]) => {
    const time = (value[0] / 100) * playerState.duration
    setDragTime(time)
    // Only update drag time during dragging, don't seek immediately
  }, [playerState.duration])

  const handleSeekEnd = useCallback(() => {
    if (isDragging) {
      player.seek(dragTime)
      setIsDragging(false)
    }
  }, [player, dragTime, isDragging])

  // Handle skip
  const handleSkipBackward = useCallback(() => {
    const newTime = Math.max(0, playerState.currentTime - 10)
    player.seek(newTime)
    onInteraction?.()
  }, [player, playerState.currentTime, onInteraction])

  const handleSkipForward = useCallback(() => {
    const newTime = Math.min(playerState.duration, playerState.currentTime + 10)
    player.seek(newTime)
    onInteraction?.()
  }, [player, playerState.duration, playerState.currentTime, onInteraction])

  // Handle fullscreen
  const handleFullscreen = useCallback(() => {
    if (playerState.isFullscreen) {
      player.exitFullscreen().catch(console.error)
    } else {
      player.enterFullscreen().catch(console.error)
    }
    onInteraction?.()
  }, [player, playerState.isFullscreen, onInteraction])

  // Handle Picture-in-Picture
  const handlePiP = useCallback(() => {
    if (playerState.isPiP) {
      player.exitPiP().catch(console.error)
    } else {
      player.enterPiP().catch(console.error)
    }
    onInteraction?.()
  }, [player, playerState.isPiP, onInteraction])

  // Calculate progress
  const currentTime = isDragging ? dragTime : playerState.currentTime
  const progress = playerState.duration > 0 ? (currentTime / playerState.duration) * 100 : 0
  const bufferedProgress = playerState.duration > 0 ? (playerState.bufferedTime / playerState.duration) * 100 : 0

  return (
    <div
      className={cn(
        'absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4 transition-all duration-300',
        visible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2 pointer-events-none',
        className
      )}
    >
      {/* Progress Bar */}
      <div className="mb-4">
        <div className="relative">
          {/* Buffered Progress */}
          <div className="absolute inset-0 bg-white/20 rounded-full h-1">
            <div 
              className="bg-white/40 h-full rounded-full transition-all duration-300"
              style={{ width: `${bufferedProgress}%` }}
            />
          </div>
          
          {/* Seek Slider */}
          <Slider
            value={[progress]}
            onValueChange={handleSeekChange}
            onValueCommit={handleSeekEnd}
            onPointerDown={handleSeekStart}
            max={100}
            step={0.1}
            className="relative z-10"
            trackClassName="bg-transparent"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white shadow-lg"
          />
        </div>
      </div>

      {/* Controls Row */}
      <div className="flex items-center justify-between">
        {/* Left Controls */}
        <div className="flex items-center space-x-2">
          {/* Play/Pause */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePlayPause}
            className="text-white hover:bg-white/20 p-2"
          >
            {playerState.state === 'playing' ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </Button>

          {/* Skip Controls */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkipBackward}
            className="text-white hover:bg-white/20 p-2"
          >
            <SkipBack className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleSkipForward}
            className="text-white hover:bg-white/20 p-2"
          >
            <SkipForward className="w-4 h-4" />
          </Button>

          {/* Volume Controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVolumeToggle}
              className="text-white hover:bg-white/20 p-2"
            >
              {playerState.muted || playerState.volume === 0 ? (
                <VolumeX className="w-4 h-4" />
              ) : (
                <Volume2 className="w-4 h-4" />
              )}
            </Button>

            <div className="w-20">
              <Slider
                value={[playerState.muted ? 0 : playerState.volume * 100]}
                onValueChange={handleVolumeChange}
                max={100}
                step={1}
                className="w-full"
                trackClassName="bg-white/20"
                rangeClassName="bg-white"
                thumbClassName="bg-white border-2 border-gray-300"
              />
            </div>
          </div>

          {/* Time Display */}
          <div className="text-white text-sm font-mono">
            {formatTime(currentTime)} / {formatTime(playerState.duration)}
          </div>
        </div>

        {/* Right Controls */}
        <div className="flex items-center space-x-2">
          {/* Picture-in-Picture */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePiP}
            className="text-white hover:bg-white/20 p-2"
            disabled={!('pictureInPictureEnabled' in document)}
          >
            <PictureInPicture className="w-4 h-4" />
          </Button>

          {/* Settings */}
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20 p-2"
          >
            <Settings className="w-4 h-4" />
          </Button>

          {/* Fullscreen */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFullscreen}
            className="text-white hover:bg-white/20 p-2"
          >
            {playerState.isFullscreen ? (
              <Minimize className="w-4 h-4" />
            ) : (
              <Maximize className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Loading Indicator */}
      {playerState.state === 'buffering' && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

// Minimal controls for compact players
export function MinimalVideoControls({
  player,
  visible,
  onInteraction,
  className
}: VideoPlayerControlsProps) {
  const [playerState, setPlayerState] = useState(player.getState())

  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    return () => player.off('onStateChange', handleStateChange)
  }, [player])

  const handlePlayPause = useCallback(() => {
    if (playerState.state === 'playing') {
      player.pause()
    } else {
      player.play().catch(console.error)
    }
    onInteraction?.()
  }, [player, playerState.state, onInteraction])

  return (
    <div
      className={cn(
        'absolute inset-0 flex items-center justify-center transition-opacity duration-300',
        visible ? 'opacity-100' : 'opacity-0 pointer-events-none',
        className
      )}
    >
      <Button
        variant="ghost"
        size="lg"
        onClick={handlePlayPause}
        className="text-white hover:bg-white/20 p-4 rounded-full bg-black/50"
      >
        {playerState.state === 'playing' ? (
          <Pause className="w-8 h-8" />
        ) : (
          <Play className="w-8 h-8" />
        )}
      </Button>
    </div>
  )
}
