import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})
import { MoodType } from '@/types'

export interface ContentAnalysis {
  moods: MoodType[]
  sentiment: 'positive' | 'negative' | 'neutral'
  themes: string[]
  suggestedTags: string[]
  emotionalIntensity: number // 0-1 scale
}

export interface MoodMatchResult {
  score: number // 0-1 relevance score
  explanation: string
  suggestedContent: string[]
}

export class AIService {
  // Analyze content for mood and emotional metadata
  static async analyzeContent(content: string, contentType: string): Promise<ContentAnalysis> {
    try {
      const prompt = `
        Analyze the following ${contentType} content for emotional and thematic elements:
        
        Content: "${content}"
        
        Please provide:
        1. Primary moods (from: happy, chill, heartbroken, inspired, energetic, peaceful, nostalgic, excited)
        2. Overall sentiment (positive/negative/neutral)
        3. Main themes
        4. Suggested hashtags/tags
        5. Emotional intensity (0-1 scale)
        
        Respond in JSON format.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      })

      const analysis = JSON.parse(response.choices[0].message.content || '{}')
      
      return {
        moods: analysis.moods || [],
        sentiment: analysis.sentiment || 'neutral',
        themes: analysis.themes || [],
        suggestedTags: analysis.suggestedTags || [],
        emotionalIntensity: analysis.emotionalIntensity || 0.5,
      }
    } catch (error) {
      console.error('Error analyzing content:', error)
      // Return default analysis on error
      return {
        moods: [],
        sentiment: 'neutral',
        themes: [],
        suggestedTags: [],
        emotionalIntensity: 0.5,
      }
    }
  }

  // Generate mood-based content recommendations
  static async getMoodMatch(userMood: MoodType, availableContent: any[]): Promise<MoodMatchResult[]> {
    try {
      const prompt = `
        A user is feeling "${userMood}". 
        
        Available content:
        ${availableContent.map(content => `
          - ${content.title}: ${content.content} (moods: ${content.moods.join(', ')})
        `).join('\n')}
        
        Rank and explain which content best matches the user's mood. 
        Provide a relevance score (0-1) and explanation for each piece of content.
        
        Respond in JSON format with an array of results.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.4,
      })

      const results = JSON.parse(response.choices[0].message.content || '[]')
      return results
    } catch (error) {
      console.error('Error getting mood match:', error)
      return []
    }
  }

  // Generate content suggestions for creators
  static async generateContentSuggestions(
    creatorGenre: string[], 
    recentMoods: MoodType[], 
    fanEngagement: any
  ): Promise<string[]> {
    try {
      const prompt = `
        Generate content ideas for a creator with these characteristics:
        - Genres: ${creatorGenre.join(', ')}
        - Recent fan mood preferences: ${recentMoods.join(', ')}
        - Fan engagement data: ${JSON.stringify(fanEngagement)}
        
        Suggest 5 creative content ideas that would resonate with their audience.
        Focus on emotional connection and innovative formats.
        
        Return as a JSON array of strings.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
      })

      const suggestions = JSON.parse(response.choices[0].message.content || '[]')
      return suggestions
    } catch (error) {
      console.error('Error generating content suggestions:', error)
      return []
    }
  }

  // Generate captions with mood-aware language
  static async generateCaption(
    content: string, 
    targetMood: MoodType, 
    platform: string = 'hvppy'
  ): Promise<string> {
    try {
      const prompt = `
        Create an engaging caption for this content:
        "${content}"
        
        Target mood: ${targetMood}
        Platform: ${platform}
        
        The caption should:
        - Match the ${targetMood} mood
        - Be engaging and authentic
        - Include relevant hashtags
        - Encourage fan interaction
        
        Keep it concise but impactful.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.6,
      })

      return response.choices[0].message.content || ''
    } catch (error) {
      console.error('Error generating caption:', error)
      return ''
    }
  }

  // Analyze fan feedback and sentiment
  static async analyzeFanFeedback(comments: string[]): Promise<{
    overallSentiment: 'positive' | 'negative' | 'neutral'
    commonThemes: string[]
    suggestedResponses: string[]
    moodDistribution: Record<MoodType, number>
  }> {
    try {
      const prompt = `
        Analyze this fan feedback:
        ${comments.map((comment, i) => `${i + 1}. ${comment}`).join('\n')}
        
        Provide:
        1. Overall sentiment
        2. Common themes/topics
        3. Suggested creator responses
        4. Mood distribution (what moods are fans expressing)
        
        Respond in JSON format.
      `

      const response = await openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      })

      const analysis = JSON.parse(response.choices[0].message.content || '{}')
      return analysis
    } catch (error) {
      console.error('Error analyzing fan feedback:', error)
      return {
        overallSentiment: 'neutral',
        commonThemes: [],
        suggestedResponses: [],
        moodDistribution: {} as Record<MoodType, number>,
      }
    }
  }
}
