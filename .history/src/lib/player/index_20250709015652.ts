// HVPPY Central Media Player System - Main Export File

// Core types and interfaces
export * from './types'

// Base player implementation
export { BaseMediaPlayer } from './base-player'

// Specific player implementations
export { 
  HVPPYVideoPlayer, 
  createVideoPlayer, 
  isVideoSupported, 
  getSupportedVideoFormats,
  getOptimalVideoQuality 
} from './video-player'

export { 
  HVPPYAudioPlayer, 
  createAudioPlayer, 
  isAudioSupported, 
  getSupportedAudioFormats,
  isWebAudioSupported 
} from './audio-player'

// Utilities
export * from './utils'

// Player factory function
import { MediaType, MediaSource, PlayerConfig, VideoPlayer, AudioPlayer } from './types'
import { createVideoPlayer } from './video-player'
import { createAudioPlayer } from './audio-player'
import { detectMediaType } from './utils'

/**
 * Factory function to create the appropriate player based on media type
 */
export function createPlayer(
  element: HTMLMediaElement,
  source?: MediaSource,
  config: Partial<PlayerConfig> = {}
): VideoPlayer | AudioPlayer {
  let mediaType: MediaType
  
  if (source) {
    mediaType = source.type
  } else if (element instanceof HTMLVideoElement) {
    mediaType = MediaType.VIDEO
  } else if (element instanceof HTMLAudioElement) {
    mediaType = MediaType.AUDIO
  } else {
    throw new Error('Unable to determine media type')
  }
  
  switch (mediaType) {
    case MediaType.VIDEO:
    case MediaType.LIVE_STREAM:
      if (!(element instanceof HTMLVideoElement)) {
        throw new Error('Video media type requires HTMLVideoElement')
      }
      return createVideoPlayer(element, config)
      
    case MediaType.AUDIO:
    case MediaType.PODCAST:
      if (!(element instanceof HTMLAudioElement)) {
        throw new Error('Audio media type requires HTMLAudioElement')
      }
      return createAudioPlayer(element, config)
      
    default:
      throw new Error(`Unsupported media type: ${mediaType}`)
  }
}

/**
 * Auto-detect and create player from URL
 */
export function createPlayerFromUrl(
  url: string,
  container: HTMLElement,
  config: Partial<PlayerConfig> = {}
): VideoPlayer | AudioPlayer {
  const mediaType = detectMediaType(url)
  let element: HTMLMediaElement
  
  if (mediaType === MediaType.VIDEO) {
    element = document.createElement('video')
    container.appendChild(element)
    return createVideoPlayer(element as HTMLVideoElement, config)
  } else {
    element = document.createElement('audio')
    container.appendChild(element)
    return createAudioPlayer(element as HTMLAudioElement, config)
  }
}

/**
 * Player manager for handling multiple players
 */
export class PlayerManager {
  private players: Map<string, VideoPlayer | AudioPlayer> = new Map()
  private activePlayer?: string

  addPlayer(id: string, player: VideoPlayer | AudioPlayer): void {
    this.players.set(id, player)
  }

  removePlayer(id: string): void {
    const player = this.players.get(id)
    if (player) {
      player.destroy()
      this.players.delete(id)
      
      if (this.activePlayer === id) {
        this.activePlayer = undefined
      }
    }
  }

  getPlayer(id: string): VideoPlayer | AudioPlayer | undefined {
    return this.players.get(id)
  }

  setActivePlayer(id: string): void {
    if (this.players.has(id)) {
      // Pause all other players
      this.players.forEach((player, playerId) => {
        if (playerId !== id) {
          player.pause()
        }
      })
      
      this.activePlayer = id
    }
  }

  getActivePlayer(): VideoPlayer | AudioPlayer | undefined {
    return this.activePlayer ? this.players.get(this.activePlayer) : undefined
  }

  pauseAll(): void {
    this.players.forEach(player => player.pause())
  }

  destroyAll(): void {
    this.players.forEach(player => player.destroy())
    this.players.clear()
    this.activePlayer = undefined
  }

  getPlayerCount(): number {
    return this.players.size
  }

  getAllPlayers(): (VideoPlayer | AudioPlayer)[] {
    return Array.from(this.players.values())
  }
}

// Global player manager instance
export const globalPlayerManager = new PlayerManager()

/**
 * Convenience functions for common operations
 */
export const PlayerUtils = {
  /**
   * Create a video player with common HVPPY Central settings
   */
  createHVPPYVideoPlayer(
    element: HTMLVideoElement,
    config: Partial<PlayerConfig> = {}
  ): VideoPlayer {
    const defaultConfig: Partial<PlayerConfig> = {
      autoPlay: false,
      muted: true, // Start muted for better UX
      controls: false, // Use custom controls
      keyboard: true,
      fullscreen: true,
      pip: true,
      preload: 'metadata',
      ...config
    }
    
    return createVideoPlayer(element, defaultConfig)
  },

  /**
   * Create an audio player with common HVPPY Central settings
   */
  createHVPPYAudioPlayer(
    element: HTMLAudioElement,
    config: Partial<PlayerConfig> = {}
  ): AudioPlayer {
    const defaultConfig: Partial<PlayerConfig> = {
      autoPlay: false,
      muted: false,
      controls: false, // Use custom controls
      keyboard: true,
      preload: 'metadata',
      ...config
    }
    
    return createAudioPlayer(element, defaultConfig)
  },

  /**
   * Setup player with HVPPY content
   */
  async setupWithHVPPYContent(
    player: VideoPlayer | AudioPlayer,
    contentId: string
  ): Promise<void> {
    try {
      // This would integrate with the HVPPY content classification system
      const response = await fetch(`/api/content/${contentId}`)
      const content = await response.json()
      
      if (content.source) {
        await player.load(content.source)
      }
    } catch (error) {
      console.error('Failed to setup player with HVPPY content:', error)
      throw error
    }
  },

  /**
   * Apply mood-based player settings
   */
  applyMoodSettings(
    player: VideoPlayer | AudioPlayer,
    mood: string
  ): void {
    // This would apply different settings based on mood
    // For example, different equalizer presets for audio players
    if (player instanceof HVPPYAudioPlayer) {
      switch (mood) {
        case 'energetic':
          player.setEqualizerPreset('rock')
          break
        case 'peaceful':
          player.setEqualizerPreset('classical')
          break
        case 'confident':
          player.setEqualizerPreset('hip_hop')
          break
        default:
          player.setEqualizerPreset('flat')
      }
    }
  }
}

// Note: HVPPYVideoPlayer and HVPPYAudioPlayer are already exported above

// Version information
export const PLAYER_VERSION = '1.0.0'
export const PLAYER_NAME = 'HVPPY Central Media Player'

/**
 * Initialize the player system
 */
export function initializePlayerSystem(): void {
  console.log(`${PLAYER_NAME} v${PLAYER_VERSION} initialized`)
  
  // Setup global error handling
  window.addEventListener('error', (event) => {
    if (event.target instanceof HTMLMediaElement) {
      console.error('Media element error:', event.target.error)
    }
  })
  
  // Setup visibility change handling to pause players when tab is hidden
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      globalPlayerManager.pauseAll()
    }
  })
}

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  initializePlayerSystem()
}
