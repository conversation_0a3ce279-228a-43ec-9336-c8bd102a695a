// Core player types and interfaces for HVPPY Central Media Player System

export enum MediaType {
  VIDEO = 'video',
  AUDIO = 'audio',
  LIVE_STREAM = 'live_stream',
  PODCAST = 'podcast'
}

export enum PlaybackState {
  IDLE = 'idle',
  LOADING = 'loading',
  READY = 'ready',
  PLAYING = 'playing',
  PAUSED = 'paused',
  BUFFERING = 'buffering',
  ENDED = 'ended',
  ERROR = 'error'
}

export enum PlaybackQuality {
  AUTO = 'auto',
  LOW = '360p',
  MEDIUM = '480p',
  HIGH = '720p',
  HD = '1080p',
  UHD = '4K'
}

export enum AudioQuality {
  LOW = '128kbps',
  MEDIUM = '256kbps',
  HIGH = '320kbps',
  LOSSLESS = 'lossless'
}

export interface MediaSource {
  id: string
  url: string
  type: MediaType
  quality?: PlaybackQuality | AudioQuality
  format: string
  size?: number
  duration?: number
  bitrate?: number
  codec?: string
}

export interface MediaMetadata {
  id: string
  title: string
  artist?: string
  album?: string
  genre?: string
  year?: number
  duration: number
  thumbnail?: string
  artwork?: string
  description?: string
  tags?: string[]
  moods?: string[]
  lyrics?: string
  chapters?: MediaChapter[]
}

export interface MediaChapter {
  id: string
  title: string
  startTime: number
  endTime: number
  thumbnail?: string
}

export interface PlayerConfig {
  autoPlay: boolean
  loop: boolean
  muted: boolean
  volume: number
  playbackRate: number
  quality: PlaybackQuality | AudioQuality
  preload: 'none' | 'metadata' | 'auto'
  crossOrigin?: 'anonymous' | 'use-credentials'
  controls: boolean
  keyboard: boolean
  fullscreen: boolean
  pip: boolean // Picture-in-Picture
  airplay: boolean
  chromecast: boolean
}

export interface PlayerState {
  mediaId?: string
  state: PlaybackState
  currentTime: number
  duration: number
  bufferedTime: number
  volume: number
  muted: boolean
  playbackRate: number
  quality: PlaybackQuality | AudioQuality
  isFullscreen: boolean
  isPiP: boolean
  isLive: boolean
  error?: PlayerError
}

export interface PlayerError {
  code: number
  message: string
  details?: any
}

export interface PlayerEvents {
  onStateChange: (state: PlayerState) => void
  onTimeUpdate: (currentTime: number) => void
  onDurationChange: (duration: number) => void
  onProgress: (buffered: number) => void
  onVolumeChange: (volume: number, muted: boolean) => void
  onQualityChange: (quality: PlaybackQuality | AudioQuality) => void
  onError: (error: PlayerError) => void
  onEnded: () => void
  onFullscreenChange: (isFullscreen: boolean) => void
  onPiPChange: (isPiP: boolean) => void
}

export interface BasePlayer {
  // Core playback methods
  load(source: MediaSource): Promise<void>
  play(): Promise<void>
  pause(): void
  stop(): void
  seek(time: number): void
  
  // Volume and audio
  setVolume(volume: number): void
  setMuted(muted: boolean): void
  
  // Playback control
  setPlaybackRate(rate: number): void
  setQuality(quality: PlaybackQuality | AudioQuality): void
  
  // State getters
  getState(): PlayerState
  getCurrentTime(): number
  getDuration(): number
  getVolume(): number
  isMuted(): boolean
  
  // Event handling
  on<K extends keyof PlayerEvents>(event: K, handler: PlayerEvents[K]): void
  off<K extends keyof PlayerEvents>(event: K, handler: PlayerEvents[K]): void
  
  // Cleanup
  destroy(): void
}

export interface VideoPlayer extends BasePlayer {
  // Video-specific methods
  enterFullscreen(): Promise<void>
  exitFullscreen(): Promise<void>
  enterPiP(): Promise<void>
  exitPiP(): Promise<void>
  
  // Video properties
  getVideoElement(): HTMLVideoElement
  setSize(width: number, height: number): void
  getAspectRatio(): number
  
  // Subtitles and captions
  getTextTracks(): TextTrack[]
  setActiveTextTrack(track: TextTrack | null): void
}

export interface AudioPlayer extends BasePlayer {
  // Audio-specific methods
  getAudioElement(): HTMLAudioElement
  
  // Audio analysis
  getFrequencyData(): Uint8Array
  getWaveformData(): Uint8Array
  
  // Equalizer
  setEqualizer(bands: number[]): void
  getEqualizer(): number[]
}

export interface PlaylistItem {
  id: string
  mediaId: string
  title: string
  artist?: string
  duration: number
  thumbnail?: string
  source: MediaSource
}

export interface Playlist {
  id: string
  name: string
  description?: string
  items: PlaylistItem[]
  currentIndex: number
  shuffle: boolean
  repeat: 'none' | 'one' | 'all'
}

export interface PlayerTheme {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  shadow: string
}

export interface VisualizerConfig {
  type: 'bars' | 'wave' | 'circle' | 'spectrum'
  color: string
  gradient?: string[]
  sensitivity: number
  smoothing: number
  barCount?: number
  barWidth?: number
  barGap?: number
}

// Hook return types
export interface UsePlayerReturn {
  player: BasePlayer | null
  state: PlayerState
  load: (source: MediaSource) => Promise<void>
  play: () => Promise<void>
  pause: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  setMuted: (muted: boolean) => void
  setPlaybackRate: (rate: number) => void
}

export interface UseVideoPlayerReturn extends UsePlayerReturn {
  player: VideoPlayer | null
  enterFullscreen: () => Promise<void>
  exitFullscreen: () => Promise<void>
  enterPiP: () => Promise<void>
  exitPiP: () => Promise<void>
}

export interface UseAudioPlayerReturn extends UsePlayerReturn {
  player: AudioPlayer | null
  frequencyData: Uint8Array | null
  waveformData: Uint8Array | null
  setEqualizer: (bands: number[]) => void
}

export interface UsePlaylistReturn {
  playlist: Playlist | null
  currentItem: PlaylistItem | null
  isPlaying: boolean
  canGoNext: boolean
  canGoPrevious: boolean
  next: () => void
  previous: () => void
  goToIndex: (index: number) => void
  shuffle: () => void
  setRepeat: (mode: 'none' | 'one' | 'all') => void
  addItem: (item: PlaylistItem) => void
  removeItem: (id: string) => void
  reorderItems: (fromIndex: number, toIndex: number) => void
}
