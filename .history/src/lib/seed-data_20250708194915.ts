import { prisma } from './prisma'
import { UserRole, ContentType, ReactionType } from '@/types'

export async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...')

    // Create test users
    const users = await Promise.all([
      prisma.user.create({
        data: {
          appwriteId: 'user1',
          email: '<EMAIL>',
          username: 'melodic_maya',
          displayName: '<PERSON>',
          bio: 'Singer-songwriter creating vibes for every mood 🎵',
          role: UserRole.CREATOR,
          isVerified: true,
        },
      }),
      prisma.user.create({
        data: {
          appwriteId: 'user2',
          email: '<EMAIL>',
          username: 'beat_master_alex',
          displayName: '<PERSON>',
          bio: 'Producer & DJ spreading good energy ⚡',
          role: UserRole.CREATOR,
          isVerified: true,
        },
      }),
      prisma.user.create({
        data: {
          appwriteId: 'user3',
          email: '<EMAIL>',
          username: 'music_lover_sam',
          displayName: '<PERSON>',
          bio: 'Music enthusiast and vibe curator 🎧',
          role: UserRole.FAN,
        },
      }),
    ])

    console.log('✅ Created users')

    // Create creators
    const creators = await Promise.all([
      prisma.creator.create({
        data: {
          userId: users[0].id,
          stageName: 'Maya',
          genre: ['Pop', 'Indie', 'Electronic'],
          location: 'Los Angeles, CA',
          website: 'https://maya-music.com',
          socialLinks: {
            instagram: '@melodic_maya',
            tiktok: '@maya_vibes',
            spotify: 'maya-chen-music',
          },
        },
      }),
      prisma.creator.create({
        data: {
          userId: users[1].id,
          stageName: 'BeatMaster Alex',
          genre: ['Electronic', 'Hip-Hop', 'House'],
          location: 'Miami, FL',
          website: 'https://beatmaster-alex.com',
          socialLinks: {
            soundcloud: 'beatmaster-alex',
            instagram: '@beat_master_alex',
            youtube: 'BeatMasterAlexOfficial',
          },
        },
      }),
    ])

    console.log('✅ Created creators')

    // Create personas
    const personas = await Promise.all([
      prisma.persona.create({
        data: {
          creatorId: creators[0].id,
          name: 'Maya the Dreamer',
          description: 'Soft melodies and introspective lyrics',
          mood: ['peaceful', 'nostalgic', 'inspired'],
          isActive: true,
        },
      }),
      prisma.persona.create({
        data: {
          creatorId: creators[0].id,
          name: 'Maya the Energizer',
          description: 'Upbeat tracks that make you dance',
          mood: ['happy', 'energetic', 'excited'],
          isActive: true,
        },
      }),
      prisma.persona.create({
        data: {
          creatorId: creators[1].id,
          name: 'Alex the Producer',
          description: 'Behind-the-scenes studio content',
          mood: ['inspired', 'energetic'],
          isActive: true,
        },
      }),
    ])

    console.log('✅ Created personas')

    // Create sample posts
    const posts = await Promise.all([
      // Maya's posts
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[0].id,
          title: 'Midnight Reflections',
          content: 'Sometimes the best songs come from the quiet moments. Here\'s a snippet of something I\'ve been working on during those late-night studio sessions. 🌙✨',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/midnight-reflections.mp3'],
          moods: ['peaceful', 'nostalgic', 'inspired'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          viewCount: 1250,
          likeCount: 89,
          shareCount: 12,
        },
      }),
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[1].id,
          title: 'Dance Floor Energy',
          content: 'When the beat drops and the crowd goes wild! This track is all about bringing that festival energy to your headphones 🎉🔥',
          contentType: ContentType.VIDEO,
          mediaUrls: ['https://example.com/video/dance-floor-energy.mp4'],
          moods: ['happy', 'energetic', 'excited'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
          viewCount: 2100,
          likeCount: 156,
          shareCount: 28,
        },
      }),
      // Alex's posts
      prisma.post.create({
        data: {
          userId: users[1].id,
          creatorId: creators[1].id,
          personaId: personas[2].id,
          title: 'Studio Session Vibes',
          content: 'Behind the scenes of creating the perfect beat. Watch how I layer different sounds to create that signature BeatMaster sound 🎛️',
          contentType: ContentType.VIDEO,
          mediaUrls: ['https://example.com/video/studio-session.mp4'],
          moods: ['inspired', 'energetic'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
          viewCount: 890,
          likeCount: 67,
          shareCount: 15,
        },
      }),
      prisma.post.create({
        data: {
          userId: users[1].id,
          creatorId: creators[1].id,
          title: 'Chill Sunday Mix',
          content: 'Sometimes you need to slow it down. Here\'s a chill mix perfect for lazy Sunday afternoons ☀️🎵',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/chill-sunday-mix.mp3'],
          moods: ['chill', 'peaceful'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
          viewCount: 1680,
          likeCount: 124,
          shareCount: 31,
        },
      }),
      // Experimental content
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          title: 'AI Collaboration Experiment',
          content: 'What happens when human creativity meets AI? I\'m experimenting with AI-generated harmonies. Should I release the full version? 🤖🎵',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/ai-collab-experiment.mp3'],
          moods: ['inspired', 'excited'],
          isPublic: true,
          isExperimental: true,
          publishedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          viewCount: 456,
          likeCount: 34,
          shareCount: 8,
        },
      }),
      // Heartbroken mood content
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[0].id,
          title: 'When Words Aren\'t Enough',
          content: 'Sometimes music is the only language that can express what we feel. This one\'s for everyone going through a tough time. You\'re not alone. 💙',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/when-words-arent-enough.mp3'],
          moods: ['heartbroken', 'peaceful'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
          viewCount: 3200,
          likeCount: 287,
          shareCount: 45,
        },
      }),
    ])

    console.log('✅ Created posts')

    // Create some reactions
    await Promise.all([
      prisma.reaction.create({
        data: {
          userId: users[2].id,
          postId: posts[0].id,
          type: ReactionType.LOVE,
          mood: 'peaceful',
        },
      }),
      prisma.reaction.create({
        data: {
          userId: users[2].id,
          postId: posts[1].id,
          type: ReactionType.FIRE,
          mood: 'energetic',
        },
      }),
    ])

    // Create some memories
    await Promise.all([
      prisma.memory.create({
        data: {
          userId: users[2].id,
          postId: posts[0].id,
          title: 'Perfect late-night vibe',
          description: 'This song perfectly captures how I feel during those quiet moments',
        },
      }),
      prisma.memory.create({
        data: {
          userId: users[2].id,
          postId: posts[5].id,
          title: 'Helped me through a tough time',
          description: 'Maya\'s music always knows how to heal',
        },
      }),
    ])

    // Create follow relationships
    await prisma.follow.create({
      data: {
        followerId: users[2].id,
        followingId: users[0].id,
      },
    })

    await prisma.follow.create({
      data: {
        followerId: users[2].id,
        followingId: users[1].id,
      },
    })

    console.log('✅ Created interactions and relationships')
    console.log('🎉 Database seeding completed successfully!')

    return {
      users,
      creators,
      personas,
      posts,
    }
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}
