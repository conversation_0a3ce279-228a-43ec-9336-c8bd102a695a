import { prisma } from './prisma'
import { UserRole, ContentType, ReactionType } from '@/types'

// Stock media URLs from Unsplash and other free sources
const STOCK_AVATARS = [
  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop&crop=face',
]

const STOCK_MUSIC_COVERS = [
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=800&h=800&fit=crop',
]

const STOCK_VIDEO_THUMBNAILS = [
  'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&h=600&fit=crop',
]

const REALISTIC_USERS = [
  {
    appwriteId: 'user_maya_chen',
    email: '<EMAIL>',
    username: 'melodic_maya',
    displayName: 'Maya Chen',
    bio: '🎵 Singer-songwriter crafting melodies for every emotion | New album "Midnight Dreams" out now ✨',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[0],
  },
  {
    appwriteId: 'user_alex_beats',
    email: '<EMAIL>',
    username: 'beat_master_alex',
    displayName: 'Alex Rodriguez',
    bio: '🎛️ Producer & DJ | Creating beats that move souls | Miami vibes worldwide 🌴',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[1],
  },
  {
    appwriteId: 'user_luna_sounds',
    email: '<EMAIL>',
    username: 'luna_sounds',
    displayName: 'Luna Park',
    bio: '🌙 Dreamy soundscapes & ethereal vocals | Indie folk with electronic touches',
    role: UserRole.CREATOR,
    isVerified: false,
    avatar: STOCK_AVATARS[2],
  },
  {
    appwriteId: 'user_rhythm_king',
    email: '<EMAIL>',
    username: 'rhythm_king_jay',
    displayName: 'Jay Williams',
    bio: '🥁 Drummer & rhythm producer | Hip-hop meets jazz fusion | Teaching beats online',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[3],
  },
  {
    appwriteId: 'user_indie_rose',
    email: '<EMAIL>',
    username: 'indie_rose',
    displayName: 'Rose Martinez',
    bio: '🌹 Indie rock guitarist | Raw emotions through power chords | Coffee shop sessions',
    role: UserRole.CREATOR,
    isVerified: false,
    avatar: STOCK_AVATARS[4],
  },
  {
    appwriteId: 'user_sam_fan',
    email: '<EMAIL>',
    username: 'music_lover_sam',
    displayName: 'Sam Johnson',
    bio: '🎧 Music enthusiast & vibe curator | Discovering hidden gems daily | Playlist master',
    role: UserRole.FAN,
    isVerified: false,
    avatar: STOCK_AVATARS[5],
  },
  {
    appwriteId: 'user_melody_hunter',
    email: '<EMAIL>',
    username: 'melody_hunter',
    displayName: 'Emma Thompson',
    bio: '🎼 Classical pianist exploring modern sounds | Conservatory trained, heart untamed',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[6],
  },
  {
    appwriteId: 'user_vibe_collector',
    email: '<EMAIL>',
    username: 'vibe_collector',
    displayName: 'Marcus Davis',
    bio: '✨ Collecting vibes from around the world | Music journalist & mood curator',
    role: UserRole.FAN,
    isVerified: false,
    avatar: STOCK_AVATARS[7],
  },
]

export async function seedDatabase() {
  try {
    console.log('🌱 Starting comprehensive database seeding...')

    // Clear existing data
    console.log('🧹 Cleaning existing data...')
    await prisma.reaction.deleteMany()
    await prisma.memory.deleteMany()
    await prisma.follow.deleteMany()
    await prisma.post.deleteMany()
    await prisma.persona.deleteMany()
    await prisma.creator.deleteMany()
    await prisma.user.deleteMany()

    // Create realistic users
    console.log('👥 Creating users...')
    const users = await Promise.all(
      REALISTIC_USERS.map(userData =>
        prisma.user.create({ data: userData })
      )
    )

    console.log('✅ Created users')

    // Create creators
    const creators = await Promise.all([
      prisma.creator.create({
        data: {
          userId: users[0].id,
          stageName: 'Maya',
          genre: ['Pop', 'Indie', 'Electronic'],
          location: 'Los Angeles, CA',
          website: 'https://maya-music.com',
          socialLinks: {
            instagram: '@melodic_maya',
            tiktok: '@maya_vibes',
            spotify: 'maya-chen-music',
          },
        },
      }),
      prisma.creator.create({
        data: {
          userId: users[1].id,
          stageName: 'BeatMaster Alex',
          genre: ['Electronic', 'Hip-Hop', 'House'],
          location: 'Miami, FL',
          website: 'https://beatmaster-alex.com',
          socialLinks: {
            soundcloud: 'beatmaster-alex',
            instagram: '@beat_master_alex',
            youtube: 'BeatMasterAlexOfficial',
          },
        },
      }),
    ])

    console.log('✅ Created creators')

    // Create personas
    const personas = await Promise.all([
      prisma.persona.create({
        data: {
          creatorId: creators[0].id,
          name: 'Maya the Dreamer',
          description: 'Soft melodies and introspective lyrics',
          mood: ['peaceful', 'nostalgic', 'inspired'],
          isActive: true,
        },
      }),
      prisma.persona.create({
        data: {
          creatorId: creators[0].id,
          name: 'Maya the Energizer',
          description: 'Upbeat tracks that make you dance',
          mood: ['happy', 'energetic', 'excited'],
          isActive: true,
        },
      }),
      prisma.persona.create({
        data: {
          creatorId: creators[1].id,
          name: 'Alex the Producer',
          description: 'Behind-the-scenes studio content',
          mood: ['inspired', 'energetic'],
          isActive: true,
        },
      }),
    ])

    console.log('✅ Created personas')

    // Create sample posts
    const posts = await Promise.all([
      // Maya's posts
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[0].id,
          title: 'Midnight Reflections',
          content: 'Sometimes the best songs come from the quiet moments. Here\'s a snippet of something I\'ve been working on during those late-night studio sessions. 🌙✨',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/midnight-reflections.mp3'],
          moods: ['peaceful', 'nostalgic', 'inspired'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          viewCount: 1250,
          likeCount: 89,
          shareCount: 12,
        },
      }),
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[1].id,
          title: 'Dance Floor Energy',
          content: 'When the beat drops and the crowd goes wild! This track is all about bringing that festival energy to your headphones 🎉🔥',
          contentType: ContentType.VIDEO,
          mediaUrls: ['https://example.com/video/dance-floor-energy.mp4'],
          moods: ['happy', 'energetic', 'excited'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
          viewCount: 2100,
          likeCount: 156,
          shareCount: 28,
        },
      }),
      // Alex's posts
      prisma.post.create({
        data: {
          userId: users[1].id,
          creatorId: creators[1].id,
          personaId: personas[2].id,
          title: 'Studio Session Vibes',
          content: 'Behind the scenes of creating the perfect beat. Watch how I layer different sounds to create that signature BeatMaster sound 🎛️',
          contentType: ContentType.VIDEO,
          mediaUrls: ['https://example.com/video/studio-session.mp4'],
          moods: ['inspired', 'energetic'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
          viewCount: 890,
          likeCount: 67,
          shareCount: 15,
        },
      }),
      prisma.post.create({
        data: {
          userId: users[1].id,
          creatorId: creators[1].id,
          title: 'Chill Sunday Mix',
          content: 'Sometimes you need to slow it down. Here\'s a chill mix perfect for lazy Sunday afternoons ☀️🎵',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/chill-sunday-mix.mp3'],
          moods: ['chill', 'peaceful'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
          viewCount: 1680,
          likeCount: 124,
          shareCount: 31,
        },
      }),
      // Experimental content
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          title: 'AI Collaboration Experiment',
          content: 'What happens when human creativity meets AI? I\'m experimenting with AI-generated harmonies. Should I release the full version? 🤖🎵',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/ai-collab-experiment.mp3'],
          moods: ['inspired', 'excited'],
          isPublic: true,
          isExperimental: true,
          publishedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          viewCount: 456,
          likeCount: 34,
          shareCount: 8,
        },
      }),
      // Heartbroken mood content
      prisma.post.create({
        data: {
          userId: users[0].id,
          creatorId: creators[0].id,
          personaId: personas[0].id,
          title: 'When Words Aren\'t Enough',
          content: 'Sometimes music is the only language that can express what we feel. This one\'s for everyone going through a tough time. You\'re not alone. 💙',
          contentType: ContentType.MUSIC,
          mediaUrls: ['https://example.com/audio/when-words-arent-enough.mp3'],
          moods: ['heartbroken', 'peaceful'],
          isPublic: true,
          publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
          viewCount: 3200,
          likeCount: 287,
          shareCount: 45,
        },
      }),
    ])

    console.log('✅ Created posts')

    // Create some reactions
    await Promise.all([
      prisma.reaction.create({
        data: {
          userId: users[2].id,
          postId: posts[0].id,
          type: ReactionType.LOVE,
          mood: 'peaceful',
        },
      }),
      prisma.reaction.create({
        data: {
          userId: users[2].id,
          postId: posts[1].id,
          type: ReactionType.FIRE,
          mood: 'energetic',
        },
      }),
    ])

    // Create some memories
    await Promise.all([
      prisma.memory.create({
        data: {
          userId: users[2].id,
          postId: posts[0].id,
          title: 'Perfect late-night vibe',
          description: 'This song perfectly captures how I feel during those quiet moments',
        },
      }),
      prisma.memory.create({
        data: {
          userId: users[2].id,
          postId: posts[5].id,
          title: 'Helped me through a tough time',
          description: 'Maya\'s music always knows how to heal',
        },
      }),
    ])

    // Create follow relationships
    await prisma.follow.create({
      data: {
        followerId: users[2].id,
        followingId: users[0].id,
      },
    })

    await prisma.follow.create({
      data: {
        followerId: users[2].id,
        followingId: users[1].id,
      },
    })

    console.log('✅ Created interactions and relationships')
    console.log('🎉 Database seeding completed successfully!')

    return {
      users,
      creators,
      personas,
      posts,
    }
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}
