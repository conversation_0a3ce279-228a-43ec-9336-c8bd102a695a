import { prisma } from './prisma'
import { UserRole, ContentType, ReactionType } from '@/types'

// Stock media URLs from Unsplash and other free sources
const STOCK_AVATARS = [
  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop&crop=face',
]

const STOCK_MUSIC_COVERS = [
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=800&fit=crop',
  'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=800&h=800&fit=crop',
]

const STOCK_VIDEO_THUMBNAILS = [
  'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&h=600&fit=crop',
]

const REALISTIC_USERS = [
  {
    appwriteId: 'user_maya_chen',
    email: '<EMAIL>',
    username: 'melodic_maya',
    displayName: 'Maya Chen',
    bio: '🎵 Singer-songwriter crafting melodies for every emotion | New album "Midnight Dreams" out now ✨',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[0],
  },
  {
    appwriteId: 'user_alex_beats',
    email: '<EMAIL>',
    username: 'beat_master_alex',
    displayName: 'Alex Rodriguez',
    bio: '🎛️ Producer & DJ | Creating beats that move souls | Miami vibes worldwide 🌴',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[1],
  },
  {
    appwriteId: 'user_luna_sounds',
    email: '<EMAIL>',
    username: 'luna_sounds',
    displayName: 'Luna Park',
    bio: '🌙 Dreamy soundscapes & ethereal vocals | Indie folk with electronic touches',
    role: UserRole.CREATOR,
    isVerified: false,
    avatar: STOCK_AVATARS[2],
  },
  {
    appwriteId: 'user_rhythm_king',
    email: '<EMAIL>',
    username: 'rhythm_king_jay',
    displayName: 'Jay Williams',
    bio: '🥁 Drummer & rhythm producer | Hip-hop meets jazz fusion | Teaching beats online',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[3],
  },
  {
    appwriteId: 'user_indie_rose',
    email: '<EMAIL>',
    username: 'indie_rose',
    displayName: 'Rose Martinez',
    bio: '🌹 Indie rock guitarist | Raw emotions through power chords | Coffee shop sessions',
    role: UserRole.CREATOR,
    isVerified: false,
    avatar: STOCK_AVATARS[4],
  },
  {
    appwriteId: 'user_sam_fan',
    email: '<EMAIL>',
    username: 'music_lover_sam',
    displayName: 'Sam Johnson',
    bio: '🎧 Music enthusiast & vibe curator | Discovering hidden gems daily | Playlist master',
    role: UserRole.FAN,
    isVerified: false,
    avatar: STOCK_AVATARS[5],
  },
  {
    appwriteId: 'user_melody_hunter',
    email: '<EMAIL>',
    username: 'melody_hunter',
    displayName: 'Emma Thompson',
    bio: '🎼 Classical pianist exploring modern sounds | Conservatory trained, heart untamed',
    role: UserRole.CREATOR,
    isVerified: true,
    avatar: STOCK_AVATARS[6],
  },
  {
    appwriteId: 'user_vibe_collector',
    email: '<EMAIL>',
    username: 'vibe_collector',
    displayName: 'Marcus Davis',
    bio: '✨ Collecting vibes from around the world | Music journalist & mood curator',
    role: UserRole.FAN,
    isVerified: false,
    avatar: STOCK_AVATARS[7],
  },
]

export async function seedDatabase() {
  try {
    console.log('🌱 Starting comprehensive database seeding...')

    // Clear existing data
    console.log('🧹 Cleaning existing data...')
    await prisma.reaction.deleteMany()
    await prisma.memory.deleteMany()
    await prisma.follow.deleteMany()
    await prisma.post.deleteMany()
    await prisma.persona.deleteMany()
    await prisma.creator.deleteMany()
    await prisma.user.deleteMany()

    // Create realistic users
    console.log('👥 Creating users...')
    const users = await Promise.all(
      REALISTIC_USERS.map(userData =>
        prisma.user.create({ data: userData })
      )
    )

    console.log('✅ Created users')

    // Create creators (filter only creator users)
    const creatorUsers = users.filter(user => user.role === UserRole.CREATOR)
    console.log('🎨 Creating creator profiles...')

    const creatorData = [
      {
        userId: creatorUsers[0].id, // Maya Chen
        stageName: 'Maya',
        genre: ['Pop', 'Indie', 'Electronic', 'Singer-Songwriter'],
        location: 'Los Angeles, CA',
        website: 'https://maya-music.com',
        socialLinks: {
          instagram: '@melodic_maya',
          tiktok: '@maya_vibes',
          spotify: 'maya-chen-music',
          youtube: 'MayaChenOfficial',
        },
        totalFollowers: 12500,
        totalViews: 450000,
        totalLikes: 34200,
      },
      {
        userId: creatorUsers[1].id, // Alex Rodriguez
        stageName: 'BeatMaster Alex',
        genre: ['Electronic', 'Hip-Hop', 'House', 'Techno'],
        location: 'Miami, FL',
        website: 'https://beatmaster-alex.com',
        socialLinks: {
          soundcloud: 'beatmaster-alex',
          instagram: '@beat_master_alex',
          youtube: 'BeatMasterAlexOfficial',
          spotify: 'alex-rodriguez-beats',
        },
        totalFollowers: 8900,
        totalViews: 320000,
        totalLikes: 28500,
      },
      {
        userId: creatorUsers[2].id, // Luna Park
        stageName: 'Luna',
        genre: ['Indie Folk', 'Electronic', 'Ambient', 'Dream Pop'],
        location: 'Portland, OR',
        website: 'https://luna-sounds.com',
        socialLinks: {
          bandcamp: 'luna-park-music',
          instagram: '@luna_sounds',
          spotify: 'luna-park-official',
        },
        totalFollowers: 15200,
        totalViews: 280000,
        totalLikes: 22100,
      },
      {
        userId: creatorUsers[3].id, // Jay Williams
        stageName: 'Rhythm King Jay',
        genre: ['Hip-Hop', 'Jazz Fusion', 'R&B', 'Neo-Soul'],
        location: 'Atlanta, GA',
        website: 'https://rhythmking.com',
        socialLinks: {
          instagram: '@rhythm_king_jay',
          youtube: 'RhythmKingJayOfficial',
          spotify: 'jay-williams-drums',
        },
        totalFollowers: 6700,
        totalViews: 180000,
        totalLikes: 15400,
      },
      {
        userId: creatorUsers[4].id, // Rose Martinez
        stageName: 'Indie Rose',
        genre: ['Indie Rock', 'Alternative', 'Grunge', 'Post-Punk'],
        location: 'Seattle, WA',
        socialLinks: {
          instagram: '@indie_rose',
          bandcamp: 'indie-rose-music',
          spotify: 'rose-martinez-indie',
        },
        totalFollowers: 4300,
        totalViews: 95000,
        totalLikes: 8900,
      },
      {
        userId: creatorUsers[5].id, // Emma Thompson
        stageName: 'Melody Hunter',
        genre: ['Classical', 'Neo-Classical', 'Ambient', 'Cinematic'],
        location: 'New York, NY',
        website: 'https://melodyhunter.com',
        socialLinks: {
          instagram: '@melody_hunter',
          youtube: 'MelodyHunterPiano',
          spotify: 'emma-thompson-piano',
        },
        totalFollowers: 9800,
        totalViews: 220000,
        totalLikes: 18600,
      },
    ]

    const creators = await Promise.all(
      creatorData.map(data => prisma.creator.create({ data }))
    )

    console.log('✅ Created creators')

    // Create diverse personas for creators
    console.log('🎭 Creating creator personas...')
    const personaData = [
      // Maya Chen personas
      {
        creatorId: creators[0].id,
        name: 'Maya the Dreamer',
        description: 'Soft melodies and introspective lyrics for late-night contemplation',
        avatar: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=1200&h=600&fit=crop',
        mood: ['peaceful', 'nostalgic', 'inspired'],
        isActive: true,
      },
      {
        creatorId: creators[0].id,
        name: 'Maya the Energizer',
        description: 'Upbeat pop anthems that make you dance and feel alive',
        avatar: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=1200&h=600&fit=crop',
        mood: ['happy', 'energetic', 'excited'],
        isActive: true,
      },
      // Alex Rodriguez personas
      {
        creatorId: creators[1].id,
        name: 'Alex the Producer',
        description: 'Behind-the-scenes studio magic and beat-making tutorials',
        avatar: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=1200&h=600&fit=crop',
        mood: ['inspired', 'energetic'],
        isActive: true,
      },
      {
        creatorId: creators[1].id,
        name: 'Alex the DJ',
        description: 'Live sets and club bangers that move the crowd',
        avatar: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=1200&h=600&fit=crop',
        mood: ['energetic', 'excited', 'happy'],
        isActive: true,
      },
      // Luna Park personas
      {
        creatorId: creators[2].id,
        name: 'Luna the Mystic',
        description: 'Ethereal soundscapes and mystical folk tales',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=1200&h=600&fit=crop',
        mood: ['peaceful', 'nostalgic', 'inspired'],
        isActive: true,
      },
      // Jay Williams personas
      {
        creatorId: creators[3].id,
        name: 'Jay the Teacher',
        description: 'Drum lessons and rhythm tutorials for all skill levels',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=600&fit=crop',
        mood: ['inspired', 'energetic'],
        isActive: true,
      },
      // Rose Martinez personas
      {
        creatorId: creators[4].id,
        name: 'Rose the Rebel',
        description: 'Raw indie rock with powerful messages and guitar riffs',
        avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=1200&h=600&fit=crop',
        mood: ['energetic', 'inspired'],
        isActive: true,
      },
      // Emma Thompson personas
      {
        creatorId: creators[5].id,
        name: 'Emma the Virtuoso',
        description: 'Classical piano performances and modern interpretations',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop',
        coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=1200&h=600&fit=crop',
        mood: ['peaceful', 'inspired', 'nostalgic'],
        isActive: true,
      },
    ]

    const personas = await Promise.all(
      personaData.map(data => prisma.persona.create({ data }))
    )

    console.log('✅ Created personas')

    // Create comprehensive realistic posts with HVPPY content
    console.log('📝 Creating realistic posts with HVPPY content...')

    // HVPPY content IDs from the classification system
    const hvppyContentIds = [
      'hvppy-mbambe-visualizer',
      'hvppy-put-in-work',
      'hvppy-love-heals',
      'hvppy-love-heals-visualizer',
      'hvppy-levels-visualizer',
      'love-heals-doc-part1',
      'love-heals-doc-part2',
      'love-heals-doc-part3',
      'love-heals-doc-part4',
      'love-heals-doc-part5'
    ]

    const postData = [
      // HVPPY Official Content - Featured
      {
        userId: creatorUsers[0].id, // Maya as HVPPY
        creatorId: creators[0].id,
        personaId: personas[0].id,
        title: 'HVPPY - MBAMBE(Visualizer)',
        content: 'The energy is unmatched! 🔥 MBAMBE brings that raw South African hip-hop flavor that gets your soul moving. This visualizer captures the essence of the streets and the dreams we chase. #HVPPY #MBAMBE #SouthAfricanHipHop',
        contentType: ContentType.VIDEO,
        contentUrl: 'hvppy-mbambe-visualizer', // Reference to HVPPY content
        mediaUrls: ['/api/player/stream/hvppy-mbambe-visualizer'],
        thumbnailUrl: STOCK_MUSIC_COVERS[0],
        moods: ['energetic', 'confident', 'inspired'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        viewCount: 1250,
        likeCount: 89,
        shareCount: 12,
      },
      {
        userId: creatorUsers[0].id,
        creatorId: creators[0].id,
        personaId: personas[0].id,
        title: 'HVPPY - Put in Work feat. Djy ZanSA (Official Music Video)',
        content: 'The grind never stops! 💪 This collaboration with Djy ZanSA represents the hustle and dedication it takes to make it in this industry. Every beat, every bar, every moment - we put in work! #PutInWork #Collaboration #HustleHard',
        contentType: ContentType.VIDEO,
        contentUrl: hvppyContentIds[1], // hvppy-put-in-work
        mediaUrls: ['/api/player/stream/hvppy-put-in-work'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[0],
        moods: ['energetic', 'confident', 'motivated'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        viewCount: 890,
        likeCount: 67,
        shareCount: 8,
      },
      // HVPPY Love Heals Content
      {
        userId: creatorUsers[0].id,
        creatorId: creators[0].id,
        personaId: personas[1].id,
        title: 'HVPPY LTD - Love Heals, No More Pain (Official Music Video)',
        content: 'Sometimes music is the medicine we all need. 💙 This track with Blxck Boy Tingz comes from a place of healing and hope. When the world feels heavy, let love be your guide. #LoveHeals #Healing #Hope',
        contentType: ContentType.VIDEO,
        contentUrl: hvppyContentIds[2], // hvppy-love-heals
        mediaUrls: ['/api/player/stream/hvppy-love-heals'],
        thumbnailUrl: STOCK_MUSIC_COVERS[1],
        moods: ['healing', 'emotional', 'hopeful'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
        viewCount: 2100,
        likeCount: 156,
        shareCount: 28,
      },
      {
        userId: creatorUsers[0].id,
        creatorId: creators[0].id,
        personaId: personas[1].id,
        title: 'HVPPY - Levels (Reloaded Freestyle) Visualizer',
        content: 'Levels on levels! 🚀 This freestyle reloaded version shows the evolution of the craft. From the streets to the studio, every level teaches you something new. Watch the visualizer and feel the energy! #Levels #Freestyle #Evolution',
        contentType: ContentType.VIDEO,
        contentUrl: hvppyContentIds[4], // hvppy-levels-visualizer
        mediaUrls: ['/api/player/stream/hvppy-levels-visualizer'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[1],
        moods: ['confident', 'energetic', 'freestyle'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        viewCount: 3400,
        likeCount: 234,
        shareCount: 45,
      },
      // Documentary Content
      {
        userId: creatorUsers[1].id,
        creatorId: creators[1].id,
        personaId: personas[2].id,
        title: 'Love Heals Documentary - Behind the Scenes Part 1',
        content: 'Take a journey behind the scenes of the "Love Heals, No More Pain" project. This unscripted documentary shows the real story, the real emotions, and the real process of creating music that heals. � #Documentary #BehindTheScenes #RealStory',
        contentType: ContentType.VIDEO,
        contentUrl: hvppyContentIds[5], // love-heals-doc-part1
        mediaUrls: ['/api/player/stream/love-heals-doc-part1'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[2],
        moods: ['authentic', 'behind_scenes', 'storytelling'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        viewCount: 890,
        likeCount: 67,
        shareCount: 15,
      },
      {
        userId: creatorUsers[1].id,
        creatorId: creators[1].id,
        personaId: personas[2].id,
        title: 'Beat Making Tutorial: 808s',
        content: 'Requested tutorial on how I get those punchy 808s that hit just right. It\'s all about the layering and the right compression settings. Drop a 🔥 if this helped you! #Tutorial #808s #ProducerTips',
        contentType: ContentType.VIDEO,
        mediaUrls: ['https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[3],
        moods: ['inspired', 'energetic'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        viewCount: 1560,
        likeCount: 123,
        shareCount: 34,
      },
      // Alex Rodriguez - DJ Persona Posts
      {
        userId: creatorUsers[1].id,
        creatorId: creators[1].id,
        personaId: personas[3].id,
        title: 'Miami Sunset Set',
        content: 'Last night\'s sunset set at South Beach was absolutely magical! The energy from the crowd was incredible. Here\'s a taste of what went down. Can\'t wait to be back! 🌅🎧 #MiamiVibes #SunsetSet',
        contentType: ContentType.VIDEO,
        mediaUrls: ['https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[4],
        moods: ['energetic', 'excited', 'happy'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
        viewCount: 2890,
        likeCount: 234,
        shareCount: 67,
      },
      // Luna Park Posts
      {
        userId: creatorUsers[2].id,
        creatorId: creators[2].id,
        personaId: personas[4].id,
        title: 'Forest Meditation',
        content: 'Recorded this ambient piece deep in the Pacific Northwest forests. The sounds of nature became part of the composition. Close your eyes and let yourself drift away. 🌲🧘‍♀️ #AmbientMusic #NatureSounds',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[2],
        moods: ['peaceful', 'nostalgic', 'inspired'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        viewCount: 1120,
        likeCount: 89,
        shareCount: 23,
      },
      {
        userId: creatorUsers[2].id,
        creatorId: creators[2].id,
        personaId: personas[4].id,
        title: 'Moonlight Serenade',
        content: 'Sometimes the moon speaks in melodies. This ethereal piece came to me during a late-night walk under the full moon. Each note carries the whispers of the night. 🌙✨ #MoonlightMusic #EtherealSounds',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[3],
        moods: ['peaceful', 'nostalgic'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
        viewCount: 1890,
        likeCount: 145,
        shareCount: 31,
      },
      // Jay Williams Posts
      {
        userId: creatorUsers[3].id,
        creatorId: creators[3].id,
        personaId: personas[5].id,
        title: 'Polyrhythm Breakdown',
        content: 'Breaking down complex polyrhythms in a way that\'s easy to understand. This technique will take your drumming to the next level! Practice slowly and build up the speed. 🥁 #DrumLesson #Polyrhythm',
        contentType: ContentType.VIDEO,
        mediaUrls: ['https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'],
        thumbnailUrl: STOCK_VIDEO_THUMBNAILS[5],
        moods: ['inspired', 'energetic'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        viewCount: 756,
        likeCount: 67,
        shareCount: 12,
      },
      // Rose Martinez Posts
      {
        userId: creatorUsers[4].id,
        creatorId: creators[4].id,
        personaId: personas[6].id,
        title: 'Raw Emotion',
        content: 'This song came from a place of pure emotion. No fancy production, no auto-tune, just raw feelings translated into sound. Sometimes that\'s all you need. 🎸💔 #IndieRock #RawEmotion',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[4],
        moods: ['heartbroken', 'inspired'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 18 * 60 * 60 * 1000),
        viewCount: 567,
        likeCount: 45,
        shareCount: 8,
      },
      // Emma Thompson Posts
      {
        userId: creatorUsers[5].id,
        creatorId: creators[5].id,
        personaId: personas[7].id,
        title: 'Chopin Meets Electronica',
        content: 'What happens when you blend Chopin\'s Nocturne with modern electronic elements? This experimental piece bridges centuries of musical evolution. Classical meets contemporary. 🎹⚡ #ClassicalFusion #PianoElectronica',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[5],
        moods: ['inspired', 'peaceful'],
        isPublic: true,
        isExperimental: true,
        publishedAt: new Date(Date.now() - 30 * 60 * 1000),
        viewCount: 456,
        likeCount: 34,
        shareCount: 8,
      },
      // Heartbroken mood content
      {
        userId: creatorUsers[0].id,
        creatorId: creators[0].id,
        personaId: personas[0].id,
        title: 'When Words Aren\'t Enough',
        content: 'Sometimes music is the only language that can express what we feel. This one\'s for everyone going through a tough time. You\'re not alone in this journey. Healing takes time, but music helps. 💙 #Healing #HeartbrokenVibes',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[6],
        moods: ['heartbroken', 'peaceful'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
        viewCount: 3200,
        likeCount: 287,
        shareCount: 45,
      },
      // Additional diverse content
      {
        userId: creatorUsers[1].id,
        creatorId: creators[1].id,
        title: 'Chill Sunday Mix',
        content: 'Sometimes you need to slow it down. Here\'s a chill mix perfect for lazy Sunday afternoons, reading a book, or just watching the world go by. Pure relaxation vibes. ☀️🎵 #ChillVibes #SundayMood',
        contentType: ContentType.MUSIC,
        mediaUrls: ['https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'],
        thumbnailUrl: STOCK_MUSIC_COVERS[7],
        moods: ['chill', 'peaceful'],
        isPublic: true,
        publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
        viewCount: 1680,
        likeCount: 124,
        shareCount: 31,
      },
    ]

    const posts = await Promise.all(
      postData.map(data => prisma.post.create({ data }))
    )

    console.log('✅ Created posts')

    // Create realistic reactions from fans
    console.log('❤️ Creating user reactions...')
    const fanUsers = users.filter(user => user.role === UserRole.FAN)

    const reactionData = [
      // Sam Johnson reactions
      { userId: fanUsers[0].id, postId: posts[0].id, type: ReactionType.LOVE, mood: 'peaceful' },
      { userId: fanUsers[0].id, postId: posts[1].id, type: ReactionType.FIRE, mood: 'energetic' },
      { userId: fanUsers[0].id, postId: posts[2].id, type: ReactionType.LIKE, mood: 'inspired' },
      { userId: fanUsers[0].id, postId: posts[6].id, type: ReactionType.LOVE, mood: 'peaceful' },
      { userId: fanUsers[0].id, postId: posts[11].id, type: ReactionType.MOOD_MATCH, mood: 'heartbroken' },

      // Marcus Davis reactions
      { userId: fanUsers[1].id, postId: posts[3].id, type: ReactionType.VIBE, mood: 'happy' },
      { userId: fanUsers[1].id, postId: posts[4].id, type: ReactionType.FIRE, mood: 'energetic' },
      { userId: fanUsers[1].id, postId: posts[7].id, type: ReactionType.LOVE, mood: 'peaceful' },
      { userId: fanUsers[1].id, postId: posts[9].id, type: ReactionType.LIKE, mood: 'inspired' },
      { userId: fanUsers[1].id, postId: posts[12].id, type: ReactionType.VIBE, mood: 'chill' },

      // Cross-creator reactions (creators supporting each other)
      { userId: creatorUsers[1].id, postId: posts[0].id, type: ReactionType.LOVE, mood: 'peaceful' },
      { userId: creatorUsers[0].id, postId: posts[4].id, type: ReactionType.FIRE, mood: 'energetic' },
      { userId: creatorUsers[2].id, postId: posts[11].id, type: ReactionType.MOOD_MATCH, mood: 'heartbroken' },
      { userId: creatorUsers[3].id, postId: posts[5].id, type: ReactionType.VIBE, mood: 'energetic' },
      { userId: creatorUsers[4].id, postId: posts[7].id, type: ReactionType.LOVE, mood: 'peaceful' },
    ]

    await Promise.all(
      reactionData.map(data => prisma.reaction.create({ data }))
    )

    // Create meaningful fan memories
    console.log('💭 Creating fan memories...')
    const memoryData = [
      {
        userId: fanUsers[0].id,
        postId: posts[0].id,
        title: 'Perfect late-night study companion',
        description: 'This song got me through so many late-night study sessions. Maya\'s voice is like a warm hug when you need it most.',
        timestamp: 45, // 45 seconds into the track
      },
      {
        userId: fanUsers[0].id,
        postId: posts[11].id,
        title: 'Helped me through my breakup',
        description: 'When words weren\'t enough, this song was. Maya\'s music always knows how to heal a broken heart.',
        timestamp: 120,
      },
      {
        userId: fanUsers[1].id,
        postId: posts[4].id,
        title: 'Epic workout motivation',
        description: 'This beat drop hits different during a workout. Alex knows how to make you feel invincible!',
        timestamp: 78,
      },
      {
        userId: fanUsers[1].id,
        postId: posts[7].id,
        title: 'Meditation session soundtrack',
        description: 'Luna\'s forest meditation piece became my go-to for morning mindfulness. Pure magic.',
        timestamp: 180,
      },
      {
        userId: creatorUsers[1].id,
        postId: posts[0].id,
        title: 'Inspiration for my next track',
        description: 'Maya\'s chord progression here inspired a whole new direction for my upcoming album.',
        timestamp: 95,
      },
    ]

    await Promise.all(
      memoryData.map(data => prisma.memory.create({ data }))
    )

    // Create realistic follow relationships
    console.log('👥 Creating follow relationships...')
    const followData = [
      // Fans following creators
      { followerId: fanUsers[0].id, followingId: creatorUsers[0].id }, // Sam follows Maya
      { followerId: fanUsers[0].id, followingId: creatorUsers[1].id }, // Sam follows Alex
      { followerId: fanUsers[0].id, followingId: creatorUsers[2].id }, // Sam follows Luna
      { followerId: fanUsers[1].id, followingId: creatorUsers[0].id }, // Marcus follows Maya
      { followerId: fanUsers[1].id, followingId: creatorUsers[1].id }, // Marcus follows Alex
      { followerId: fanUsers[1].id, followingId: creatorUsers[3].id }, // Marcus follows Jay
      { followerId: fanUsers[1].id, followingId: creatorUsers[5].id }, // Marcus follows Emma

      // Creators following each other (mutual support)
      { followerId: creatorUsers[0].id, followingId: creatorUsers[1].id }, // Maya follows Alex
      { followerId: creatorUsers[1].id, followingId: creatorUsers[0].id }, // Alex follows Maya
      { followerId: creatorUsers[0].id, followingId: creatorUsers[2].id }, // Maya follows Luna
      { followerId: creatorUsers[2].id, followingId: creatorUsers[0].id }, // Luna follows Maya
      { followerId: creatorUsers[1].id, followingId: creatorUsers[3].id }, // Alex follows Jay
      { followerId: creatorUsers[3].id, followingId: creatorUsers[1].id }, // Jay follows Alex
      { followerId: creatorUsers[4].id, followingId: creatorUsers[2].id }, // Rose follows Luna
      { followerId: creatorUsers[5].id, followingId: creatorUsers[0].id }, // Emma follows Maya
    ]

    await Promise.all(
      followData.map(data => prisma.follow.create({ data }))
    )

    // Update post engagement counts based on reactions
    console.log('📊 Updating engagement metrics...')
    for (const post of posts) {
      const reactionCount = await prisma.reaction.count({
        where: { postId: post.id }
      })
      const memoryCount = await prisma.memory.count({
        where: { postId: post.id }
      })

      // Use memory count for additional engagement metrics if needed
      const totalEngagement = reactionCount + memoryCount

      await prisma.post.update({
        where: { id: post.id },
        data: {
          likeCount: reactionCount,
          // Add some realistic view counts based on total engagement
          viewCount: Math.max(post.viewCount, totalEngagement * 15 + Math.floor(Math.random() * 500)),
        }
      })
    }

    console.log('✅ Created interactions and relationships')
    console.log('🎉 Comprehensive database seeding completed successfully!')
    console.log(`📈 Summary:`)
    console.log(`   - ${users.length} users created`)
    console.log(`   - ${creators.length} creators created`)
    console.log(`   - ${personas.length} personas created`)
    console.log(`   - ${posts.length} posts created`)
    console.log(`   - ${reactionData.length} reactions created`)
    console.log(`   - ${memoryData.length} memories created`)
    console.log(`   - ${followData.length} follow relationships created`)

    return {
      users,
      creators,
      personas,
      posts,
      summary: {
        users: users.length,
        creators: creators.length,
        personas: personas.length,
        posts: posts.length,
        reactions: reactionData.length,
        memories: memoryData.length,
        follows: followData.length,
      }
    }
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}
