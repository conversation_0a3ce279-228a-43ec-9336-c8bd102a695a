import { User, Creator, Persona, Post, Reaction, Memory, MoodType, ContentType, ReactionType } from './index'

// Feed-specific types
export interface FeedItem {
  id: string
  post: Post & {
    user: User
    creator?: Creator
    persona?: Persona
    reactions: Reaction[]
    memories: Memory[]
    _count: {
      reactions: number
      memories: number
    }
  }
  // Feed-specific metadata
  feedScore?: number // AI relevance score
  moodMatch?: number // Mood matching score (0-1)
  isSponsored?: boolean
  insertedAt: Date
}

export interface FeedResponse {
  items: FeedItem[]
  nextCursor?: string
  hasMore: boolean
  totalCount?: number
}

export enum FeedType {
  FOLLOWING = 'following',
  DISCOVER = 'discover', 
  MOOD_BASED = 'mood-based',
  TRENDING = 'trending',
  EXPERIMENTAL = 'experimental',
  PERSONA = 'persona'
}

export interface FeedFilters {
  moods?: MoodType[]
  contentTypes?: ContentType[]
  timeRange?: 'today' | 'week' | 'month' | 'all'
  creatorIds?: string[]
  personaIds?: string[]
  isExperimental?: boolean
}

export interface FeedPreferences {
  defaultFeedType: FeedType
  autoPlay: boolean
  showCaptions: boolean
  preferredMoods: MoodType[]
  blockedCreators: string[]
  notificationSettings: {
    newFollowerContent: boolean
    moodMatches: boolean
    trending: boolean
  }
}

export interface InteractionData {
  postId: string
  type: ReactionType
  mood?: MoodType
  timestamp: Date
  metadata?: Record<string, any>
}

export interface VideoPlayerState {
  currentPostId?: string
  isPlaying: boolean
  isMuted: boolean
  volume: number
  currentTime: number
  duration: number
  isBuffering: boolean
  playbackRate: number
}

export interface FeedState {
  currentFeed: FeedType
  items: FeedItem[]
  loading: boolean
  error?: string
  hasMore: boolean
  nextCursor?: string
  filters: FeedFilters
  currentIndex: number
  preloadedItems: Set<string>
}

export interface UserInteractionState {
  reactions: Map<string, ReactionType>
  memories: Set<string>
  shares: Set<string>
  views: Map<string, number>
  watchTime: Map<string, number>
}

// API request/response types
export interface GetFeedRequest {
  type: FeedType
  cursor?: string
  limit?: number
  filters?: FeedFilters
  userId?: string
}

export interface CreateInteractionRequest {
  postId: string
  type: ReactionType
  mood?: MoodType
  metadata?: Record<string, any>
}

export interface CreateMemoryRequest {
  postId: string
  title?: string
  description?: string
  timestamp?: number
}

export interface MoodMatchRequest {
  userMood: MoodType
  limit?: number
  excludePostIds?: string[]
}

export interface FeedAnalytics {
  totalViews: number
  averageWatchTime: number
  engagementRate: number
  moodDistribution: Record<MoodType, number>
  topPerformingContent: FeedItem[]
  userRetention: number
}

// Component prop types
export interface VerticalFeedContainerProps {
  feedType: FeedType
  filters?: FeedFilters
  onItemChange?: (item: FeedItem, index: number) => void
  onInteraction?: (interaction: InteractionData) => void
  className?: string
  autoPlay?: boolean
}

export interface ContentCardProps {
  item: FeedItem
  isActive: boolean
  autoPlay: boolean
  onInteraction: (interaction: InteractionData) => void
  onMemoryCreate: (data: CreateMemoryRequest) => void
  className?: string
}

export interface FeedControlsProps {
  item: FeedItem
  userReaction?: ReactionType
  hasMemory: boolean
  onReaction: (type: ReactionType, mood?: MoodType) => void
  onShare: () => void
  onMemoryToggle: () => void
  onComment: () => void
  className?: string
}

export interface MoodSelectorProps {
  selectedMoods: MoodType[]
  onMoodChange: (moods: MoodType[]) => void
  maxSelections?: number
  showCounts?: boolean
  moodCounts?: Record<MoodType, number>
  className?: string
}

export interface CreatorInfoProps {
  creator: Creator
  persona?: Persona
  isFollowing: boolean
  onFollow: () => void
  onPersonaChange?: (personaId: string) => void
  className?: string
}

export interface FeedNavigationProps {
  currentFeed: FeedType
  onFeedChange: (type: FeedType) => void
  unreadCounts?: Record<FeedType, number>
  className?: string
}

// Hook return types
export interface UseFeedDataReturn {
  items: FeedItem[]
  loading: boolean
  error?: string
  hasMore: boolean
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  updateFilters: (filters: Partial<FeedFilters>) => void
}

export interface UseInfiniteScrollReturn {
  containerRef: React.RefObject<HTMLDivElement>
  currentIndex: number
  isScrolling: boolean
  scrollToIndex: (index: number) => void
  scrollDirection: 'up' | 'down' | null
}

export interface UseMoodFilterReturn {
  selectedMoods: MoodType[]
  setSelectedMoods: (moods: MoodType[]) => void
  filteredItems: FeedItem[]
  moodCounts: Record<MoodType, number>
  clearMoods: () => void
}

export interface UseContentInteractionsReturn {
  reactions: Map<string, ReactionType>
  memories: Set<string>
  loading: Set<string>
  react: (postId: string, type: ReactionType, mood?: MoodType) => Promise<void>
  unreact: (postId: string) => Promise<void>
  toggleMemory: (postId: string, data?: CreateMemoryRequest) => Promise<void>
  share: (postId: string) => Promise<void>
}

export interface UseVideoPlayerReturn {
  playerState: VideoPlayerState
  play: (postId: string) => void
  pause: () => void
  toggleMute: () => void
  setVolume: (volume: number) => void
  seek: (time: number) => void
  setPlaybackRate: (rate: number) => void
}
