# HVPPY Central Information

## Summary
HVPPY Central is a media content sharing and management platform for artists and content creators, featuring emotional-AI fan engagement, persona-based creator channels, and powerful content management tools. The platform aims to revolutionize how artists and fans connect through emotional intelligence and innovative content formats.

## Structure
The repository consists of a main Next.js application and a separate MediaSoup server for real-time communication:

- **src/**: Main Next.js application code
  - **app/**: Next.js 15 App Router components
  - **components/**: Reusable UI components
  - **lib/**: Utility libraries and services
  - **hooks/**: Custom React hooks
  - **types/**: TypeScript type definitions
- **mediasoup-server/**: Separate server for WebRTC streaming
- **prisma/**: Database schema and migrations
- **public/**: Static assets and content
- **scripts/**: Utility scripts including database seeding

## Projects

### Next.js Application
**Configuration File**: package.json

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.6.3
**Runtime**: Node.js 18+
**Framework**: Next.js 15.0.0, React 19.0.0
**Build System**: Next.js
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- Next.js 15.0.0 with App Router
- React 19.0.0
- Prisma ORM with PostgreSQL
- Appwrite for auth, storage, and realtime
- Shadcn/UI components with Tailwind CSS
- AI integration (@ai-sdk/google, OpenAI)
- MediaSoup client for WebRTC
- Framer Motion for animations

**Development Dependencies**:
- TypeScript 5.6.3
- ESLint 8.57.1
- Tailwind CSS 3.4.14
- Prisma CLI 5.22.0

#### Build & Installation
```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Development server
npm run dev

# Production build
npm run build

# Start production server
npm run start
```

### MediaSoup Server
**Configuration File**: mediasoup-server/package.json

#### Language & Runtime
**Language**: JavaScript
**Runtime**: Node.js
**Framework**: Express
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- MediaSoup 3.16.6 (WebRTC SFU)
- Express 5.1.0
- WebSocket (ws) 8.18.3
- Appwrite 18.1.1
- JSON Web Token 9.0.2

#### Build & Installation
```bash
# Install dependencies
cd mediasoup-server
npm install

# Start server
npm start
```

## Database
**Type**: PostgreSQL
**ORM**: Prisma
**Schema**: Comprehensive data model including:
- User authentication and profiles
- Creator and persona management
- Content posts with emotional metadata
- Fan engagement (likes, comments, reactions)
- Memory system for fan-saved moments

## Authentication
**System**: NextAuth.js 5.0.0-beta.29
**Providers**:
- Email/Password
- OAuth providers
- Two-factor authentication
- Password reset functionality

## Environment Setup
Required environment variables:
- PostgreSQL database URL
- Appwrite project credentials
- OpenAI API key

## Prerequisites
- Node.js 18+
- PostgreSQL database
- Appwrite account