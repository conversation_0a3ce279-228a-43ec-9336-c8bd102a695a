# Authentication System Documentation

## Overview
This document outlines the authentication system implemented for the HVPPY Central platform. It provides a comprehensive solution for user registration, login, password management, two-factor authentication (2FA), and role-based access control (RBAC), ensuring a secure and user-friendly experience.

## Key Technologies
*   **Next.js (App Router):** The React framework for building the web application.
*   **NextAuth.js (v5 Beta):** The authentication library for Next.js, handling various authentication strategies and session management.
*   **Prisma:** An ORM (Object-Relational Mapper) for database interaction, used with a PostgreSQL database.
*   **PostgreSQL:** The relational database used to store user and authentication-related data.
*   **Bcrypt.js:** A library for hashing and comparing passwords securely.
*   **Nodemailer:** A module for sending emails (e.g., verification, password reset).
*   **OTPAuth:** A library for generating and verifying TOTP (Time-based One-Time Password) tokens for 2FA.
*   **Shadcn UI:** A collection of reusable UI components built with Radix UI and Tai<PERSON>wind CSS, used for a consistent and modern user interface.
*   **Sonner:** A toast library for displaying user notifications.

## Database Schema (Prisma)
The following Prisma models are central to the authentication system:

```prisma
// User model - extends Appwrite user data
model User {
  id                     String    @id @default(cuid())
  name                   String?
  email                  String?   @unique
  emailVerified          DateTime?
  image                  String?
  password               String? // Hashed password for credentials provider
  twoFactorEnabled       Boolean   @default(false)
  twoFactorSecret        String?   @db.Text
  twoFactorRecoveryCodes String[]  // Array of strings for recovery codes

  // Custom fields for HVPPY Central
  appwriteId             String?   @unique // Links to Appwrite user ID
  username               String?   @unique
  displayName            String?
  bio                    String?
  avatar                 String?
  role                   UserRole  @default(FAN)
  isVerified             Boolean   @default(false) // Indicates if email is verified

  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  accounts               Account[]
  sessions               Session[]
  resetTokens            ResetToken[]
  // ... other relations (creator, posts, reactions, memories, followers, following)
}

// NextAuth.js models (managed by Prisma Adapter)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model ResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("reset_tokens")
}

enum UserRole {
  FAN
  CREATOR
  ADMIN
}
```

## Authentication Flows

### 1. User Registration
*   **UI:** `src/app/auth/register/page.tsx`
*   **API:** `src/app/api/register/route.ts` (POST)
*   **Process:**
    1.  User provides name, email, and password.
    2.  Client-side password validation ensures strength.
    3.  Data is sent to `/api/register`.
    4.  Server-side validation (email uniqueness, password strength).
    5.  Password is hashed using `bcrypt.js`.
    6.  User record is created in the `User` table.
    7.  A `VerificationToken` is generated and saved.
    8.  A verification email with a unique link is sent to the user via Nodemailer.
    9.  User is redirected to the login page with a success message.

### 2. User Login
*   **UI:** `src/app/auth/login/page.tsx`
*   **API:** Handled internally by NextAuth.js via `src/app/api/auth/[...nextauth]/route.ts`
*   **Process (Credentials):**
    1.  User provides email and password.
    2.  Data is sent to NextAuth.js `signIn` function with `credentials` provider.
    3.  `authorize` callback in `src/lib/auth.ts` fetches user by email.
    4.  Password is compared using `bcrypt.js`.
    5.  If 2FA is enabled for the user, a `TwoFactorRequired` error is thrown, prompting the UI to show a 2FA input.
    6.  If 2FA code is provided, it's validated using `otpauth`.
    7.  Upon successful authentication, a session is established.
*   **Process (OAuth - GitHub, Google, Facebook):**
    1.  User clicks on the respective OAuth button.
    2.  NextAuth.js redirects to the OAuth provider's authorization page.
    3.  User grants permission.
    4.  Provider redirects back to NextAuth.js callback URL.
    5.  NextAuth.js handles user creation/linking and session establishment.

### 3. Forgot Password / Reset Password
*   **UI:**
    *   `src/app/auth/forgot-password/page.tsx`
    *   `src/app/auth/reset-password/[token]/page.tsx`
*   **API:**
    *   `src/app/api/auth/forgot-password/route.ts` (POST)
    *   `src/app/api/auth/reset-password/route.ts` (POST)
*   **Process:**
    1.  User enters email on `forgot-password` page.
    2.  Request sent to `/api/auth/forgot-password`.
    3.  Server generates a `ResetToken` and saves it.
    4.  A password reset email with a unique link (containing the token) is sent via Nodemailer.
    5.  User clicks the link, navigating to `reset-password/[token]` page.
    6.  User enters new password and confirms.
    7.  Request sent to `/api/auth/reset-password`.
    8.  Server validates the token (existence, expiration).
    9.  New password is hashed and updated in the `User` table.
    10. The `ResetToken` is deleted.

### 4. Email Verification
*   **UI:** `src/app/auth/verify-email/page.tsx`
*   **API:**
    *   `src/app/api/auth/verify-email/route.ts` (GET)
    *   `src/app/api/auth/resend-verification/route.ts` (POST)
*   **Process:**
    1.  User clicks verification link from email, navigating to `verify-email?token=...`.
    2.  Client-side `useEffect` on `verify-email` page calls `/api/auth/verify-email`.
    3.  Server validates the `VerificationToken`.
    4.  `emailVerified` and `isVerified` fields are updated for the user.
    5.  The `VerificationToken` is deleted.
    6.  If verification fails or no token is present, the page allows the user to enter their email to resend the verification link via `/api/auth/resend-verification`.

### 5. Two-Factor Authentication (2FA)
*   **UI:** Integrated into `src/app/dashboard/profile/page.tsx`
*   **API:**
    *   `src/app/api/user/2fa/generate/route.ts` (GET)
    *   `src/app/api/user/2fa/verify/route.ts` (POST)
    *   `src/app/api/user/2fa/disable/route.ts` (POST)
*   **Process (Enable):**
    1.  User clicks "Enable 2FA" on profile page.
    2.  Client calls `/api/user/2fa/generate`.
    3.  Server generates a TOTP secret and QR code, saves the secret to `User.twoFactorSecret`.
    4.  QR code and secret are displayed to the user.
    5.  User scans QR code with authenticator app and enters the generated code.
    6.  Client calls `/api/user/2fa/verify` with the code.
    7.  Server validates the code using `otpauth`.
    8.  `User.twoFactorEnabled` is set to `true`.
*   **Process (Login with 2FA):**
    1.  User logs in with credentials.
    2.  If `User.twoFactorEnabled` is `true`, the login page prompts for a 2FA code.
    3.  User enters the code, which is sent with credentials to `signIn`.
    4.  `authorize` callback validates the 2FA code.
*   **Process (Disable):**
    1.  User clicks "Disable 2FA" on profile page.
    2.  Client calls `/api/user/2fa/disable`.
    3.  Server sets `User.twoFactorEnabled` to `false` and clears `twoFactorSecret` and `twoFactorRecoveryCodes`.

### 6. Session Invalidation
*   **UI:** Integrated into `src/app/dashboard/profile/page.tsx`
*   **API:** `src/app/api/user/invalidate-sessions/route.ts` (POST)
*   **Process:**
    1.  User clicks "Invalidate Other Sessions" button or changes password.
    2.  Client calls `/api/user/invalidate-sessions`.
    3.  Server deletes all `Session` records for the current user, except the current session. This logs the user out from all other devices.

### 7. User Profile Management
*   **UI:** `src/app/dashboard/profile/page.tsx`
*   **API:**
    *   `src/app/api/user/profile/route.ts` (GET, PUT)
    *   `src/app/api/user/change-password/route.ts` (PUT)
*   **Process:**
    1.  **Fetch Profile:** `GET` request to `/api/user/profile` retrieves user data.
    2.  **Update Profile:** `PUT` request to `/api/user/profile` updates name and email.
    3.  **Change Password:** `PUT` request to `/api/user/change-password` updates the user's password after verifying the current password and validating the new password's strength.

### 8. Role-Based Access Control (RBAC)
*   **Schema:** `User.role` field (enum `UserRole: FAN, CREATOR, ADMIN`).
*   **NextAuth.js Integration:** `role` is included in JWT and Session objects.
*   **API Authorization:** Example: `src/app/api/admin/route.ts` checks `session.user.role === UserRole.ADMIN` to restrict access.
*   **UI Conditional Rendering:** Example: `src/app/dashboard/page.tsx` conditionally renders content based on `session.user.role`.

## Security Considerations
*   **Password Hashing:** All passwords are hashed using `bcrypt.js` before storage.
*   **Password Strength Validation:** Enforced on both client and server sides.
*   **Token Expiration:** Verification and reset tokens have defined expiration times.
*   **Session Management:** Secure session handling via NextAuth.js, with explicit invalidation options.
*   **Environment Variables:** Sensitive information (API keys, secrets) are stored in environment variables.
*   **Error Handling:** Generic error messages are returned to prevent information leakage.

## Environment Variables
The following environment variables are required for the authentication system to function correctly:

*   `DATABASE_URL`: Connection string for your PostgreSQL database.
*   `AUTH_SECRET`: A long, random string used to sign and encrypt tokens. Generate with `openssl rand -base64 32`.
*   `NEXTAUTH_URL`: The base URL of your application (e.g., `http://localhost:3000`).
*   `AUTH_GITHUB_ID`: GitHub OAuth Client ID.
*   `AUTH_GITHUB_SECRET`: GitHub OAuth Client Secret.
*   `AUTH_GOOGLE_ID`: Google OAuth Client ID.
*   `AUTH_GOOGLE_SECRET`: Google OAuth Client Secret.
*   `AUTH_FACEBOOK_ID`: Facebook OAuth Client ID.
*   `AUTH_FACEBOOK_SECRET`: Facebook OAuth Client Secret.
*   `EMAIL_SERVER_HOST`: SMTP server host (e.g., `smtp.gmail.com`).
*   `EMAIL_SERVER_PORT`: SMTP server port (e.g., `587` or `465`).
*   `EMAIL_SERVER_SECURE`: `true` for SSL/TLS (port 465), `false` otherwise.
*   `EMAIL_SERVER_USER`: SMTP username.
*   `EMAIL_SERVER_PASSWORD`: SMTP password/app-specific password.
*   `EMAIL_FROM`: The email address from which verification/reset emails will be sent.

---
