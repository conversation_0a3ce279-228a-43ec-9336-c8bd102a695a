# 🌟 HVPPY Central

A revolutionary media content sharing and management platform for artists and content creators, featuring emotional-AI fan engagement, persona-based creator channels, and powerful content management tools.

## ✨ Unique Features

### 🧠 Emotional-AI Fan Engagement Engine
- Fans choose their mood and receive content matching their vibe
- AI-driven "MoodMatch" recommendations across emotions and interests
- Creators can tag content with emotional metadata

### 🎭 Persona-Based Creator Channels
- Multiple personas/identities under one creator account
- Each persona has its own feed, community, and content style
- Fans can follow specific personas (Singer vs. Producer vs. Storyteller)

### 🧪 Experimental Content Lab
- Test new content formats and get real-time fan feedback
- Interactive drops with voting to greenlight full releases
- Like Netflix previews meets Kickstarter for creative content

### 🧰 Content OS (Creator CMS Evolution)
- Drag-and-drop modular content creation
- Real-time collaborative editing
- AI suggestions for layout, thumbnails, and voiceovers

### 🌀 Vibe Timelines
- Visual storyline journeys through creator eras and chapters
- Dynamic timelines fans can scroll through
- Fan reactions to each "chapter" or era

### 💬 Fan Memories & Moments
- Save clips, lines, or moments as "Memory Cards"
- Creators can highlight top fan-generated memories
- Use for fan-generated content and collaborations

## 🚀 Tech Stack

- **Frontend**: Next.js 15 with App Router, React 19, TypeScript
- **UI Components**: Shadcn/UI with Tailwind CSS
- **Backend**: Appwrite (Auth, Database, Storage, Realtime)
- **Database**: Hybrid approach - Appwrite + PostgreSQL with Prisma
- **AI/ML**: Vercel AI SDK with OpenAI integration
- **Styling**: Tailwind CSS with custom HVPPY theme
- **Animations**: Framer Motion

## 📁 Project Structure

```
src/
├── app/                 # Next.js 15 App Router
│   ├── globals.css     # Global styles with HVPPY theme
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Homepage
├── components/         # Reusable UI components
│   ├── ui/            # Shadcn/UI components
│   └── providers/     # Context providers
├── lib/               # Utility libraries
│   ├── appwrite.ts    # Appwrite configuration
│   └── utils.ts       # Helper functions
├── types/             # TypeScript type definitions
└── hooks/             # Custom React hooks
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- Appwrite account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hvppy-central
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Fill in your environment variables:
   - Appwrite project credentials
   - PostgreSQL database URL
   - OpenAI API key

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push schema to database
   npm run db:push
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

## 🎯 MVP Roadmap

### Phase 1: Fan-Facing App
- [x] Project setup and architecture
- [ ] Core feed system with infinite scroll
- [ ] User profiles and creator pages
- [ ] MoodMatch content discovery
- [ ] Stories with vibe reactions
- [ ] Fan memories system
- [ ] Basic content upload

### Phase 2: Creator Studio
- [ ] Upload center with content timeline builder
- [ ] Persona management system
- [ ] AI tools (caption writer, mood tagger)
- [ ] Creator dashboard and analytics
- [ ] Real-time collaboration features

### Phase 3: Advanced Features
- [ ] Experimental content lab
- [ ] Localized culture capsules
- [ ] Gift-driven content unlocks
- [ ] Advanced AI recommendations
- [ ] Mobile app development

## 🎨 Design System

### Colors
- **Primary**: HVPPY Purple gradient (#ec5eff to #b821d1)
- **Moods**: 
  - Happy: #FFD700
  - Chill: #87CEEB  
  - Heartbroken: #DC143C
  - Inspired: #9370DB
  - Energetic: #FF6347
  - Peaceful: #98FB98

### Components
- Glass effect backgrounds
- Mood-based color coding
- Animated interactions
- Responsive design

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🌟 Vision

HVPPY Central aims to revolutionize how artists and fans connect through emotional intelligence, innovative content formats, and community-driven experiences. We're building the future of content sharing where every interaction is meaningful and every creator has the tools to build authentic relationships with their audience.

---

**Built with ❤️ for the creative community**
