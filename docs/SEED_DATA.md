# HVPPY Central Seed Data Documentation

This document describes the comprehensive seed data created for HVPPY Central, featuring realistic content with high-quality stock media.

## 🎯 Overview

The seed data creates a vibrant, realistic social network environment with:
- **8 diverse users** (6 creators, 2 fans)
- **6 creator profiles** with authentic social media presence
- **8 unique personas** representing different artistic sides
- **13 realistic posts** with stock media and engaging content
- **Authentic interactions** including reactions, memories, and follows
- **High-quality stock media** from Unsplash and free audio sources

## 👥 Users & Creators

### Creators

#### 1. <PERSON> (@melodic_maya)
- **Role**: Singer-songwriter
- **Genres**: Pop, Indie, Electronic, Singer-Songwriter
- **Location**: Los Angeles, CA
- **Personas**: 
  - <PERSON> the Dreamer (peaceful, nostalgic, inspired)
  - <PERSON> the Energizer (happy, energetic, excited)
- **Content**: Acoustic sessions, pop anthems, emotional ballads
- **Stats**: 12.5K followers, 450K total views

#### 2. <PERSON> (@beat_master_alex)
- **Role**: Producer & DJ
- **Genres**: Electronic, Hip-Hop, House, Techno
- **Location**: Miami, FL
- **Personas**:
  - <PERSON> the Producer (inspired, energetic)
  - <PERSON> the DJ (energetic, excited, happy)
- **Content**: Beat-making tutorials, live DJ sets, studio sessions
- **Stats**: 8.9K followers, 320K total views

#### 3. Luna Park (@luna_sounds)
- **Role**: Indie folk artist
- **Genres**: Indie Folk, Electronic, Ambient, Dream Pop
- **Location**: Portland, OR
- **Personas**:
  - Luna the Mystic (peaceful, nostalgic, inspired)
- **Content**: Ethereal soundscapes, nature-inspired music
- **Stats**: 15.2K followers, 280K total views

#### 4. Jay Williams (@rhythm_king_jay)
- **Role**: Drummer & educator
- **Genres**: Hip-Hop, Jazz Fusion, R&B, Neo-Soul
- **Location**: Atlanta, GA
- **Personas**:
  - Jay the Teacher (inspired, energetic)
- **Content**: Drum tutorials, rhythm breakdowns
- **Stats**: 6.7K followers, 180K total views

#### 5. Rose Martinez (@indie_rose)
- **Role**: Indie rock guitarist
- **Genres**: Indie Rock, Alternative, Grunge, Post-Punk
- **Location**: Seattle, WA
- **Personas**:
  - Rose the Rebel (energetic, inspired)
- **Content**: Raw indie rock, emotional guitar pieces
- **Stats**: 4.3K followers, 95K total views

#### 6. Emma Thompson (@melody_hunter)
- **Role**: Classical pianist
- **Genres**: Classical, Neo-Classical, Ambient, Cinematic
- **Location**: New York, NY
- **Personas**:
  - Emma the Virtuoso (peaceful, inspired, nostalgic)
- **Content**: Classical-electronic fusion, piano performances
- **Stats**: 9.8K followers, 220K total views

### Fans

#### 7. Sam Johnson (@music_lover_sam)
- **Role**: Music enthusiast & curator
- **Bio**: Discovering hidden gems daily, playlist master
- **Engagement**: Active reactor and memory creator

#### 8. Marcus Davis (@vibe_collector)
- **Role**: Music journalist & mood curator
- **Bio**: Collecting vibes from around the world
- **Engagement**: Cross-genre music discovery

## 🎭 Personas & Moods

Each creator has unique personas representing different artistic sides:

### Mood Categories
- **Peaceful**: Calm, meditative content
- **Nostalgic**: Reflective, memory-inducing pieces
- **Inspired**: Motivational, creative content
- **Happy**: Uplifting, joyful tracks
- **Energetic**: High-energy, exciting content
- **Excited**: Thrilling, anticipation-building pieces
- **Heartbroken**: Emotional, healing content
- **Chill**: Relaxed, laid-back vibes

## 📱 Content Types & Posts

### Music Posts (8 tracks)
1. **"Midnight Reflections"** - Maya Chen (Peaceful, Nostalgic)
2. **"Dance Floor Energy"** - Maya Chen (Happy, Energetic)
3. **"Forest Meditation"** - Luna Park (Peaceful, Nostalgic)
4. **"Moonlight Serenade"** - Luna Park (Peaceful, Nostalgic)
5. **"Raw Emotion"** - Rose Martinez (Heartbroken, Inspired)
6. **"Chopin Meets Electronica"** - Emma Thompson (Inspired, Peaceful)
7. **"When Words Aren't Enough"** - Maya Chen (Heartbroken, Peaceful)
8. **"Chill Sunday Mix"** - Alex Rodriguez (Chill, Peaceful)

### Video Posts (5 videos)
1. **"Coffee Shop Sessions"** - Maya Chen (Peaceful, Inspired)
2. **"Summer Anthem Preview"** - Maya Chen (Happy, Excited)
3. **"Studio Session Vibes"** - Alex Rodriguez (Inspired, Energetic)
4. **"Beat Making Tutorial: 808s"** - Alex Rodriguez (Inspired, Energetic)
5. **"Miami Sunset Set"** - Alex Rodriguez (Energetic, Excited)
6. **"Polyrhythm Breakdown"** - Jay Williams (Inspired, Energetic)

## 🖼️ Stock Media Sources

### High-Quality Avatars (Unsplash)
- Professional headshots with diverse representation
- 400x400 optimized for profile pictures
- Cropped and focused on faces for better recognition

### Music Cover Art (Unsplash)
- Abstract and musical imagery
- 800x800 square format for album covers
- Artistic and mood-appropriate visuals

### Video Thumbnails (Unsplash)
- Studio and performance imagery
- 800x600 aspect ratio for video previews
- Dynamic and engaging compositions

### Audio Samples
- Free audio samples for demonstration
- Placeholder URLs for realistic media integration
- Ready for replacement with actual content

## 💝 Interactions & Engagement

### Realistic Reactions
- **15 authentic reactions** across different content types
- Mood-matched reactions (peaceful content gets LOVE, energetic gets FIRE)
- Cross-creator support (artists supporting each other)
- Fan engagement patterns

### Fan Memories
- **5 meaningful memories** with personal stories
- Specific timestamps for memorable moments
- Emotional connections to content
- Real-world use cases (study music, workout motivation, meditation)

### Follow Relationships
- **15 follow connections** creating a realistic network
- Fans following multiple creators
- Mutual creator support and collaboration
- Organic community building

## 🚀 Usage Instructions

### Seeding the Database

```bash
# Via API (recommended)
curl -X POST http://localhost:3000/api/seed

# Or visit in browser (development only)
http://localhost:3000/api/seed
```

### Accessing Seeded Content

1. **Feed Pages**: Visit `/feed/discover` to see all content
2. **Creator Profiles**: Navigate to `/profile` to see user profiles
3. **Search**: Use `/search` to find creators and content
4. **Creator Studio**: Visit `/studio` for creator dashboard

### Testing Different Moods

- **Peaceful**: Maya's "Midnight Reflections", Luna's ambient pieces
- **Energetic**: Alex's DJ sets, Maya's dance tracks
- **Heartbroken**: Maya's emotional ballads
- **Inspired**: Tutorial content, behind-the-scenes videos
- **Chill**: Sunday mixes, relaxed content

## 🎨 Customization

### Adding More Content
1. Extend the `postData` array in `src/lib/seed-data.ts`
2. Add new stock media URLs to the constants
3. Create additional personas for existing creators
4. Add more user interactions and memories

### Updating Stock Media
1. Replace Unsplash URLs with your own media
2. Update the media constants at the top of the seed file
3. Ensure proper aspect ratios and file sizes
4. Test media loading and display

### Expanding User Base
1. Add more users to the `REALISTIC_USERS` array
2. Create corresponding creator profiles
3. Add personas for new creators
4. Generate content for new users

## 📊 Analytics & Metrics

The seed data includes realistic engagement metrics:
- **View counts**: Based on content quality and creator popularity
- **Like counts**: Proportional to views with realistic engagement rates
- **Share counts**: Lower than likes, following typical social media patterns
- **Memory counts**: Meaningful, personal connections to content
- **Follow relationships**: Organic network growth patterns

## 🔄 Regenerating Data

To refresh the seed data:
1. The seed process clears existing data first
2. Creates fresh content with updated timestamps
3. Maintains consistent user relationships
4. Updates engagement metrics based on new interactions

This comprehensive seed data provides a realistic foundation for testing and demonstrating HVPPY Central's unique mood-based social network features.
