You are an AI Development Assistant. Your mission is to architect and generate the entire codebase, documentation, and deployment pipeline for “HVPPY Central,” a next-generation media content sharing and management platform for artists and content creators. The platform has two primary faces:  

1. A **public-facing, TikTok-inspired social feed** where fans discover and engage with short-form and long-form media.  
2. A **creator-facing studio** that provides powerful content management, AI-assisted media generation, and live-streaming tools.  

---

### 1. Project Overview

- **Name:** HVPPY Central  
- **Primary Goals:**  
  - Enable creators to upload, schedule, AI-enhance, and monetize media.  
  - Give fans a hyper-personalized, mood-driven discovery feed and live-streaming experience.  
  - Incorporate unique differentiators: Emotional AI tagging (“VibeMatch”), Persona Channels, and Fan Memory Cards.  

---

### 2. Core Features & User Flows

#### A. Public-Facing Experience  
1. **Feed & Discovery**  
   - Infinite scroll of video/audio/image posts.  
   - “VibeMatch” filter: fans select a mood tag (happy, chill, epic, etc.) and see content tagged accordingly.  
   - Quick reactions (emoji + animated confetti).  

2. **Creator Profiles & Persona Channels**  
   - Multi-persona support: each user can toggle between “<PERSON>,” “Producer,” “Storyteller,” etc.  
   - Fans follow personas, not just accounts.  

3. **Live Streams**  
   - List of upcoming and active streams on homepage.  
   - Low-latency HLS/DASH playback with chat, polls, and gifting.  

4. **Memory Cards**  
   - Fans can clip 10-second moments from any stream or video and save them as “Memory Cards.”  
   - Public gallery of top fan cards per creator.  

#### B. Creator Studio  
1. **Content Composer**  
   - Drag-and-drop blocks: text, image, audio, video, poll, merchandise link.  
   - AI tools: auto-captioning, thumbnail generation, vibe tagging, short-form trailer cuts.  

2. **Scheduling & Batch Upload**  
   - Bulk upload interface with smart auto-split (e.g. 5-minute video → five 1-minute clips).  
   - Calendar view to schedule posts & streams.  

3. **Analytics & Monetization**  
   - Dashboards: views, vibes, fan retention heatmaps, revenue by content.  
   - Monetization options: subscriptions, pay-per-view, tips, NFT drops.  

4. **Persona & Plugin Marketplace**  
   - Plugin system for third-party AI filters or custom analytics widgets.  
   - Marketplace UI for browsing and installing plugins.  

---

### 3. Technical Requirements

- **Frontend**: Next.js (App Router), TypeScript, Tailwind CSS, Shadcn/UI  
- **Backend**: Node.js + tRPC (or GraphQL), TypeScript, Prisma ORM + PostgreSQL  
- **Auth**: NextAuth.js with multi-tenant support (JWT + OAuth)  
- **Media Storage & CDN**: S3-compatible (Cloudflare R2) + Cloudflare CDN  
- **Live-Streaming**: Integrate with Livepeer (or Mux/AWS IVS) for RTMP ingest and HLS playback  
- **Realtime**: Pusher or Socket.IO for chat, reactions, and polls  
- **AI Services**: OpenAI for text/video/audio processing; Replicate for any custom ML models  

---

### 4. Data Modeling (Prisma Schema Sketch)

```prisma
model User { /* id, name, email, avatarUrl, roles, personas, ... */ }
model Post { /* id, author→User, mediaUrl, mediaType, vibes[], createdAt */ }
model LiveStream { /* id, host→User, streamKey, playbackUrl, start/end times, isActive */ }
model MemoryCard { /* id, fan→User, sourcePost→Post or LiveStream, startSec, endSec */ }
model VibeTag { /* id, name, colorHex */ }
model Plugin { /* id, name, manifestUrl, author, installCount */ }
```

---

### 5. API Endpoints & Serverless Functions

* `GET /api/feed?vibes=[...]` → Returns a paginated, vibe-filtered list of posts
* `POST /api/posts` → Create a new post (with presigned upload URL)
* `POST /api/streams/:id/start` → Activate stream, return RTMP key
* `POST /api/streams/:id/clip` → Generate MemoryCard via serverless FFmpeg
* `WS /realtime` → Subscribe to chat, reactions, poll updates

---

### 6. UI/UX & Design System

* Use a **dark baseline theme** with bright accent tokens (HVPPY Pink `#FF3B81`, Vibe Blue `#00F0FF`).
* Component library: Bottom nav + swipeable full-screen cards (mobile), sidebar + modals (desktop).
* Provide both Figma design tokens and Tailwind config.

---

### 7. Deployment & CI/CD

* **Host**: Frontend and API on Vercel (Edge Functions for SSR, Serverless API routes)
* **Database**: Managed PostgreSQL (Neon or Supabase)
* **Streaming**: Host or subscribe to Livepeer nodes for RTMP ingestion
* **CI/CD**: GitHub Actions to run tests, linting, build, and deploy

---

### 8. Deliverables

1. **Complete monorepo** with `/apps/frontend`, `/apps/backend`, `/packages/ui`, `/packages/prisma`
2. **Prisma schema** and migrations for all models
3. **Next.js pages & components** for feed, profile, composer, live stream studio, and analytics
4. **Serverless functions** for media upload, stream control, and clipping
5. **Real-time chat server** and client integration
6. **Figma file** or design tokens for the UI kit
7. **End-to-end tests** covering key user flows
8. **Documentation**: README, architecture overview, API reference, deployment guide

---

**Instruction to the AI Assistant:**
“Break the work into iterative sprints of 1–2 weeks. For each sprint, generate code stubs, Docker/DevContainer configs, tests, and a summary of completed work plus next tasks. Ensure code is modular, well-typed (TypeScript), and follows best practices for security, performance, and accessibility. Provide PR-ready GitHub diffs and commit messages. Start by scaffolding the monorepo, Prisma schema, and basic Next.js app with authentication.”

```

---

**How to use this**: Paste the entire block (including the “You are an AI Development Assistant…” header) as a single prompt into your AI dev tool. It will then generate each piece—code, docs, design tokens, and deployment setups—in an organized, sprint-driven manner.
```
