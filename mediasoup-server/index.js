const express = require('express');
const https = require('https');
const fs = require('fs');
const mediasoup = require('mediasoup');
const WebSocket = require('ws');
const jwt = require('jsonwebtoken'); // Import jsonwebtoken
require('dotenv').config({ path: './.env' }); // Load environment variables
const { databases, ID } = require('./appwrite'); // Import Appwrite client

const app = express();
const port = 3001; // Mediasoup server port

// SSL/TLS certificates (for HTTPS and WSS)
const options = {
  key: fs.readFileSync('./certs/key.pem'),
  cert: fs.readFileSync('./certs/cert.pem')
};

const server = https.createServer(options, app);
const wss = new WebSocket.Server({ server });

let worker;
let router;
// Global variables for simplicity, in a real app manage per-client
let producerTransport;
let consumerTransport;
let producer;
let consumer;

// Store active streams and their associated user IDs
const activeStreams = new Map(); // Map<userId, { streamId: string, producerId: string }>

// Mediasoup Router settings
const mediaCodecs = [
  {
    kind: 'audio',
    mimeType: 'audio/opus',
    clockRate: 48000,
    channels: 2
  },
  {
    kind: 'video',
    mimeType: 'video/VP8',
    clockRate: 90000,
    parameters: {
      'x-google-start-bitrate': 1000
    }
  },
];

async function startMediasoup() {
  worker = await mediasoup.createWorker({
    logLevel: 'debug',
    logTags: [
      'info', 'ice', 'dtls', 'rtp', 'srtp', 'rtcp', 'rtx', 'bwe', 'score', 'simulcast', 'svc', 'sctp',
    ],
    rtcMinPort: 10000,
    rtcMaxPort: 10100,
  });

  worker.on('died', () => {
    console.error('Mediasoup worker died, exiting...');
    setTimeout(() => process.exit(1), 2000);
  });

  router = await worker.createRouter({ mediaCodecs });
  console.log('Mediasoup Worker and Router created.');
}

// WebSocket signaling
wss.on('connection', ws => {
  console.log('Client connected');
  let authenticatedUser = null; // Store authenticated user info for this WebSocket

  ws.on('message', async message => {
    const msg = JSON.parse(message);

    // First message must be authentication
    if (msg.type === 'authenticate') {
      try {
        const decoded = jwt.verify(msg.token, process.env.AUTH_SECRET);
        authenticatedUser = decoded;
        console.log(`Client authenticated: ${authenticatedUser.userId}`);
        ws.send(JSON.stringify({ type: 'authenticated', success: true }));
      } catch (error) {
        console.error('Authentication failed:', error.message);
        ws.send(JSON.stringify({ type: 'authenticated', success: false, error: 'Authentication failed' }));
        ws.close(); // Close connection if authentication fails
        return;
      }
    }

    if (!authenticatedUser) {
      // If not authenticated, ignore other messages
      console.warn('Received message from unauthenticated client:', msg.type);
      return;
    }

    switch (msg.type) {
      case 'getRouterRtpCapabilities':
        ws.send(JSON.stringify({ type: 'routerRtpCapabilities', data: router.rtpCapabilities }));
        break;

      case 'createWebRtcTransport':
        const { forceTcp, producing, consuming } = msg.data;
        const transport = await router.createWebRtcTransport({
          listenIps: [{ ip: '127.0.0.1', announcedIp: null }],
          enableUdp: true,
          enableTcp: true,
          preferUdp: true,
          enableSctp: true,
          initialAvailableOutgoingBitrate: 1000000,
        });

        transport.on('dtlsstatechange', dtlsState => {
          if (dtlsState === 'closed') {
            console.log('Transport DTLS state changed to closed');
            transport.close();
          }
        });

        transport.on('close', () => {
          console.log('Transport closed');
        });

        if (producing) producerTransport = transport;
        if (consuming) consumerTransport = transport;

        ws.send(JSON.stringify({ type: 'webRtcTransportCreated', data: { id: transport.id, iceParameters: transport.iceParameters, iceCandidates: transport.iceCandidates, dtlsParameters: transport.dtlsParameters } }));
        break;

      case 'connectWebRtcTransport':
        const { transportId, dtlsParameters } = msg.data;
        const connectTransport = producerTransport.id === transportId ? producerTransport : consumerTransport;
        await connectTransport.connect({ dtlsParameters });
        ws.send(JSON.stringify({ type: 'webRtcTransportConnected' }));
        break;

      case 'produce':
        const { kind, rtpParameters } = msg.data;
        producer = await producerTransport.produce({ kind, rtpParameters });

        // Store stream metadata in Appwrite
        try {
          const liveStreamDoc = await databases.createDocument(
            process.env.APPWRITE_DATABASE_ID,
            process.env.APPWRITE_COLLECTION_ID_LIVESTREAMS,
            ID.unique(),
            {
              userId: authenticatedUser.userId,
              streamId: producer.id, // Use producer ID as stream ID for simplicity
              status: 'live',
              title: `${authenticatedUser.name || authenticatedUser.userId}'s Live Stream`,
              description: 'A live stream from HVPPY Central',
              startedAt: new Date().toISOString(),
            }
          );
          activeStreams.set(authenticatedUser.userId, { streamId: liveStreamDoc.$id, producerId: producer.id });
          console.log('Live stream started in Appwrite:', liveStreamDoc.$id);
        } catch (appwriteError) {
          console.error('Error creating live stream document in Appwrite:', appwriteError);
          // Decide how to handle this: close producer, send error to client, etc.
        }

        ws.send(JSON.stringify({ type: 'producerCreated', data: { id: producer.id } }));
        break;

      case 'consume':
        const { rtpCapabilities } = msg.data;
        if (producer && router.canConsume({ producerId: producer.id, rtpCapabilities })) {
          consumer = await consumerTransport.consume({
            producerId: producer.id,
            rtpCapabilities,
            paused: producer.kind === 'video',
          });

          ws.send(JSON.stringify({ type: 'consumerCreated', data: { id: consumer.id, producerId: producer.id, kind: consumer.kind, rtpParameters: consumer.rtpParameters, type: consumer.type, sctpParameters: consumer.sctpParameters, paused: consumer.paused } }));
        } else {
          ws.send(JSON.stringify({ type: 'consumerCreated', data: null }));
        }
        break;

      case 'resumeConsumer':
        await consumer.resume();
        ws.send(JSON.stringify({ type: 'consumerResumed' }));
        break;

      default:
        console.warn('Unknown message type:', msg.type);
    }
  });

  ws.on('close', async () => {
    console.log('Client disconnected');
    if (producer) producer.close();
    if (consumer) consumer.close();
    if (producerTransport) producerTransport.close();
    if (consumerTransport) consumerTransport.close();

    // Update stream status in Appwrite if this client was a producer
    if (authenticatedUser && activeStreams.has(authenticatedUser.userId)) {
      const { streamId } = activeStreams.get(authenticatedUser.userId);
      try {
        await databases.updateDocument(
          process.env.APPWRITE_DATABASE_ID,
          process.env.APPWRITE_COLLECTION_ID_LIVESTREAMS,
          streamId,
          {
            status: 'offline',
            endedAt: new Date().toISOString(),
          }
        );
        activeStreams.delete(authenticatedUser.userId);
        console.log('Live stream ended in Appwrite:', streamId);
      } catch (appwriteError) {
        console.error('Error updating live stream document in Appwrite on close:', appwriteError);
      }
    }
  });

  ws.on('error', error => {
    console.error('WebSocket error:', error);
  });
});

// Start the server
server.listen(port, async () => {
  console.log(`Mediasoup server listening on https://localhost:${port}`);
  await startMediasoup();
});