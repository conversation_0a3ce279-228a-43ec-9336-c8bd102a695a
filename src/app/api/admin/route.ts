import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { UserRole } from "@prisma/client";

export async function GET() {
  const session = await auth();

  if (!session || !session.user || session.user.role !== UserRole.ADMIN) {
    return new NextResponse("Forbidden", { status: 403 });
  }

  return NextResponse.json({ message: "Welcome, Admin! This is a protected route." });
}
