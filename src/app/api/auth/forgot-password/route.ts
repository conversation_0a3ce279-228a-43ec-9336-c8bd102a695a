import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import crypto from "crypto";
import { sendEmail } from "@/lib/email";

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email) {
      return new NextResponse("Email is required", { status: 400 });
    }

    const user = await prisma.user.findUnique({ where: { email } });

    if (!user) {
      // For security, don't reveal if the email doesn't exist
      return new NextResponse("If an account with that email exists, a password reset link has been sent.", { status: 200 });
    }

    const token = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 3600 * 1000); // 1 hour from now

    await prisma.resetToken.create({
      data: {
        token,
        userId: user.id,
        expiresAt,
      },
    });

    const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password/${token}`;

    await sendEmail({
      to: email,
      subject: "Password Reset for HVPPY Central",
      html: `
        <p>Hello,</p>
        <p>You have requested a password reset for your HVPPY Central account. Please click the link below to reset your password:</p>
        <p><a href="${resetUrl}">Reset Password</a></p>
        <p>If you did not request a password reset, please ignore this email.</p>
      `,
    });

    return new NextResponse("If an account with that email exists, a password reset link has been sent.", { status: 200 });
  } catch (error) {
    console.error("Forgot password error:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}