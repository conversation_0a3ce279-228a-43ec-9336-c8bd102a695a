import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import crypto from "crypto";
import { sendEmail } from "@/lib/email";

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email) {
      return new NextResponse("Email is required", { status: 400 });
    }

    const user = await prisma.user.findUnique({ where: { email } });

    if (!user) {
      // For security, don't reveal if the email doesn't exist
      return new NextResponse("If an account with that email exists, a new verification link has been sent.", { status: 200 });
    }

    if (user.emailVerified) {
      return new NextResponse("<PERSON>ail is already verified.", { status: 400 });
    }

    // Delete any existing verification tokens for this user
    await prisma.verificationToken.deleteMany({
      where: { identifier: email },
    });

    // Generate and save new verification token
    const verificationToken = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 24 * 3600 * 1000); // 24 hours from now

    await prisma.verificationToken.create({
      data: {
        identifier: user.email as string,
        token: verificationToken,
        expires: expiresAt,
      },
    });

    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}`;

    await sendEmail({
      to: email,
      subject: "Verify your email for HVPPY Central",
      html: `
        <p>Hello ${user.name || user.email},</p>
        <p>You recently requested a new email verification link for your HVPPY Central account. Please click the link below to verify your email address:</p>
        <p><a href="${verificationUrl}">Verify Email</a></p>
        <p>If you did not request this, please ignore this email.</p>
      `,
    });

    return new NextResponse("If an account with that email exists, a new verification link has been sent.", { status: 200 });
  } catch (error) {
    console.error("Resend verification email error:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
