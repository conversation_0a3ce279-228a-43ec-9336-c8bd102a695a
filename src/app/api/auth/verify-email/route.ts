import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const token = searchParams.get("token");

    if (!token) {
      return new NextResponse("Missing token", { status: 400 });
    }

    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token },
    });

    if (!verificationToken || verificationToken.expires < new Date()) {
      return new NextResponse("Invalid or expired token", { status: 400 });
    }

    const user = await prisma.user.update({
      where: { email: verificationToken.identifier },
      data: { emailVerified: new Date(), isVerified: true },
    });

    await prisma.verificationToken.delete({ where: { token } });

    return new NextResponse("Email verified successfully", { status: 200 });
  } catch (error) {
    console.error("Email verification error:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
