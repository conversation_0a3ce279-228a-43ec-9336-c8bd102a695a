import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ModerationStatus, TranscodingStatus } from "@prisma/client";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '0');
    const limit = parseInt(searchParams.get('limit') || '10');
    const moods = searchParams.get('moods')?.split(',').filter(Boolean) || [];
    const contentTypes = searchParams.get('contentTypes')?.split(',').filter(Boolean) || [];

    const skip = page * limit;

    // Build where clause
    const where: any = {
      isPublic: true,
      moderationStatus: ModerationStatus.APPROVED,
      transcodingStatus: TranscodingStatus.COMPLETED,
    };

    if (moods.length > 0) {
      where.moods = {
        hasSome: moods,
      };
    }

    if (contentTypes.length > 0) {
      where.contentType = {
        in: contentTypes,
      };
    }

    const posts = await prisma.post.findMany({
      where,
      orderBy: [
        { publishedAt: "desc" },
        { createdAt: "desc" },
      ],
      skip,
      take: limit,
      include: {
        user: { 
          select: { 
            id: true,
            name: true, 
            email: true, 
            image: true,
            username: true,
            displayName: true,
            role: true,
            isVerified: true,
            createdAt: true,
            updatedAt: true,
          } 
        },
        creator: { 
          select: { 
            id: true,
            stageName: true,
            genre: true,
            totalFollowers: true,
            totalViews: true,
            totalLikes: true,
            createdAt: true,
            updatedAt: true,
          } 
        },
        reactions: {
          select: {
            id: true,
            userId: true,
            type: true,
            mood: true,
            createdAt: true,
          },
        },
        memories: {
          select: {
            id: true,
            userId: true,
            createdAt: true,
          },
        },
        comments: {
          select: {
            id: true,
            userId: true,
            content: true,
            createdAt: true,
            user: {
              select: {
                name: true,
                image: true,
                displayName: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 3, // Only get latest 3 comments for feed
        },
      },
    });

    // Transform posts to match FeedItem format
    const feedItems = posts.map((post) => ({
      id: post.id,
      post: {
        ...post,
        user: {
          ...post.user,
          appwriteId: post.user.id, // Use Prisma ID as appwriteId for now
          avatarUrl: post.user.image,
        },
        creator: post.creator ? {
          ...post.creator,
          userId: post.userId,
          user: post.user,
          name: post.creator.stageName,
          avatarUrl: post.user.image,
          personas: [], // TODO: Add personas if needed
        } : undefined,
        description: post.content,
      },
      score: Math.random() * 100, // TODO: Implement proper scoring algorithm
      reason: 'discover',
    }));

    return NextResponse.json({ posts: feedItems });
  } catch (error) {
    console.error("Error fetching discovery feed:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
