import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { ModerationStatus, TranscodingStatus } from "@prisma/client";

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Find all users that the current user is following
    const following = await prisma.follow.findMany({
      where: { followerId: session.user.id as string },
      select: { followingId: true },
    });

    const followedUserIds = following.map((f) => f.followingId);

    // Find creators associated with these followed users
    const followedCreators = await prisma.creator.findMany({
      where: { userId: { in: followedUserIds } },
      select: { id: true },
    });

    const followedCreatorIds = followedCreators.map((c) => c.id);

    // Fetch posts from these followed creators that are approved and transcoded
    const posts = await prisma.post.findMany({
      where: {
        creatorId: { in: followedCreatorIds },
        isPublic: true,
        moderationStatus: ModerationStatus.APPROVED,
        transcodingStatus: TranscodingStatus.COMPLETED,
      },
      orderBy: {
        publishedAt: "desc",
      },
      include: {
        user: { select: { name: true, email: true, image: true } }, // Include user details
        creator: { select: { stageName: true } }, // Include creator details
      },
    });

    return NextResponse.json({ posts });
  } catch (error) {
    console.error("Error fetching personalized feed:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
