import { NextResponse } from "next/server";
import { databases } from "@/lib/appwrite";
import { Query } from "appwrite";

export async function GET() {
  try {
    const response = await databases.listDocuments(
      process.env.APPWRITE_DATABASE_ID as string,
      process.env.APPWRITE_COLLECTION_ID_LIVESTREAMS as string,
      [Query.equal("status", "live")] // Only fetch live streams
    );
    return NextResponse.json({ streams: response.documents });
  } catch (error) {
    console.error("Error listing live streams from Appwrite:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
