import { NextResponse } from "next/server";
import { storage } from "@/lib/appwrite";

export async function GET() {
  try {
    const files = await storage.listFiles(process.env.APPWRITE_BUCKET_ID as string);
    return NextResponse.json({ files: files.files });
  } catch (error) {
    console.error("Error listing files from Appwrite:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
