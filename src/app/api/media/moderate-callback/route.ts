import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ModerationStatus } from "@prisma/client";

export async function POST(req: Request) {
  try {
    // Authenticate the callback request (e.g., using an API key)
    const apiKey = req.headers.get("X-API-Key");
    if (apiKey !== process.env.MODERATION_SERVICE_API_KEY) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId, status, notes, message } = await req.json();

    if (!postId || !status) {
      return new NextResponse("Missing postId or status", { status: 400 });
    }

    // Update the Post record based on the callback status
    await prisma.post.update({
      where: { id: postId },
      data: {
        moderationStatus: status as ModerationStatus,
        moderationNotes: notes || undefined,
      },
    });

    console.log(`Moderation callback received for postId: ${postId}, Status: ${status}, Message: ${message}`);

    return new NextResponse("Callback received", { status: 200 });
  } catch (error) {
    console.error("Error processing moderation callback:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
