import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ModerationStatus } from "@prisma/client";
import { sendModerationJob } from "@/lib/moderation-service";

export async function POST(req: Request) {
  try {
    const { postId } = await req.json();

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    // Update the post status to PENDING (or IN_REVIEW)
    const post = await prisma.post.update({
      where: { id: postId },
      data: { moderationStatus: ModerationStatus.PENDING },
      select: { contentUrl: true }, // Select contentUrl to pass to moderation service
    });

    if (!post || !post.contentUrl) {
      return new NextResponse("Post not found or missing content URL", { status: 404 });
    }

    const originalFileUrl = `${process.env.NEXTAUTH_URL}/api/media/stream/${post.contentUrl}`;
    const callbackUrl = `${process.env.NEXTAUTH_URL}/api/media/moderate-callback`;

    // Trigger the simulated external moderation service
    sendModerationJob({
      postId: postId,
      contentUrl: originalFileUrl,
      callbackUrl: callbackUrl,
    });

    return new NextResponse("Moderation process initiated", { status: 200 });
  } catch (error) {
    console.error("Error initiating moderation:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}