import { NextResponse } from "next/server";
import { storage } from "@/lib/appwrite";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await params;

    if (!fileId) {
      return new NextResponse("File ID is required", { status: 400 });
    }

    const filePreview = storage.getFileView(
      process.env.APPWRITE_BUCKET_ID as string, // Your Appwrite bucket ID
      fileId
    );

    // Redirect to the Appwrite file preview URL
    return NextResponse.redirect(filePreview);
  } catch (error) {
    console.error("Error streaming file from Appwrite:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
