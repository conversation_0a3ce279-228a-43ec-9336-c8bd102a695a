import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { TranscodingStatus } from "@prisma/client";

export async function POST(req: Request) {
  try {
    // Authenticate the callback request (e.g., using an API key)
    const apiKey = req.headers.get("X-API-Key");
    if (apiKey !== process.env.TRANSCODING_SERVICE_API_KEY) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId, status, renditions, message } = await req.json();

    if (!postId || !status) {
      return new NextResponse("Missing postId or status", { status: 400 });
    }

    // Update the Post record based on the callback status
    await prisma.post.update({
      where: { id: postId },
      data: {
        transcodingStatus: status as TranscodingStatus,
        renditions: renditions || undefined, // Store renditions if provided
      },
    });

    console.log(`Transcoding callback received for postId: ${postId}, Status: ${status}, Message: ${message}`);

    return new NextResponse("Callback received", { status: 200 });
  } catch (error) {
    console.error("Error processing transcoding callback:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
