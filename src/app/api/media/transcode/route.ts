import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { TranscodingStatus } from "@prisma/client";
import { sendTranscodingJob } from "@/lib/transcoding-service";

export async function POST(req: Request) {
  try {
    const { postId } = await req.json();

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    // Update the post status to IN_PROGRESS
    const post = await prisma.post.update({
      where: { id: postId },
      data: { transcodingStatus: TranscodingStatus.IN_PROGRESS },
      select: { contentUrl: true }, // Select contentUrl to pass to transcoding service
    });

    if (!post || !post.contentUrl) {
      return new NextResponse("Post not found or missing content URL", { status: 404 });
    }

    const originalFileUrl = `${process.env.NEXTAUTH_URL}/api/media/stream/${post.contentUrl}`;
    const callbackUrl = `${process.env.NEXTAUTH_URL}/api/media/transcode-callback`;

    // Trigger the simulated external transcoding service
    sendTranscodingJob({
      postId: postId,
      originalFileUrl: originalFileUrl,
      callbackUrl: callbackUrl,
    });

    return new NextResponse("Transcoding process initiated", { status: 200 });
  } catch (error) {
    console.error("Error initiating transcoding:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}