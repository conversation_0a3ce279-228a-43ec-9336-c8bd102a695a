import { NextResponse } from "next/server";
import { storage } from "@/lib/appwrite";
import { ID } from "appwrite";
import { prisma } from "@/lib/prisma"; // Import prisma client
import { ContentType, TranscodingStatus, ModerationStatus } from "@prisma/client"; // Import enums
import { auth } from "@/lib/auth"; // Import auth to get session

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return new NextResponse("No file uploaded", { status: 400 });
    }

    // Determine content type based on MIME type
    let contentType: ContentType = ContentType.TEXT; // Default
    if (file.type.startsWith("video/")) {
      contentType = ContentType.VIDEO;
    } else if (file.type.startsWith("image/")) {
      contentType = ContentType.IMAGE;
    } else if (file.type.startsWith("audio/")) {
      contentType = ContentType.MUSIC;
    }

    // Upload file to Appwrite Storage
    const uploadedFile = await storage.createFile(
      process.env.APPWRITE_BUCKET_ID as string, // Your Appwrite bucket ID
      ID.unique(),
      file
    );

    // Create a Post entry in Prisma
    const post = await prisma.post.create({
      data: {
        userId: session.user.id as string,
        content: file.name, // Use file name as content for now
        contentType: contentType,
        contentUrl: uploadedFile.$id, // Store Appwrite file ID
        mediaUrls: [uploadedFile.$id], // Store Appwrite file ID
        transcodingStatus: TranscodingStatus.PENDING,
        moderationStatus: ModerationStatus.PENDING, // Set initial moderation status
        title: file.name, // Use file name as title for now
      },
    });

    // Trigger transcoding process (async)
    fetch(`${process.env.NEXTAUTH_URL}/api/media/transcode`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ postId: post.id }),
    }).catch(console.error);

    // Trigger moderation process (async)
    fetch(`${process.env.NEXTAUTH_URL}/api/media/moderate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ postId: post.id }),
    }).catch(console.error);

    return NextResponse.json({ fileId: uploadedFile.$id, bucketId: uploadedFile.bucketId, postId: post.id }, { status: 201 });
  } catch (error) {
    console.error("Error uploading file to Appwrite or creating post:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
