import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import jwt from "jsonwebtoken";

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Create a short-lived JWT for the Mediasoup server
    const token = jwt.sign(
      {
        userId: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
      },
      process.env.AUTH_SECRET as string, // Use the same AUTH_SECRET as NextAuth.js
      { expiresIn: "1h" } // Token valid for 1 hour
    );

    return NextResponse.json({ token });
  } catch (error) {
    console.error("Error generating Mediasoup token:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
