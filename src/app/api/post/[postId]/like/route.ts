import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function POST(req: Request, { params }: { params: { postId: string } }) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId } = params;

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    // Check if the post exists
    const post = await prisma.post.findUnique({ where: { id: postId } });
    if (!post) {
      return new NextResponse("Post not found", { status: 404 });
    }

    // Check if already liked
    const existingLike = await prisma.like.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    });

    if (existingLike) {
      return new NextResponse("Post already liked", { status: 409 });
    }

    // Create the like record
    await prisma.like.create({
      data: {
        userId: session.user.id as string,
        postId: postId,
      },
    });

    // Increment like count on the post
    await prisma.post.update({
      where: { id: postId },
      data: { likeCount: { increment: 1 } },
    });

    return new NextResponse("Post liked successfully", { status: 200 });
  } catch (error) {
    console.error("Error liking post:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: { postId: string } }) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { postId } = params;

    if (!postId) {
      return new NextResponse("Post ID is required", { status: 400 });
    }

    // Check if the like exists
    const existingLike = await prisma.like.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    });

    if (!existingLike) {
      return new NextResponse("Post not liked by this user", { status: 404 });
    }

    // Delete the like record
    await prisma.like.delete({
      where: {
        userId_postId: {
          userId: session.user.id as string,
          postId: postId,
        },
      },
    });

    // Decrement like count on the post
    await prisma.post.update({
      where: { id: postId },
      data: { likeCount: { decrement: 1 } },
    });

    return new NextResponse("Post unliked successfully", { status: 200 });
  } catch (error) {
    console.error("Error unliking post:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
