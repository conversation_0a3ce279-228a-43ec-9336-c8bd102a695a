import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth"; // Import auth

export async function GET(req: Request) {
  try {
    const session = await auth(); // Get the current session
    const userId = session?.user?.id; // Get the current user's ID

    const posts = await prisma.post.findMany({
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        title: true,
        content: true,
        contentType: true,
        contentUrl: true,
        mediaUrls: true,
        thumbnailUrl: true,
        transcodingStatus: true,
        renditions: true,
        createdAt: true,
        likeCount: true, // Include likeCount
        likes: userId ? { // Conditionally include likes if user is logged in
          where: { userId: userId as string },
          select: { userId: true },
        } : false,
      },
    });

    // Map posts to include hasLiked property
    const postsWithLikeStatus = posts.map(post => ({
      ...post,
      hasLiked: post.likes ? post.likes.length > 0 : false,
      likes: undefined, // Remove the raw likes array from the response
    }));

    return NextResponse.json({ posts: postsWithLikeStatus });
  } catch (error) {
    console.error("Error fetching posts:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}