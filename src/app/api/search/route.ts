import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ModerationStatus, TranscodingStatus } from "@prisma/client";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get("q") || "";

    if (!query) {
      return NextResponse.json({ users: [], creators: [], posts: [] });
    }

    const searchKeyword = `%${query.toLowerCase()}%`;

    // Search Users
    const users = await prisma.user.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { username: { contains: query, mode: "insensitive" } },
          { displayName: { contains: query, mode: "insensitive" } },
          { bio: { contains: query, mode: "insensitive" } },
        ],
      },
      select: {
        id: true,
        name: true,
        username: true,
        displayName: true,
        avatar: true,
        role: true,
      },
      take: 10, // Limit results
    });

    // Search Creators
    const creators = await prisma.creator.findMany({
      where: {
        OR: [
          { stageName: { contains: query, mode: "insensitive" } },
          { genre: { hasSome: [query] } }, // Basic search for genre
          { location: { contains: query, mode: "insensitive" } },
        ],
      },
      include: {
        user: { select: { id: true, name: true, username: true, displayName: true, avatar: true } },
      },
      take: 10, // Limit results
    });

    // Search Posts
    const posts = await prisma.post.findMany({
      where: {
        isPublic: true,
        moderationStatus: ModerationStatus.APPROVED,
        transcodingStatus: TranscodingStatus.COMPLETED,
        OR: [
          { title: { contains: query, mode: "insensitive" } },
          { content: { contains: query, mode: "insensitive" } },
        ],
      },
      select: {
        id: true,
        title: true,
        content: true,
        contentType: true,
        contentUrl: true,
        thumbnailUrl: true,
        user: { select: { name: true, username: true, displayName: true } },
        creator: { select: { stageName: true } },
      },
      take: 10, // Limit results
    });

    return NextResponse.json({ users, creators, posts });
  } catch (error) {
    console.error("Error during search:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
