import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Disable 2FA for the user and clear the secret
    await prisma.user.update({
      where: { id: session.user.id as string },
      data: {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorRecoveryCodes: [],
      },
    });

    return new NextResponse("2FA disabled successfully", { status: 200 });
  } catch (error) {
    console.error("Error disabling 2FA:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
