import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { OTPAuth } from "otpauth";
import qrcode from "qrcode";

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
    });

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Generate a new secret if one doesn't exist
    let secret = user.twoFactorSecret;
    if (!secret) {
      const otpauth = new OTPAuth({
        secret: OTPAuth.Secret.randomBytes(20),
      });
      secret = otpauth.secret.base32;

      await prisma.user.update({
        where: { id: user.id },
        data: { twoFactorSecret: secret },
      });
    }

    const otpauth = new OTPAuth({
      secret: OTPAuth.Secret.fromBase32(secret),
      issuer: "HVPPY Central",
      label: user.email || user.name || "User",
      algorithm: "SHA1",
      digits: 6,
      period: 30,
    });

    const otpauthUrl = otpauth.toString();
    const qrCodeImage = await qrcode.toDataURL(otpauthUrl);

    return NextResponse.json({ secret, qrCodeImage });
  } catch (error) {
    console.error("Error generating 2FA secret:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
