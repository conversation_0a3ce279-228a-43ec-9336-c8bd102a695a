import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { OTPAuth } from "otpauth";

export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { token } = await req.json();

    if (!token) {
      return new NextResponse("Missing token", { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
    });

    if (!user || !user.twoFactorSecret) {
      return new NextResponse("2FA not set up for this user", { status: 400 });
    }

    const otpauth = new OTPAuth({
      secret: OTPAuth.Secret.fromBase32(user.twoFactorSecret),
      algorithm: "SHA1",
      digits: 6,
      period: 30,
    });

    const delta = otpauth.validate({
      token,
      window: 1, // Allow for a 30-second window either side of the current time
    });

    if (delta === null) {
      return new NextResponse("Invalid 2FA token", { status: 401 });
    }

    // Enable 2FA for the user
    await prisma.user.update({
      where: { id: user.id },
      data: { twoFactorEnabled: true },
    });

    return new NextResponse("2FA enabled successfully", { status: 200 });
  } catch (error) {
    console.error("Error verifying 2FA token:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
