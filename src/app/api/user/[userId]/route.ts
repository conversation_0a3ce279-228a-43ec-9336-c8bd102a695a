import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { ModerationStatus, TranscodingStatus } from "@prisma/client";

export async function GET(req: Request, { params }: { params: { userId: string } }) {
  try {
    const { userId } = params;
    const session = await auth();

    if (!userId) {
      return new NextResponse("User ID is required", { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        username: true,
        displayName: true,
        bio: true,
        avatar: true,
        role: true,
        isVerified: true,
        creator: {
          select: {
            id: true,
            stageName: true,
            genre: true,
            location: true,
            website: true,
            socialLinks: true,
          },
        },
        posts: {
          where: {
            isPublic: true,
            moderationStatus: ModerationStatus.APPROVED,
            transcodingStatus: TranscodingStatus.COMPLETED,
          },
          orderBy: {
            publishedAt: "desc",
          },
          select: {
            id: true,
            title: true,
            content: true,
            contentType: true,
            contentUrl: true,
            mediaUrls: true,
            thumbnailUrl: true,
            transcodingStatus: true,
            renditions: true,
            moderationStatus: true,
            moderationNotes: true,
            createdAt: true,
          },
        },
      },
    });

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    let isFollowing = false;
    if (session?.user?.id && session.user.id !== userId) {
      const follow = await prisma.follow.findUnique({
        where: {
          followerId_followingId: {
            followerId: session.user.id as string,
            followingId: userId,
          },
        },
      });
      isFollowing = !!follow;
    }

    // Remove sensitive data before sending to client
    const { email: userEmail, ...publicUser } = user; // Destructure to omit email

    return NextResponse.json({ ...publicUser, isFollowing });
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
