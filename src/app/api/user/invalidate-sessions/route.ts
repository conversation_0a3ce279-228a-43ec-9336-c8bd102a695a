import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function POST() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Delete all sessions for the current user except the current one
    await prisma.session.deleteMany({
      where: {
        userId: session.user.id as string,
        NOT: { sessionToken: session.sessionToken }, // Keep the current session active
      },
    });

    return new NextResponse("Other sessions invalidated successfully", { status: 200 });
  } catch (error) {
    console.error("Error invalidating sessions:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
