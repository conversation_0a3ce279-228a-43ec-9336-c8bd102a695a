import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
      select: { id: true, name: true, email: true, image: true, emailVerified: true, isVerified: true, username: true, displayName: true, bio: true, avatar: true, role: true },
    });

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function PUT(req: Request) {
  try {
    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { name, email } = await req.json();

    if (!name || !email) {
      return new NextResponse("Missing fields", { status: 400 });
    }

    // Check if email is already taken by another user
    if (email !== session.user.email) {
      const existingUserWithEmail = await prisma.user.findUnique({ where: { email } });
      if (existingUserWithEmail && existingUserWithEmail.id !== session.user.id) {
        return new NextResponse("Email already in use by another account", { status: 409 });
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id as string },
      data: {
        name,
        email,
      },
      select: { id: true, name: true, email: true, image: true, emailVerified: true, isVerified: true, username: true, displayName: true, bio: true, avatar: true, role: true },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("Error updating user profile:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
