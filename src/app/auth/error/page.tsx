"use client";

import { useSearchParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const errorMessage = {
    Signin: "You are not authorized to sign in.",
    OAuthSignin: "There was an error with the OAuth provider.",
    OAuthCallback: "There was an error with the OAuth callback.",
    OAuthCreateAccount: "Could not create account with OAuth provider.",
    EmailCreateAccount: "Could not create account with email.",
    Callback: "There was an error with the callback.",
    OAuthAccountNotLinked: "This account is already linked to another user.",
    EmailSignin: "Check your email for the sign-in link.",
    CredentialsSignin: "Invalid credentials. Please try again.",
    default: "An unknown error occurred.",
  }[error as keyof typeof errorMessage] || errorMessage.default;

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg overflow-hidden text-center">
        <CardHeader className="space-y-2 p-6 pb-4">
          <CardTitle className="text-3xl font-extrabold text-red-600 dark:text-red-400">Authentication Error</CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">Something went wrong during authentication.</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4 p-6 pt-0">
          <p className="text-base text-gray-700 dark:text-gray-300 font-medium">{errorMessage}</p>
        </CardContent>
        <CardFooter className="flex justify-center p-6 pt-0">
          <Button asChild className="py-2 text-lg">
            <Link href="/auth/login">Try Again</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
