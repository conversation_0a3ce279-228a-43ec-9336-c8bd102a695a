"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        toast.success("If an account with that email exists, a password reset link has been sent.");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to send reset link.");
      }
    } catch (error) {
      console.error("Forgot password request error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg overflow-hidden">
        <CardHeader className="space-y-2 p-6 pb-4">
          <CardTitle className="text-3xl font-extrabold text-center text-gray-900 dark:text-gray-50">Forgot Password</CardTitle>
          <CardDescription className="text-center text-gray-600 dark:text-gray-400">Enter your email to receive a password reset link</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-6 p-6 pt-0">
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="<EMAIL>" value={email} onChange={(e) => setEmail(e.target.value)} disabled={isLoading} />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col gap-4 p-6 pt-0">
          <Button className="w-full py-2 text-lg" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Sending..." : "Send Reset Link"}
          </Button>
          <p className="text-sm text-center text-gray-500 dark:text-gray-400">
            Remember your password?{" "}
            <Link href="/auth/login" className="text-primary hover:underline">
              Sign In
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}