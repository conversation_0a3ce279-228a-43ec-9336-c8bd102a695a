"use client";

import { signIn } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showTwoFactorInput, setShowTwoFactorInput] = useState(false);
  const [pendingUser, setPendingUser] = useState<{ email: string } | null>(null);

  const handleCredentialSignIn = async () => {
    setIsLoading(true);
    const result = await signIn("credentials", {
      redirect: false,
      email,
      password,
      twoFactorCode: twoFactorCode || undefined, // Pass 2FA code if available
    });
    setIsLoading(false);

    if (result?.error) {
      if (result.error === "TwoFactorRequired") {
        setShowTwoFactorInput(true);
        setPendingUser({ email });
        toast.info("Two-factor authentication required. Please enter your code.");
      } else {
        toast.error(result.error);
      }
    } else if (result?.ok) {
      toast.success("Signed in successfully!");
      window.location.href = "/dashboard"; // Redirect manually after successful login
    }
  };

  const handleOAuthSignIn = async (provider: string) => {
    setIsLoading(true);
    await signIn(provider, { callbackUrl: "/dashboard" });
    // setIsLoading(false); // signIn will redirect, so no need to set loading to false here
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg overflow-hidden">
        <CardHeader className="space-y-2 p-6 pb-4">
          <CardTitle className="text-3xl font-extrabold text-center text-gray-900 dark:text-gray-50">Sign In</CardTitle>
          <CardDescription className="text-center text-gray-600 dark:text-gray-400">Access your HVPPY Central account</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-6 p-6 pt-0">
          {!showTwoFactorInput ? (
            <>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="w-full py-2 flex items-center justify-center space-x-2 text-lg font-medium transition-colors duration-200 ease-in-out hover:bg-gray-100 dark:hover:bg-gray-800" onClick={() => handleOAuthSignIn("github")} disabled={isLoading}>
                  <svg className="mr-2 h-5 w-5" aria-hidden="true" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M12 2C6.477 2 2 6.484 2 12.017 2 16.085 4.408 19.483 7.61 21.717.9.9 8.75 22 12 22c3.242 0 5.917-2.179 5.917-5.917 0-3.242-2.179-5.917-5.917-5.917-3.242 0-5.917 2.179-5.917 5.917 0 3.242 2.179 5.917 5.917 5.917 3.242 0 5.917-2.179 5.917-5.917 0-3.242-2.179-5.917-5.917-5.917zM12 4.295c-4.242 0-7.705 3.463-7.705 7.705 0 3.41 2.21 6.304 5.25 7.34.38.07.53-.16.53-.35v-2.56c-2.18.47-2.64-.94-2.64-.94-.36-.91-.88-1.15-.88-1.15-.72-.49.05-.48.05-.48.79.06 1.2.81 ******** 1.2 1.84.86 **********-.52.27-.86.49-1.06-1.75-.2-3.59-.88-3.59-3.91 0-.86.3-1.56.81-2.11-.08-.2-.35-1-.08-2.09 0 0 .67-.21 ********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.01 2.2-.8 2.2-.8.27 1.09.01 1.89-.08 **********.81 1.25.81 2.11 0 3.04-1.84 3.71-3.59 **********.53.71.53 1.43v2.12c0 .***********.35 3.04-1.03 5.25-3.93 5.25-7.34C20.295 7.758 16.832 4.295 12 4.295z"
                      clipRule="evenodd"
                    />
                  </svg>
                  GitHub
                </Button>
                <Button variant="outline" className="w-full py-2 flex items-center justify-center space-x-2 text-lg font-medium transition-colors duration-200 ease-in-out hover:bg-gray-100 dark:hover:bg-gray-800" onClick={() => handleOAuthSignIn("google")} disabled={isLoading}>
                  <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.24 10.285V11.69h4.225c-.125 1.425-1.55 3.825-4.225 3.825-2.535 0-4.6-2.06-4.6-4.6s2.06-4.6 4.6-4.6c1.15 0 2.2.4 3.025 1.1l2.6-2.6c-1.53-1.4-3.575-2.285-5.625-2.285C6.475 2.12 2 6.59 2 12s4.475 9.88 10 9.88c5.375 0 9.325-3.95 9.325-9.325 0-.65-.075-1.3-.2-1.95H12.24z" />
                  </svg>
                  Google
                </Button>
                <Button variant="outline" className="w-full py-2 flex items-center justify-center space-x-2 text-lg font-medium transition-colors duration-200 ease-in-out hover:bg-gray-100 dark:hover:bg-gray-800" onClick={() => handleOAuthSignIn("facebook")} disabled={isLoading}>
                  <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.776-3.89 1.094 0 2.24.195 2.24.195v2.453H15.83c-1.22 0-1.628.75-1.628 1.54v1.81h2.919l-.474 2.919h-2.445V22C18.343 21.128 22 16.991 22 12z" />
                  </svg>
                  Facebook
                </Button>
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-2 text-muted-foreground">Or continue with</span>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" value={email} onChange={(e) => setEmail(e.target.value)} disabled={isLoading} />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input id="password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} disabled={isLoading} />
              </div>
              <div className="text-right text-sm">
                <Link href="/auth/forgot-password" className="text-primary hover:underline">
                  Forgot password?
                </Link>
              </div>
            </>
          ) : (
            <div className="grid gap-2">
              <Label htmlFor="two-factor-code">Two-Factor Code</Label>
              <Input
                id="two-factor-code"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                value={twoFactorCode}
                onChange={(e) => setTwoFactorCode(e.target.value)}
                disabled={isLoading}
              />
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col gap-4 p-6 pt-0">
          <Button className="w-full py-2 text-lg" onClick={handleCredentialSignIn} disabled={isLoading}>
            {isLoading ? "Signing In..." : (showTwoFactorInput ? "Verify Code" : "Sign in with Email")}
          </Button>
          {!showTwoFactorInput && (
            <p className="text-sm text-center text-gray-500 dark:text-gray-400">
              Don&apos;t have an account?{" "}
              <Link href="/auth/register" className="text-primary hover:underline">
                Sign Up
              </Link>
            </p>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}