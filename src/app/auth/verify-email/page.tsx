"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardDescription, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [verificationStatus, setVerificationStatus] = useState("verifying"); // 'verifying', 'success', 'error'
  const [message, setMessage] = useState("Verifying your email address...");
  const [emailToResend, setEmailToResend] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const token = searchParams.get("token");

    if (!token) {
      setVerificationStatus("error");
      setMessage("No verification token found. Please enter your email to resend the link.");
      // toast.error("No verification token found."); // Removed to avoid double toast
      return;
    }

    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/auth/verify-email?token=${token}`);

        if (response.ok) {
          setVerificationStatus("success");
          setMessage("Your email has been successfully verified! You can now log in.");
          toast.success("Email verified successfully!");
          setTimeout(() => {
            router.push("/auth/login");
          }, 3000);
        } else {
          const errorData = await response.text(); // Get text for generic error
          setVerificationStatus("error");
          setMessage(errorData || "Failed to verify email. The link might be invalid or expired. Please enter your email to resend the link.");
          toast.error(errorData || "Failed to verify email.");
        }
      } catch (error) {
        console.error("Email verification request error:", error);
        setVerificationStatus("error");
        setMessage("An unexpected error occurred during verification. Please enter your email to resend the link.");
        toast.error("An unexpected error occurred.");
      }
    };

    verifyEmail();
  }, [searchParams, router]);

  const handleResendVerification = async () => {
    if (!emailToResend) {
      toast.error("Please enter your email address.");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: emailToResend }),
      });

      if (response.ok) {
        toast.success("If an account with that email exists, a new verification link has been sent.");
        setMessage("A new verification link has been sent to your email. Please check your inbox.");
      } else {
        const errorData = await response.text();
        toast.error(errorData || "Failed to resend verification email.");
      }
    } catch (error) {
      console.error("Resend verification email request error:", error);
      toast.error("An unexpected error occurred while resending the email.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg overflow-hidden text-center">
        <CardHeader className="space-y-2 p-6 pb-4">
          <CardTitle className="text-3xl font-extrabold text-gray-900 dark:text-gray-50">
            {verificationStatus === "success" ? "Email Verified!" : "Verify Your Email"}
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            {verificationStatus === "success" ? "" : ""}
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4 p-6 pt-0">
          <p className={`text-base ${verificationStatus === "error" ? "text-red-500" : "text-gray-700 dark:text-gray-300"}`}>
            {message}
          </p>
          {verificationStatus === "error" && (
            <div className="grid gap-4 mt-4">
              <div className="grid gap-2">
                <Label htmlFor="emailToResend">Email Address</Label>
                <Input
                  id="emailToResend"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailToResend}
                  onChange={(e) => setEmailToResend(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <Button onClick={handleResendVerification} className="w-full py-2 text-lg" disabled={isLoading}>
                {isLoading ? "Sending..." : "Resend Verification Email"}
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center p-6 pt-0">
          {verificationStatus !== "verifying" && (
            <Button asChild className="py-2 text-lg">
              <Link href="/auth/login">Back to Login</Link>
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
