"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Image from "next/image";

export default function UserProfilePage() {
  const { data: session, update } = useSession();
  const [name, setName] = useState(session?.user?.name || "");
  const [email, setEmail] = useState(session?.user?.email || "");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [profileLoading, setProfileLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  // 2FA states
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(session?.user?.twoFactorEnabled || false);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [twoFactorSecret, setTwoFactorSecret] = useState<string | null>(null);
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [twoFactorLoading, setTwoFactorLoading] = useState(false);

  useEffect(() => {
    if (session?.user) {
      setName(session.user.name || "");
      setEmail(session.user.email || "");
      setTwoFactorEnabled(session.user.twoFactorEnabled || false);
    }
  }, [session]);

  const validatePassword = (pwd: string) => {
    if (pwd.length < 8) {
      return "Password must be at least 8 characters long.";
    }
    if (!/[A-Z]/.test(pwd)) {
      return "Password must contain at least one uppercase letter.";
    }
    if (!/[a-z]/.test(pwd)) {
      return "Password must contain at least one lowercase letter.";
    }
    if (!/[0-9]/.test(pwd)) {
      return "Password must contain at least one number.";
    }
    if (!/[^A-Za-z0-9]/.test(pwd)) {
      return "Password must contain at least one special character.";
    }
    return null;
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const pwd = e.target.value;
    setNewPassword(pwd);
    setPasswordError(validatePassword(pwd));
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileLoading(true);

    try {
      const response = await fetch("/api/user/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, email }),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        // Update session with new data
        await update({
          user: {
            name: updatedUser.name,
            email: updatedUser.email,
          },
        });
        toast.success("Profile updated successfully!");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to update profile.");
      }
    } catch (error) {
      console.error("Profile update error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setProfileLoading(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordLoading(true);

    if (newPassword !== confirmNewPassword) {
      toast.error("New passwords do not match.");
      setPasswordLoading(false);
      return;
    }

    const validationError = validatePassword(newPassword);
    if (validationError) {
      toast.error(validationError);
      setPasswordLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/user/change-password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ currentPassword, newPassword }),
      });

      if (response.ok) {
        toast.success("Password changed successfully!");
        setCurrentPassword("");
        setNewPassword("");
        setConfirmNewPassword("");
        // Invalidate other sessions after password change
        await handleInvalidateOtherSessions();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to change password.");
      }
    } catch (error) {
      console.error("Password change error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleGenerate2FA = async () => {
    setTwoFactorLoading(true);
    try {
      const response = await fetch("/api/user/2fa/generate");
      if (response.ok) {
        const data = await response.json();
        setQrCode(data.qrCodeImage);
        setTwoFactorSecret(data.secret);
        toast.success("2FA QR code generated. Scan it with your authenticator app.");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to generate 2FA QR code.");
      }
    } catch (error) {
      console.error("Generate 2FA error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setTwoFactorLoading(false);
    }
  };

  const handleVerify2FA = async () => {
    setTwoFactorLoading(true);
    try {
      const response = await fetch("/api/user/2fa/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token: twoFactorCode }),
      });

      if (response.ok) {
        setTwoFactorEnabled(true);
        setQrCode(null);
        setTwoFactorSecret(null);
        setTwoFactorCode("");
        await update(); // Refresh session to reflect 2FA status
        toast.success("2FA enabled successfully!");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to verify 2FA code.");
      }
    } catch (error) {
      console.error("Verify 2FA error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setTwoFactorLoading(false);
    }
  };

  const handleDisable2FA = async () => {
    setTwoFactorLoading(true);
    try {
      const response = await fetch("/api/user/2fa/disable", {
        method: "POST",
      });

      if (response.ok) {
        setTwoFactorEnabled(false);
        setQrCode(null);
        setTwoFactorSecret(null);
        setTwoFactorCode("");
        await update(); // Refresh session to reflect 2FA status
        toast.success("2FA disabled successfully!");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to disable 2FA.");
      }
    } catch (error) {
      console.error("Disable 2FA error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setTwoFactorLoading(false);
    }
  };

  const handleInvalidateOtherSessions = async () => {
    try {
      const response = await fetch("/api/user/invalidate-sessions", {
        method: "POST",
      });

      if (response.ok) {
        toast.success("Other active sessions have been invalidated.");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to invalidate other sessions.");
      }
    } catch (error) {
      console.error("Invalidate sessions error:", error);
      toast.error("An unexpected error occurred while invalidating sessions.");
    }
  };

  if (!session) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading session...</p>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <div className="grid gap-6 w-full max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Profile Information</CardTitle>
            <CardDescription>Update your account's profile information and email address.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input id="name" value={name} onChange={(e) => setName(e.target.value)} disabled={profileLoading} />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} disabled={profileLoading} />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleProfileUpdate} disabled={profileLoading}>
              {profileLoading ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Change Password</CardTitle>
            <CardDescription>Ensure your account is using a long, random password to stay secure.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="current-password">Current Password</Label>
              <Input id="current-password" type="password" value={currentPassword} onChange={(e) => setCurrentPassword(e.target.value)} disabled={passwordLoading} />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="new-password">New Password</Label>
              <Input id="new-password" type="password" value={newPassword} onChange={handleNewPasswordChange} disabled={passwordLoading} />
              {passwordError && <p className="text-red-500 text-sm mt-1">{passwordError}</p>}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirm-new-password">Confirm New Password</Label>
              <Input id="confirm-new-password" type="password" value={confirmNewPassword} onChange={(e) => setConfirmNewPassword(e.target.value)} disabled={passwordLoading} />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button onClick={handleChangePassword} disabled={passwordLoading}>
              {passwordLoading ? "Updating..." : "Update Password"}
            </Button>
            <Button variant="outline" onClick={handleInvalidateOtherSessions} disabled={passwordLoading}>
              Invalidate Other Sessions
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Two-Factor Authentication</CardTitle>
            <CardDescription>Add an additional layer of security to your account using 2FA.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            {twoFactorEnabled ? (
              <div className="text-center">
                <p className="text-lg font-medium text-green-600 dark:text-green-400">2FA is currently ENABLED.</p>
                <Button onClick={handleDisable2FA} disabled={twoFactorLoading} className="mt-4">
                  {twoFactorLoading ? "Disabling..." : "Disable 2FA"}
                </Button>
              </div>
            ) : (
              <div className="text-center">
                <p className="text-lg font-medium text-red-600 dark:text-red-400">2FA is currently DISABLED.</p>
                {!qrCode ? (
                  <Button onClick={handleGenerate2FA} disabled={twoFactorLoading} className="mt-4">
                    {twoFactorLoading ? "Generating..." : "Enable 2FA"}
                  </Button>
                ) : (
                  <div className="mt-4 grid gap-4 justify-items-center">
                    <p>Scan the QR code with your authenticator app (e.g., Google Authenticator, Authy).</p>
                    {qrCode && <Image src={qrCode} alt="QR Code" width={200} height={200} className="border p-2 rounded-md" />}
                    {twoFactorSecret && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 break-all">
                        Or manually enter this secret: <strong>{twoFactorSecret}</strong>
                      </p>
                    )}
                    <div className="grid gap-2 w-full max-w-xs">
                      <Label htmlFor="two-factor-code">Enter 2FA Code</Label>
                      <Input
                        id="two-factor-code"
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        value={twoFactorCode}
                        onChange={(e) => setTwoFactorCode(e.target.value)}
                        disabled={twoFactorLoading}
                      />
                    </div>
                    <Button onClick={handleVerify2FA} disabled={twoFactorLoading || !twoFactorCode} className="w-full max-w-xs">
                      {twoFactorLoading ? "Verifying..." : "Verify & Enable 2FA"}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
