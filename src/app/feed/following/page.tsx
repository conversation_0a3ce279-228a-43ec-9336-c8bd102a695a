"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react";

interface Post {
  id: string;
  title: string;
  content: string;
  contentType: string;
  contentUrl: string; // Appwrite file ID
  mediaUrls: string[];
  thumbnailUrl?: string;
  transcodingStatus: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  renditions?: { [key: string]: string }; // URLs of different transcoded versions
  moderationStatus: "PENDING" | "APPROVED" | "REJECTED";
  moderationNotes?: string;
  createdAt: string;
  user: { name: string | null; email: string | null; image: string | null };
  creator: { stageName: string | null } | null;
}

export default function FollowingFeedPage() {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      if (!session?.user?.id) {
        setIsLoading(false);
        setError("Please log in to view your personalized feed.");
        return;
      }

      try {
        const response = await fetch("/api/feed/personalized");
        if (response.ok) {
          const data = await response.json();
          setPosts(data.posts);
        } else {
          const errorData = await response.json();
          setError(errorData.message || "Failed to fetch personalized feed.");
          toast.error(errorData.message || "Failed to fetch personalized feed.");
        }
      } catch (err) {
        console.error("Error fetching personalized feed:", err);
        setError("An unexpected error occurred while fetching personalized feed.");
        toast.error("An unexpected error occurred while fetching personalized feed.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();

    // Poll for updates
    const interval = setInterval(fetchPosts, 10000); 
    return () => clearInterval(interval);
  }, [session]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading personalized feed...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Your Personalized Feed</h1>
      {posts.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-lg text-muted-foreground">No content from followed creators yet.</p>
          <p className="text-md text-muted-foreground">Start following creators to see their content here!</p>
          <Button asChild className="mt-4">
            <Link href="/live/discover">Discover Live Streams</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {posts.map((post) => (
            <Card key={post.id} className="flex flex-col">
              <CardHeader>
                <CardTitle className="text-lg truncate">{post.title}</CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  By {post.creator?.stageName || post.user.name || post.user.email}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex items-center justify-center p-2">
                {post.moderationStatus === "REJECTED" ? (
                  <div className="text-center text-red-500">
                    <p className="font-bold">Content Rejected</p>
                    {post.moderationNotes && <p className="text-sm">Reason: {post.moderationNotes}</p>}
                  </div>
                ) : (
                  <>
                    {post.transcodingStatus === "PENDING" && (
                      <p className="text-yellow-500">Transcoding Pending...</p>
                    )}
                    {post.transcodingStatus === "IN_PROGRESS" && (
                      <p className="text-blue-500">Transcoding In Progress...</p>
                    )}
                    {post.transcodingStatus === "FAILED" && (
                      <p className="text-red-500">Transcoding Failed</p>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("video/") && (
                      <video controls src={post.renditions.high} className="max-w-full max-h-48 object-contain rounded-md"></video>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("audio/") && (
                      <audio controls src={post.renditions.high} className="w-full"></audio>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                      <img src={`/api/media/stream/${post.contentUrl}`} alt={post.title} className="max-w-full max-h-48 object-contain rounded-md" />
                    )}
                    {post.transcodingStatus === "COMPLETED" && !post.contentType.startsWith("image/") && !post.contentType.startsWith("video/") && !post.contentType.startsWith("audio/") && (
                      <p className="text-muted-foreground">Unsupported file type for preview.</p>
                    )}
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-between items-center text-sm text-muted-foreground">
                <span>Transcoding: {post.transcodingStatus}</span>
                <span>Moderation: {post.moderationStatus}</span>
                {post.transcodingStatus === "COMPLETED" && post.renditions?.high && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={post.renditions.high} target="_blank" rel="noopener noreferrer">View High Quality</a>
                  </Button>
                )}
                {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/api/media/stream/${post.contentUrl}`} target="_blank" rel="noopener noreferrer">View Raw</a>
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
