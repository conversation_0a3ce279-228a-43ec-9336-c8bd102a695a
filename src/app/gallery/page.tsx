"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react";
import CommentsSection from "@/components/comments-section"; // Import CommentsSection

interface Post {
  id: string;
  title: string;
  content: string;
  contentType: string;
  contentUrl: string; // Appwrite file ID
  mediaUrls: string[];
  thumbnailUrl?: string;
  transcodingStatus: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  renditions?: { [key: string]: string }; // URLs of different transcoded versions
  moderationStatus: "PENDING" | "APPROVED" | "REJECTED";
  moderationNotes?: string;
  createdAt: string;
  likeCount: number; // Added for likes
  hasLiked: boolean; // Added for current user's like status
}

export default function MediaGalleryPage() {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPosts = async () => {
    try {
      const response = await fetch("/api/posts");
      if (response.ok) {
        const data = await response.json();
        setPosts(data.posts);
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to fetch media posts.");
        toast.error(errorData.message || "Failed to fetch media posts.");
      }
    } catch (err) {
      console.error("Error fetching media posts:", err);
      setError("An unexpected error occurred while fetching media posts.");
      toast.error("An unexpected error occurred while fetching media posts.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();

    // Poll for updates on transcoding and moderation status
    const interval = setInterval(fetchPosts, 5000); 
    return () => clearInterval(interval);
  }, [session]); // Re-fetch when session changes to update hasLiked

  const handleLike = async (postId: string) => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to like a post.");
      return;
    }

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      if (response.ok) {
        toast.success("Post liked!");
        setPosts(prevPosts =>
          prevPosts.map(post =>
            post.id === postId ? { ...post, likeCount: post.likeCount + 1, hasLiked: true } : post
          )
        );
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to like post.");
      }
    } catch (error) {
      console.error("Error liking post:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  const handleUnlike = async (postId: string) => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to unlike a post.");
      return;
    }

    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
      });

      if (response.ok) {
        toast.success("Post unliked!");
        setPosts(prevPosts =>
          prevPosts.map(post =>
            post.id === postId ? { ...post, likeCount: post.likeCount - 1, hasLiked: false } : post
          )
        );
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to unlike post.");
      }
    } catch (error) {
      console.error("Error unliking post:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading media gallery...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Your Media Gallery</h1>
      {posts.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-lg text-muted-foreground">No media files uploaded yet.</p>
          <Button asChild className="mt-4">
            <Link href="/upload">Upload your first file</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {posts.map((post) => (
            <Card key={post.id} className="flex flex-col">
              <CardHeader>
                <CardTitle className="text-lg truncate">{post.title}</CardTitle>
                <CardDescription className="text-sm text-muted-foreground">{post.contentType}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex items-center justify-center p-2">
                {post.moderationStatus === "REJECTED" ? (
                  <div className="text-center text-red-500">
                    <p className="font-bold">Content Rejected</p>
                    {post.moderationNotes && <p className="text-sm">Reason: {post.moderationNotes}</p>}
                  </div>
                ) : (
                  <>
                    {post.transcodingStatus === "PENDING" && (
                      <p className="text-yellow-500">Transcoding Pending...</p>
                    )}
                    {post.transcodingStatus === "IN_PROGRESS" && (
                      <p className="text-blue-500">Transcoding In Progress...</p>
                    )}
                    {post.transcodingStatus === "FAILED" && (
                      <p className="text-red-500">Transcoding Failed</p>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("video/") && (
                      <video controls src={post.renditions.high} className="max-w-full max-h-48 object-contain rounded-md"></video>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("audio/") && (
                      <audio controls src={post.renditions.high} className="w-full"></audio>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                      <img src={`/api/media/stream/${post.contentUrl}`} alt={post.title} className="max-w-full max-h-48 object-contain rounded-md" />
                    )}
                    {post.transcodingStatus === "COMPLETED" && !post.contentType.startsWith("image/") && !post.contentType.startsWith("video/") && !post.contentType.startsWith("audio/") && (
                      <p className="text-muted-foreground">Unsupported file type for preview.</p>
                    )}
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-between items-center text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => (post.hasLiked ? handleUnlike(post.id) : handleLike(post.id))}
                    disabled={!session?.user?.id}
                  >
                    {post.hasLiked ? "❤️" : "🤍"} {post.likeCount}
                  </Button>
                  <span>Transcoding: {post.transcodingStatus}</span>
                  <span>Moderation: {post.moderationStatus}</span>
                </div>
                {post.transcodingStatus === "COMPLETED" && post.renditions?.high && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={post.renditions.high} target="_blank" rel="noopener noreferrer">View High Quality</a>
                  </Button>
                )}
                {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/api/media/stream/${post.contentUrl}`} target="_blank" rel="noopener noreferrer">View Raw</a>
                  </Button>
                )}
              </CardFooter>
              {/* Comments Section */}
              <CommentsSection postId={post.id} />
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
