"use client";

import { useEffect, useRef, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import * as mediasoupClient from 'mediasoup-client';
import LiveChat from '@/components/live-chat'; // Import the LiveChat component

const MEDIASOUP_SERVER_URL = 'wss://localhost:3001';

export default function LiveStreamViewerPage() {
  const { streamId } = useParams();
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  const device = useRef<mediasoupClient.Device | null>(null);
  const consumerTransport = useRef<mediasoupClient.Transport | null>(null);
  const consumer = useRef<mediasoupClient.Consumer | null>(null);
  const ws = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Clean up on unmount
    return () => {
      if (consumer.current) consumer.current.close();
      if (consumerTransport.current) consumerTransport.current.close();
      if (ws.current) ws.current.close();
      if (remoteVideoRef.current && remoteVideoRef.current.srcObject) {
        (remoteVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const connectAndConsume = async () => {
    setIsConnecting(true);

    // 1. Fetch JWT from Next.js API
    let token = null;
    try {
      const response = await fetch("/api/mediasoup-token");
      if (response.ok) {
        const data = await response.json();
        token = data.token;
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to get Mediasoup token.");
        setIsConnecting(false);
        return;
      }
    } catch (error) {
      console.error("Error fetching Mediasoup token:", error);
      toast.error("An unexpected error occurred while fetching Mediasoup token.");
      setIsConnecting(false);
      return;
    }

    ws.current = new WebSocket(MEDIASOUP_SERVER_URL);

    ws.current.onopen = async () => {
      toast.success('Connected to Mediasoup server. Authenticating...');
      // 2. Send JWT for authentication
      ws.current?.send(JSON.stringify({ type: 'authenticate', token }));
    };

    ws.current.onmessage = async (event) => {
      const msg = JSON.parse(event.data as string);

      switch (msg.type) {
        case 'authenticated':
          if (msg.success) {
            toast.success('Authenticated with Mediasoup server.');
            setIsConnected(true);
            setIsConnecting(false);
            // Now get router RTP capabilities
            ws.current?.send(JSON.stringify({ type: 'getRouterRtpCapabilities' }));
          } else {
            toast.error(msg.error || 'Authentication failed.');
            ws.current?.close();
            setIsConnecting(false);
          }
          break;

        case 'routerRtpCapabilities':
          // 2. Load Mediasoup Device
          if (!device.current) {
            device.current = new mediasoupClient.Device();
          }
          try {
            await device.current.load({ routerRtpCapabilities: msg.data });
            toast.info('Mediasoup device loaded.');
            // Now create consumer transport
            ws.current?.send(JSON.stringify({ type: 'createWebRtcTransport', data: { consuming: true } }));
          } catch (error: any) {
            if (error.name === 'UnsupportedError') {
              toast.error('Browser not supported for Mediasoup.');
            } else {
              console.error(error);
              toast.error('Failed to load Mediasoup device.');
            }
          }
          break;

        case 'webRtcTransportCreated':
          const { id, iceParameters, iceCandidates, dtlsParameters, sctpParameters } = msg.data;
          consumerTransport.current = device.current?.createRecvTransport({
            id,
            iceParameters,
            iceCandidates,
            dtlsParameters,
            sctpParameters,
          });

          consumerTransport.current?.on('connect', ({ dtlsParameters }, callback, errback) => {
            ws.current?.send(JSON.stringify({ type: 'connectWebRtcTransport', data: { transportId: consumerTransport.current?.id, dtlsParameters } }));
            callback();
          });

          // Now consume the stream
          if (device.current?.rtpCapabilities) {
            ws.current?.send(JSON.stringify({ type: 'consume', data: { rtpCapabilities: device.current?.rtpCapabilities, producerId: streamId } }));
          }
          break;

        case 'webRtcTransportConnected':
          // Transport connected
          break;

        case 'consumerCreated':
          if (msg.data) {
            const { id: consumerId, producerId, kind, rtpParameters, type, sctpParameters, paused } = msg.data;
            consumer.current = await consumerTransport.current?.consume({
              id: consumerId,
              producerId,
              kind,
              rtpParameters,
              appData: { type },
            });

            if (remoteVideoRef.current) {
              remoteVideoRef.current.srcObject = new MediaStream([consumer.current!.track]);
            }
            if (paused) {
              ws.current?.send(JSON.stringify({ type: 'resumeConsumer', data: { consumerId: consumer.current?.id } }));
            }
            toast.success('Consumer created and remote stream received.');
          } else {
            toast.error('Failed to create consumer. Stream might not be active.');
          }
          break;

        case 'consumerResumed':
          toast.info('Consumer resumed.');
          break;

        default:
          console.warn('Unknown message type from mediasoup server:', msg.type);
      }
    };

    ws.current.onclose = () => {
      toast.info('Disconnected from Mediasoup server.');
      setIsConnected(false);
      setIsConnecting(false);
    };

    ws.current.onerror = (err) => {
      toast.error('Mediasoup connection error.');
      console.error('Mediasoup WebSocket error:', err);
      setIsConnected(false);
      setIsConnecting(false);
    };
  };

  const disconnect = () => {
    if (consumer.current) consumer.current.close();
    if (consumerTransport.current) consumerTransport.current.close();
    if (ws.current) ws.current.close();
    if (remoteVideoRef.current && remoteVideoRef.current.srcObject) {
      (remoteVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      remoteVideoRef.current.srcObject = null;
    }
    setIsConnected(false);
    toast.info('Disconnected from stream.');
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Watching Live Stream: {streamId}</h1>
      <div className="flex justify-center space-x-4 mb-4">
        {!isConnected ? (
          <Button onClick={connectAndConsume} disabled={isConnecting}>
            {isConnecting ? 'Connecting...' : 'Connect to Stream'}
          </Button>
        ) : (
          <Button onClick={disconnect} variant="destructive">
            Disconnect
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 h-[70vh]">
        <div className="md:col-span-2">
          <h3 className="text-xl font-semibold mb-2">Live Stream</h3>
          <video ref={remoteVideoRef} autoPlay controls className="w-full h-full object-contain border rounded-md"></video>
        </div>
        <div className="md:col-span-1 h-full">
          <LiveChat streamId={streamId as string} />
        </div>
      </div>
    </div>
  );
}