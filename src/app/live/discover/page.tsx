"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react"; // Import useSession

interface LiveStream {
  $id: string;
  userId: string; // The ID of the user who owns the stream
  streamId: string;
  status: string;
  title: string;
  description: string;
  startedAt: string;
  endedAt?: string;
}

export default function LiveStreamDiscoveryPage() {
  const { data: session } = useSession(); // Get session data
  const [liveStreams, setLiveStreams] = useState<LiveStream[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [followingStatus, setFollowingStatus] = useState<Record<string, boolean>>({}); // Map of userId to boolean

  const fetchLiveStreams = async () => {
    try {
      const response = await fetch("/api/livestreams");
      if (response.ok) {
        const data = await response.json();
        setLiveStreams(data.streams);

        // Check following status for each stream's user
        if (session?.user?.id) {
          const newFollowingStatus: Record<string, boolean> = {};
          for (const stream of data.streams) {
            if (stream.userId !== session.user.id) { // Cannot follow self
              const followCheckResponse = await fetch(`/api/user/follow?followerId=${session.user.id}&followingId=${stream.userId}`);
              if (followCheckResponse.ok) {
                const followCheckData = await followCheckResponse.json();
                newFollowingStatus[stream.userId] = followCheckData.isFollowing;
              } else {
                console.error(`Failed to check follow status for ${stream.userId}`);
                newFollowingStatus[stream.userId] = false;
              }
            } else {
              newFollowingStatus[stream.userId] = false; // Cannot follow self
            }
          }
          setFollowingStatus(newFollowingStatus);
        }

      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to fetch live streams.");
        toast.error(errorData.message || "Failed to fetch live streams.");
      }
    } catch (err) {
      console.error("Error fetching live streams:", err);
      setError("An unexpected error occurred while fetching live streams.");
      toast.error("An unexpected error occurred while fetching live streams.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveStreams();

    // Optional: Poll for updates every few seconds
    const interval = setInterval(fetchLiveStreams, 5000); 
    return () => clearInterval(interval);
  }, [session]); // Re-fetch when session changes

  const handleFollow = async (followingId: string) => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to follow.");
      return;
    }
    if (session.user.id === followingId) {
      toast.error("You cannot follow yourself.");
      return;
    }

    try {
      const response = await fetch("/api/user/follow", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId }),
      });

      if (response.ok) {
        toast.success("Followed successfully!");
        setFollowingStatus(prev => ({ ...prev, [followingId]: true }));
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to follow.");
      }
    } catch (error) {
      console.error("Error following:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  const handleUnfollow = async (followingId: string) => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to unfollow.");
      return;
    }

    try {
      const response = await fetch("/api/user/follow", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId }),
      });

      if (response.ok) {
        toast.success("Unfollowed successfully!");
        setFollowingStatus(prev => ({ ...prev, [followingId]: false }));
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to unfollow.");
      }
    } catch (error) {
      console.error("Error unfollowing:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading live streams...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Discover Live Streams</h1>
      {liveStreams.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-lg text-muted-foreground">No live streams currently active.</p>
          <Button asChild className="mt-4">
            <Link href="/dashboard">Start your own stream</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {liveStreams.map((stream) => (
            <Card key={stream.$id} className="flex flex-col">
              <CardHeader>
                <CardTitle className="text-lg truncate">{stream.title}</CardTitle>
                <CardDescription className="text-sm text-muted-foreground">{stream.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex items-center justify-center p-2">
                <p className={`text-xl font-bold ${stream.status === 'live' ? 'text-green-500' : 'text-red-500'}`}>
                  {stream.status.toUpperCase()}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between items-center text-sm text-muted-foreground">
                <span>Started: {new Date(stream.startedAt).toLocaleString()}</span>
                <div className="flex items-center space-x-2">
                  {stream.status === 'live' && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/live/${stream.streamId}`}>Watch Now</Link>
                    </Button>
                  )}
                  {session?.user?.id && stream.userId !== session.user.id && (
                    followingStatus[stream.userId] ? (
                      <Button variant="secondary" size="sm" onClick={() => handleUnfollow(stream.userId)}>
                        Unfollow
                      </Button>
                    ) : (
                      <Button variant="default" size="sm" onClick={() => handleFollow(stream.userId)}>
                        Follow
                      </Button>
                    )
                  )}
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}