"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import Link from "next/link";

interface UserProfile {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  username?: string | null;
  displayName?: string | null;
  bio?: string | null;
  avatar?: string | null;
  role: string;
  isVerified: boolean;
  creator?: {
    id: string;
    stageName?: string | null;
    genre: string[];
    location?: string | null;
    website?: string | null;
    socialLinks?: any;
  } | null;
  posts: Post[];
  isFollowing?: boolean; // Added for viewer's perspective
}

interface Post {
  id: string;
  title: string;
  content: string;
  contentType: string;
  contentUrl: string; // Appwrite file ID
  mediaUrls: string[];
  thumbnailUrl?: string;
  transcodingStatus: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  renditions?: { [key: string]: string }; // URLs of different transcoded versions
  moderationStatus: "PENDING" | "APPROVED" | "REJECTED";
  moderationNotes?: string;
  createdAt: string;
}

export default function UserProfilePage() {
  const { userId } = useParams();
  const { data: session } = useSession();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFollowLoading, setIsFollowLoading] = useState(false);

  const fetchUserProfile = async () => {
    try {
      const response = await fetch(`/api/user/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setProfile(data);
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Failed to fetch user profile.");
        toast.error(errorData.message || "Failed to fetch user profile.");
      }
    } catch (err) {
      console.error("Error fetching user profile:", err);
      setError("An unexpected error occurred while fetching user profile.");
      toast.error("An unexpected error occurred while fetching user profile.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUserProfile();
    }
  }, [userId, session]); // Re-fetch if userId or session changes

  const handleFollow = async () => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to follow.");
      return;
    }
    if (session.user.id === userId) {
      toast.error("You cannot follow yourself.");
      return;
    }

    setIsFollowLoading(true);
    try {
      const response = await fetch("/api/user/follow", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId: userId }),
      });

      if (response.ok) {
        toast.success("Followed successfully!");
        setProfile(prev => prev ? { ...prev, isFollowing: true } : null);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to follow.");
      }
    } catch (error) {
      console.error("Error following:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setIsFollowLoading(false);
    }
  };

  const handleUnfollow = async () => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to unfollow.");
      return;
    }

    setIsFollowLoading(true);
    try {
      const response = await fetch("/api/user/follow", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId: userId }),
      });

      if (response.ok) {
        toast.success("Unfollowed successfully!");
        setProfile(prev => prev ? { ...prev, isFollowing: false } : null);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to unfollow.");
      }
    } catch (error) {
      console.error("Error unfollowing:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setIsFollowLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading profile...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>User not found.</p>
      </div>
    );
  }

  const isOwnProfile = session?.user?.id === userId;

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full max-w-4xl mx-auto shadow-lg rounded-lg overflow-hidden">
        <CardHeader className="space-y-2 p-6 pb-4 text-center">
          {profile.avatar && (
            <img src={profile.avatar} alt="Avatar" className="w-24 h-24 rounded-full mx-auto mb-4 object-cover" />
          )}
          <CardTitle className="text-3xl font-extrabold text-gray-900 dark:text-gray-50">
            {profile.creator?.stageName || profile.displayName || profile.name || "User"}
          </CardTitle>
          <CardDescription className="text-center text-gray-600 dark:text-gray-400">
            @{profile.username || profile.email}
          </CardDescription>
          {profile.bio && <p className="text-md text-gray-700 dark:text-gray-300 mt-2">{profile.bio}</p>}

          {!isOwnProfile && session?.user?.id && (
            <div className="mt-4">
              {profile.isFollowing ? (
                <Button onClick={handleUnfollow} disabled={isFollowLoading} variant="secondary">
                  {isFollowLoading ? "Unfollowing..." : "Unfollow"}
                </Button>
              ) : (
                <Button onClick={handleFollow} disabled={isFollowLoading}>
                  {isFollowLoading ? "Following..." : "Follow"}
                </Button>
              )}
            </div>
          )}

          {isOwnProfile && (
            <div className="mt-4">
              <Button asChild>
                <Link href="/dashboard/profile">Edit Profile</Link>
              </Button>
              {profile.role === "CREATOR" && (
                <Button asChild className="ml-2">
                  <Link href="/dashboard/creator-profile">Edit Creator Profile</Link>
                </Button>
              )}
            </div>
          )}
        </CardHeader>

        {profile.creator && (
          <CardContent className="p-6 pt-0 border-t mt-4">
            <h2 className="text-2xl font-bold mb-4">Creator Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {profile.creator.genre.length > 0 && (
                <div>
                  <p className="font-semibold">Genre:</p>
                  <p>{profile.creator.genre.join(", ")}</p>
                </div>
              )}
              {profile.creator.location && (
                <div>
                  <p className="font-semibold">Location:</p>
                  <p>{profile.creator.location}</p>
                </div>
              )}
              {profile.creator.website && (
                <div>
                  <p className="font-semibold">Website:</p>
                  <p><a href={profile.creator.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{profile.creator.website}</a></p>
                </div>
              )}
              {profile.creator.socialLinks && Object.keys(profile.creator.socialLinks).length > 0 && (
                <div>
                  <p className="font-semibold">Social Links:</p>
                  {Object.entries(profile.creator.socialLinks).map(([platform, url]) => (
                    <p key={platform}><a href={url as string} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{platform}</a></p>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        )}

        <CardContent className="p-6 pt-0 border-t mt-4">
          <h2 className="text-2xl font-bold mb-4">Content by {profile.creator?.stageName || profile.displayName || profile.name}</h2>
          {profile.posts.length === 0 ? (
            <p className="text-muted-foreground">No content available yet.</p>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {profile.posts.map((post) => (
                <Card key={post.id} className="flex flex-col">
                  <CardHeader>
                    <CardTitle className="text-md truncate">{post.title}</CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">{post.contentType}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow flex items-center justify-center p-2">
                    {post.moderationStatus === "REJECTED" ? (
                      <div className="text-center text-red-500">
                        <p className="font-bold">Content Rejected</p>
                        {post.moderationNotes && <p className="text-sm">Reason: {post.moderationNotes}</p>}
                      </div>
                    ) : (
                      <>
                        {post.transcodingStatus === "PENDING" && (
                          <p className="text-yellow-500">Transcoding Pending...</p>
                        )}
                        {post.transcodingStatus === "IN_PROGRESS" && (
                          <p className="text-blue-500">Transcoding In Progress...</p>
                        )}
                        {post.transcodingStatus === "FAILED" && (
                          <p className="text-red-500">Transcoding Failed</p>
                        )}
                        {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("video/") && (
                          <video controls src={post.renditions.high} className="max-w-full max-h-32 object-contain rounded-md"></video>
                        )}
                        {post.transcodingStatus === "COMPLETED" && post.renditions?.high && post.contentType.startsWith("audio/") && (
                          <audio controls src={post.renditions.high} className="w-full"></audio>
                        )}
                        {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                          <img src={`/api/media/stream/${post.contentUrl}`} alt={post.title} className="max-w-full max-h-32 object-contain rounded-md" />
                        )}
                        {post.transcodingStatus === "COMPLETED" && !post.contentType.startsWith("image/") && !post.contentType.startsWith("video/") && !post.contentType.startsWith("audio/") && (
                          <p className="text-muted-foreground">Unsupported file type for preview.</p>
                        )}
                      </>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-between items-center text-sm text-muted-foreground">
                    <span>Status: {post.transcodingStatus}</span>
                    {post.transcodingStatus === "COMPLETED" && post.renditions?.high && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={post.renditions.high} target="_blank" rel="noopener noreferrer">View</a>
                      </Button>
                    )}
                    {post.transcodingStatus === "COMPLETED" && post.contentType.startsWith("image/") && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={`/api/media/stream/${post.contentUrl}`} target="_blank" rel="noopener noreferrer">View</a>
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
