"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";

interface UserResult {
  id: string;
  name?: string | null;
  username?: string | null;
  displayName?: string | null;
  avatar?: string | null;
  role: string;
}

interface CreatorResult {
  id: string;
  stageName?: string | null;
  user: { id: string; name?: string | null; username?: string | null; displayName?: string | null; avatar?: string | null };
}

interface PostResult {
  id: string;
  title: string;
  content: string;
  contentType: string;
  contentUrl?: string | null;
  thumbnailUrl?: string | null;
  user: { name?: string | null; username?: string | null; displayName?: string | null };
  creator?: { stageName?: string | null } | null;
}

export default function SearchPage() {
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState<{ users: UserResult[]; creators: CreatorResult[]; posts: PostResult[] } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!query.trim()) {
      setSearchResults(null);
      setHasSearched(false);
      return;
    }

    setIsLoading(true);
    setHasSearched(true);
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Search failed.");
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("An unexpected error occurred during search.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Optional: Trigger search on query change after a debounce
    const handler = setTimeout(() => {
      handleSearch();
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [query]);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Search HVPPY Central</h1>
      <form onSubmit={handleSearch} className="flex gap-2 mb-8 max-w-lg mx-auto">
        <Input
          type="text"
          placeholder="Search users, creators, or content..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          disabled={isLoading}
        />
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Searching..." : "Search"}
        </Button>
      </form>

      {hasSearched && isLoading && <p className="text-center">Loading results...</p>}
      {hasSearched && !isLoading && searchResults && (
        <div className="grid gap-8">
          {searchResults.users.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Users</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4">
                {searchResults.users.map((user) => (
                  <div key={user.id} className="flex items-center gap-4">
                    {user.avatar && <img src={user.avatar} alt="Avatar" className="w-10 h-10 rounded-full object-cover" />}
                    <div>
                      <Link href={`/profile/${user.id}`} className="font-semibold hover:underline">
                        {user.displayName || user.name || user.username || user.id}
                      </Link>
                      <p className="text-sm text-muted-foreground">@{user.username || user.email}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {searchResults.creators.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Creators</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4">
                {searchResults.creators.map((creator) => (
                  <div key={creator.id} className="flex items-center gap-4">
                    {creator.user.avatar && <img src={creator.user.avatar} alt="Avatar" className="w-10 h-10 rounded-full object-cover" />}
                    <div>
                      <Link href={`/profile/${creator.user.id}`} className="font-semibold hover:underline">
                        {creator.stageName || creator.user.displayName || creator.user.name}
                      </Link>
                      <p className="text-sm text-muted-foreground">@{creator.user.username || creator.user.email}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {searchResults.posts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Content</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4">
                {searchResults.posts.map((post) => (
                  <div key={post.id} className="flex items-center gap-4">
                    {post.thumbnailUrl && <img src={post.thumbnailUrl} alt="Thumbnail" className="w-16 h-16 object-cover rounded-md" />}
                    <div>
                      <Link href={`/gallery`} className="font-semibold hover:underline"> {/* Link to gallery for now, ideally to individual post page */}
                        {post.title}
                      </Link>
                      <p className="text-sm text-muted-foreground">
                        By {post.creator?.stageName || post.user.displayName || post.user.name}
                      </p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {searchResults.users.length === 0 && searchResults.creators.length === 0 && searchResults.posts.length === 0 && (
            <p className="text-center text-muted-foreground">No results found for "{query}".</p>
          )}
        </div>
      )}
      {hasSearched && !isLoading && !searchResults && (
        <p className="text-center text-muted-foreground">Enter a search term to find users, creators, or content.</p>
      )}
    </div>
  );
}
