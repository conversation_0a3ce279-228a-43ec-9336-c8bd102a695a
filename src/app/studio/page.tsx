"use client"

import React, { useState, useEffect } from 'react'
import { AudioStudio } from '@/components/studio/audio-studio'
import { DJStudio } from '@/components/studio/dj-studio'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Music, Disc3, Sparkles, Users } from 'lucide-react'

export default function StudioPage() {
  const [studioMode, setStudioMode] = useState<'select' | 'audio' | 'dj'>('select')
  const [discoveredTrack, setDiscoveredTrack] = useState<any>(null)
  const [initialMood, setInitialMood] = useState<string>('energetic')

  // Check for discovered track from URL params or state
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const trackId = urlParams.get('track')
    const mood = urlParams.get('mood')

    if (trackId) {
      // In real implementation, fetch track data
      setDiscoveredTrack({ id: trackId, title: 'Discovered Track' })
      setStudioMode('dj') // Auto-open DJ mode for discovered tracks
    }

    if (mood) {
      setInitialMood(mood)
    }
  }, [])

  if (studioMode === 'audio') {
    return <AudioStudio />
  }

  if (studioMode === 'dj') {
    return (
      <DJStudio
        initialMood={initialMood}
        discoveredTrack={discoveredTrack}
      />
    )
  }

  return (
    <div className="h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-4xl w-full space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            HVPPY Central Studio
          </h1>
          <p className="text-lg text-muted-foreground">
            Transform your music discovery into creative expression
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Audio Production Studio */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow group">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                  <Music className="w-6 h-6 text-primary" />
                </div>
                Audio Production Suite
              </CardTitle>
              <CardDescription>
                Professional multi-track audio editing, mixing, and AI-powered music creation tools
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Sparkles className="w-4 h-4 text-primary" />
                  <span>AI Audio Generation & Enhancement</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Music className="w-4 h-4 text-primary" />
                  <span>Multi-track Timeline Editor</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Users className="w-4 h-4 text-primary" />
                  <span>Real-time Collaboration</span>
                </div>
              </div>
              <Button
                onClick={() => setStudioMode('audio')}
                className="w-full"
              >
                Open Audio Studio
              </Button>
            </CardContent>
          </Card>

          {/* DJ & Live Performance Studio */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow group border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-primary to-purple-600 rounded-lg group-hover:scale-110 transition-transform">
                  <Disc3 className="w-6 h-6 text-white" />
                </div>
                DJ & Live Performance
              </CardTitle>
              <CardDescription>
                Professional DJ mixing with mood-based content discovery and live streaming
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Disc3 className="w-4 h-4 text-primary" />
                  <span>Dual-Deck DJ Interface</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Sparkles className="w-4 h-4 text-primary" />
                  <span>Mood-Based Content Discovery</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Users className="w-4 h-4 text-primary" />
                  <span>Live Collaboration & Streaming</span>
                </div>
              </div>
              <Button
                onClick={() => setStudioMode('dj')}
                className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
              >
                Start DJ Session
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Start Options */}
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold">Quick Start</h3>
          <div className="flex justify-center gap-4">
            <Button variant="outline" onClick={() => setStudioMode('dj')}>
              <Disc3 className="w-4 h-4 mr-2" />
              Start DJing Now
            </Button>
            <Button variant="outline" onClick={() => setStudioMode('audio')}>
              <Music className="w-4 h-4 mr-2" />
              Create New Track
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
