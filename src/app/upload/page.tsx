"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";

export default function MediaUploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedPostId, setUploadedPostId] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      setUploadedPostId(null); // Clear previous upload status
    } else {
      setSelectedFile(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a file to upload.");
      return;
    }

    setIsLoading(true);
    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await fetch("/api/media/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setUploadedPostId(data.postId);
        toast.success(`File uploaded successfully! Transcoding initiated for Post ID: ${data.postId}`);
        setSelectedFile(null); // Clear selected file
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "File upload failed.");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("An unexpected error occurred during upload.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg overflow-hidden">
        <CardHeader className="space-y-2 p-6 pb-4">
          <CardTitle className="text-3xl font-extrabold text-center text-gray-900 dark:text-gray-50">Upload Media</CardTitle>
          <CardDescription className="text-center text-gray-600 dark:text-gray-400">Upload your video, audio, or image files.</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-6 p-6 pt-0">
          <div className="grid gap-2">
            <Label htmlFor="media-file">Select File</Label>
            <Input id="media-file" type="file" onChange={handleFileChange} disabled={isLoading} />
          </div>
          {selectedFile && (
            <p className="text-sm text-muted-foreground">Selected: {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)</p>
          )}
          {uploadedPostId && (
            <div className="text-center text-green-600 mt-4">
              <p>Upload successful! Transcoding is in progress.</p>
              <p>Check the <Link href="/gallery" className="underline">media gallery</Link> for updates.</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col gap-4 p-6 pt-0">
          <Button className="w-full py-2 text-lg" onClick={handleUpload} disabled={isLoading || !selectedFile}>
            {isLoading ? "Uploading..." : "Upload File"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}