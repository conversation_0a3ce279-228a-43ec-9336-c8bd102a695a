"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

interface Comment {
  id: string;
  userId: string;
  user: { name: string | null; image: string | null; displayName: string | null; username: string | null };
  postId: string;
  content: string;
  createdAt: string;
}

interface CommentsSectionProps {
  postId: string;
}

export default function CommentsSection({ postId }: CommentsSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isPostingComment, setIsPostingComment] = useState(false);
  const commentsEndRef = useRef<HTMLDivElement>(null);

  const fetchComments = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/post/${postId}/comments`);
      if (response.ok) {
        const data = await response.json();
        setComments(data.comments);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to fetch comments.");
      }
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast.error("An unexpected error occurred while fetching comments.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
    const interval = setInterval(fetchComments, 10000); // Poll for new comments
    return () => clearInterval(interval);
  }, [postId]);

  useEffect(() => {
    commentsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [comments]);

  const handlePostComment = async () => {
    if (!newComment.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }
    if (!session?.user?.id) {
      toast.error("You must be logged in to comment.");
      return;
    }

    setIsPostingComment(true);
    try {
      const response = await fetch(`/api/post/${postId}/comments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: newComment }),
      });

      if (response.ok) {
        const postedComment = await response.json();
        setComments(prev => [...prev, postedComment]);
        setNewComment('');
        toast.success("Comment posted!");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to post comment.");
      }
    } catch (error) {
      console.error("Error posting comment:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setIsPostingComment(false);
    }
  };

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle>Comments</CardTitle>
      </CardHeader>
      <CardContent className="max-h-60 overflow-y-auto p-4 space-y-3 border-b">
        {isLoading ? (
          <p className="text-center text-muted-foreground">Loading comments...</p>
        ) : comments.length === 0 ? (
          <p className="text-center text-muted-foreground">No comments yet. Be the first!</p>
        ) : (
          comments.map((comment) => (
            <div key={comment.id} className="text-sm">
              <Link href={`/profile/${comment.userId}`} className="font-semibold hover:underline">
                {comment.user.displayName || comment.user.name || comment.user.username || "Anonymous"}
              </Link>
              <span className="ml-2 text-gray-500">{new Date(comment.createdAt).toLocaleTimeString()}</span>
              <p>{comment.content}</p>
            </div>
          ))
        )}
        <div ref={commentsEndRef} />
      </CardContent>
      <CardFooter className="p-4">
        <div className="flex w-full space-x-2">
          <Input
            type="text"
            placeholder="Add a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') handlePostComment();
            }}
            disabled={!session?.user?.id || isPostingComment}
          />
          <Button onClick={handlePostComment} disabled={!session?.user?.id || isPostingComment}>
            {isPostingComment ? "Posting..." : "Post"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
