# HVPPY Central Vertical Feed Library

A comprehensive TikTok-inspired vertical feed system built as a reusable library for the HVPPY Central platform. This library provides all the components, hooks, and state management needed to create engaging, mood-based content feeds.

## 🚀 Features

### Core Components
- **VerticalFeedContainer**: Main feed wrapper with infinite scroll
- **ContentCard**: Individual post/content display (TODO)
- **FeedControls**: Like, share, comment, mood reaction buttons (TODO)
- **MoodSelector**: Emotional AI mood selection interface (TODO)
- **CreatorInfo**: Creator/persona information overlay (TODO)
- **FeedNavigation**: Navigation between feed types (TODO)

### Custom Hooks
- **useFeedData**: Fetch and manage feed content with pagination
- **useInfiniteScroll**: Handle infinite scrolling behavior
- **useMoodFilter**: Filter content based on selected moods
- **useContentInteractions**: Handle likes, shares, reactions
- **useVideoPlayer**: Manage video playback in feed

### State Management (Zustand)
- **feedStore**: Current feed state, loaded posts, pagination
- **userPreferencesStore**: Selected moods, feed preferences
- **interactionStore**: User interactions, reactions, engagement data
- **playerStore**: Video/audio player state across feed items

### API Routes
- **POST /api/feed**: Get feed content (following, discover, mood-based)
- **POST /api/feed/mood-match**: Get AI-powered mood-based recommendations
- **POST /api/content/[id]/interact**: Handle post interactions
- **POST /api/content/[id]/memory**: Create/delete fan memories

## 📁 File Structure

```
src/components/feed/
├── index.ts                     # Main exports
├── vertical-feed-container.tsx  # Main feed component
├── content-card.tsx            # Individual post component (TODO)
├── feed-controls.tsx           # Interaction controls (TODO)
├── mood-selector.tsx           # Mood selection UI (TODO)
├── creator-info.tsx            # Creator information (TODO)
├── feed-navigation.tsx         # Feed type navigation (TODO)
└── README.md                   # This file

src/hooks/feed/
├── use-feed-data.ts            # Feed data management
├── use-infinite-scroll.ts      # Scroll behavior
├── use-mood-filter.ts          # Mood filtering
├── use-content-interactions.ts # User interactions
└── use-video-player.ts         # Video player control

src/lib/stores/
├── feed-store.ts               # Feed state management
├── user-preferences-store.ts   # User preferences
├── interaction-store.ts        # User interactions
└── player-store.ts             # Media player state

src/types/
└── feed.ts                     # TypeScript definitions

src/app/api/
├── feed/route.ts               # Main feed API
├── feed/mood-match/route.ts    # Mood matching API
├── content/[id]/interact/route.ts # Interaction API
└── content/[id]/memory/route.ts   # Memory API
```

## 🎯 Usage

### Basic Feed Implementation

```tsx
import { VerticalFeedContainer, FeedType } from '@/components/feed'

export default function FeedPage() {
  const handleItemChange = (item, index) => {
    console.log('Current item:', item.post.title)
  }

  const handleInteraction = (interaction) => {
    console.log('User interaction:', interaction)
  }

  return (
    <VerticalFeedContainer
      feedType={FeedType.DISCOVER}
      onItemChange={handleItemChange}
      onInteraction={handleInteraction}
      autoPlay={true}
    />
  )
}
```

### Using Hooks

```tsx
import { useFeedData, useMoodFilter, useContentInteractions } from '@/components/feed'

function CustomFeedComponent() {
  const { items, loading, loadMore } = useFeedData(FeedType.DISCOVER)
  const { selectedMoods, setSelectedMoods } = useMoodFilter()
  const { react, toggleMemory } = useContentInteractions()

  // Your custom implementation
}
```

### State Management

```tsx
import { useFeedStore, useUserPreferencesStore } from '@/components/feed'

function FeedSettings() {
  const { currentFeed, setCurrentFeed } = useFeedStore()
  const { autoPlay, setAutoPlay } = useUserPreferencesStore()

  // Settings UI
}
```

## 🎨 Mood System

The feed system includes a comprehensive mood-based content discovery system:

### Available Moods
- **Happy**: Uplifting, joyful content
- **Chill**: Relaxed, laid-back vibes
- **Heartbroken**: Emotional, healing content
- **Inspired**: Motivational, creative content
- **Energetic**: High-energy, exciting content
- **Peaceful**: Calm, meditative content
- **Nostalgic**: Reflective, memory-inducing content
- **Excited**: Thrilling, anticipation-building content

### Mood Matching
The system uses AI to match content with user moods:
- Direct mood matches (content tagged with selected mood)
- Similar mood recommendations (related emotional states)
- AI-powered content analysis for better matching

## 🔧 Configuration

### Environment Variables
```env
# Required for AI mood matching
OPENAI_API_KEY=your_openai_api_key

# Database
DATABASE_URL=your_postgresql_url

# Appwrite (for real-time features)
NEXT_PUBLIC_APPWRITE_ENDPOINT=your_appwrite_endpoint
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
```

### Feed Types
- **DISCOVER**: Algorithm-curated content
- **FOLLOWING**: Content from followed creators
- **MOOD_BASED**: Content filtered by selected moods
- **TRENDING**: High-engagement recent content
- **EXPERIMENTAL**: Beta/experimental creator content
- **PERSONA**: Content from specific creator personas

## 🎮 Controls & Navigation

### Keyboard Shortcuts
- **Arrow Keys**: Navigate up/down
- **Space**: Play/pause current item
- **R**: Refresh feed
- **M**: Clear mood filters
- **Home/End**: Jump to first/last item

### Touch/Mouse
- **Scroll/Swipe**: Navigate between items
- **Click dots**: Jump to specific item
- **Tap controls**: Interact with content

## 🧪 Development & Testing

### Seed Data
Use the seed API to populate test data:
```bash
curl -X POST http://localhost:3000/api/seed
```

### Demo Page
Visit `/feed` to see the feed system in action with test data.

### Debug Mode
In development, debug information is shown in the bottom-left corner of the feed.

## 🔮 Future Enhancements

### Planned Components
- [ ] ContentCard with media player
- [ ] FeedControls with reaction animations
- [ ] MoodSelector with visual mood indicators
- [ ] CreatorInfo with persona switching
- [ ] FeedNavigation with unread counts

### Planned Features
- [ ] Real-time content updates via Appwrite
- [ ] Advanced AI content recommendations
- [ ] Creator analytics integration
- [ ] Fan memory sharing system
- [ ] Live streaming integration
- [ ] Collaborative playlists

## 📚 API Reference

### Feed API
```typescript
POST /api/feed
{
  type: FeedType
  cursor?: string
  limit?: number
  filters?: FeedFilters
  userId?: string
}
```

### Mood Match API
```typescript
POST /api/feed/mood-match
{
  userMood: MoodType
  limit?: number
  excludePostIds?: string[]
}
```

### Interaction API
```typescript
POST /api/content/[id]/interact
{
  type: ReactionType
  mood?: MoodType
  metadata?: Record<string, any>
}
```

This library provides a solid foundation for building engaging, mood-aware content feeds that can scale with the HVPPY Central platform's growth.
