"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem, InteractionData } from '@/types/feed'
import { ContentType, ReactionType, InteractionType } from '@/types'
import { FeedVideoPlayer } from '@/components/player/feed-video-player'
import { HVPPYContentPlayer } from '@/components/player/hvppy-content-player'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'

import { createMediaSource } from '@/lib/player/utils'
import { MediaType } from '@/lib/player/types'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Heart, 
  MessageCircle, 
  Share, 
  Bookmark,
  Music,
  Play,
  MoreHorizontal,
  Sparkles
} from 'lucide-react'

interface EnhancedContentCardProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onInteraction?: (interaction: InteractionData) => void
  className?: string
  onRegisterMedia?: (element: HTMLVideoElement | HTMLAudioElement, itemId: string, type: 'video' | 'audio') => void
  onUnregisterMedia?: (itemId: string) => void
}

export function EnhancedContentCard({
  item,
  isActive,
  autoPlay = true,
  onInteraction,
  className,
  onRegisterMedia,
  onUnregisterMedia
}: EnhancedContentCardProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)
  const [showFullDescription, setShowFullDescription] = useState(false)
  
  const { react, unreact, toggleMemory, share } = useContentInteractions()
  const { addToQueue } = usePlayerStore()

  // Check if user has already interacted with this post
  useEffect(() => {
    const userReaction = item.post.reactions.find(r => r.userId === 'current-user') // TODO: Get actual user ID
    setIsLiked(!!userReaction)
    
    const userMemory = item.post.memories.find(m => m.userId === 'current-user') // TODO: Get actual user ID
    setIsSaved(!!userMemory)
  }, [item.post.reactions, item.post.memories])

  // Create media source from post content
  const mediaSource = createMediaSource(
    item.post.id,
    item.post.contentUrl || '',
    {
      type: item.post.contentType === ContentType.MUSIC ? MediaType.AUDIO : MediaType.VIDEO,
      format: item.post.contentUrl?.split('.').pop() || 'mp4'
    }
  )

  // Handle social interactions
  const handleLike = useCallback(async () => {
    try {
      if (isLiked) {
        await unreact(item.post.id)
      } else {
        await react(item.post.id, ReactionType.LIKE)
      }
      setIsLiked(!isLiked)
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.LIKE,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to toggle like:', error)
    }
  }, [isLiked, item.post.id, react, unreact, onInteraction])

  const handleSave = useCallback(async () => {
    try {
      await toggleMemory(item.post.id)
      setIsSaved(!isSaved)
      
      // Add to player queue if saved
      if (!isSaved && mediaSource) {
        addToQueue({
          id: item.post.id,
          source: mediaSource,
          metadata: {
            title: item.post.title,
            artist: item.post.creator?.name || item.post.user.name,
            description: item.post.description
          }
        })
      }
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.MEMORY,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to toggle save:', error)
    }
  }, [isSaved, item.post.id, toggleMemory, mediaSource, addToQueue, onInteraction])

  const handleShare = useCallback(async () => {
    try {
      await share(item.post.id)
      
      // Native share if available
      if (navigator.share) {
        await navigator.share({
          title: item.post.title,
          text: item.post.description || `Check out this ${item.post.contentType.toLowerCase()} by ${item.post.creator?.name || item.post.user.name}`,
          url: `${window.location.origin}/post/${item.post.id}`
        })
      } else {
        // Fallback to clipboard
        await navigator.clipboard?.writeText(`${window.location.origin}/post/${item.post.id}`)
      }
      
      onInteraction?.({
        postId: item.post.id,
        type: InteractionType.SHARE,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Failed to share:', error)
    }
  }, [item.post, share, onInteraction])

  const handleComment = useCallback(() => {
    // TODO: Open comment modal or navigate to post detail
    onInteraction?.({
      postId: item.post.id,
      type: 'COMMENT',
      timestamp: new Date()
    })
  }, [item.post.id, onInteraction])

  // Determine if this is HVPPY content or user-generated content
  const isHVPPYContent = item.post.creator?.name === 'HVPPY' ||
                        item.post.contentUrl?.includes('hvppy-content') ||
                        (!!item.post.contentUrl && item.post.contentUrl.startsWith('hvppy-'))

  // Use contentUrl if available and valid, otherwise fall back to regular media player
  const contentId = item.post.contentUrl || item.post.id
  const hasValidHVPPYContent = !!item.post.contentUrl && (
    item.post.contentUrl.startsWith('hvppy-') ||
    item.post.contentUrl.startsWith('love-heals-')
  )

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-screen snap-start snap-always bg-black overflow-hidden',
        className
      )}
      data-post-id={item.post.id}
    >
      {/* Media Player */}
      {isHVPPYContent ? (
        // Use HVPPY content player for classified content
        <HVPPYContentPlayer
          contentId={contentId}
          className="w-full h-full"
          autoPlay={isActive && autoPlay}
          variant="default"
        />
      ) : item.post.contentType === ContentType.VIDEO ? (
        // Use feed video player for user videos
        <FeedVideoPlayer
          contentId={item.post.id}
          source={mediaSource}
          metadata={{
            title: item.post.title,
            artist: item.post.creator?.name || item.post.user.name,
            description: item.post.description,
            tags: item.post.tags,
            moods: item.post.moods,
            thumbnail: item.post.thumbnailUrl,
            likes: item.post._count.reactions,
            comments: item.post._count.memories // Using memories as comments for now
          }}
          onLike={handleLike}
          onShare={handleShare}
          onComment={handleComment}
          onSave={handleSave}
        />
      ) : (
        // Audio content with custom overlay
        <div className="relative w-full h-full bg-gradient-to-br from-purple-900/20 to-pink-900/20">
          <HVPPYContentPlayer
            contentId={contentId}
            className="w-full h-full"
            autoPlay={isActive && autoPlay}
            variant="card"
          />
          
          {/* Audio-specific overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center text-white">
              <Music className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-2xl font-bold mb-2">{item.post.title}</h3>
              <p className="text-lg opacity-80">
                {item.post.creator?.name || item.post.user.name}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Feed Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Creator Info - Top Left */}
        <div className="absolute top-4 left-4 flex items-center space-x-3 pointer-events-auto">
          <Avatar className="w-12 h-12 border-2 border-white/20">
            <AvatarImage 
              src={item.post.creator?.avatarUrl || item.post.user.avatarUrl} 
              alt={item.post.creator?.name || item.post.user.name}
            />
            <AvatarFallback className="bg-purple-500 text-white">
              {(item.post.creator?.name || item.post.user.name)?.[0]?.toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h4 className="text-white font-semibold">
              {item.post.creator?.name || item.post.user.name}
            </h4>
            {item.post.creator && (
              <div className="flex items-center space-x-1">
                <Sparkles className="w-3 h-3 text-purple-400" />
                <span className="text-purple-400 text-xs">Creator</span>
              </div>
            )}
          </div>
        </div>

        {/* Mood Indicators - Top Right */}
        {item.post.moods && item.post.moods.length > 0 && (
          <div className="absolute top-4 right-4 flex flex-wrap gap-2 pointer-events-auto">
            {item.post.moods.slice(0, 3).map((mood) => (
              <Badge
                key={mood}
                variant="secondary"
                className="bg-purple-500/20 text-purple-200 border-purple-400/30 text-xs"
              >
                {mood}
              </Badge>
            ))}
          </div>
        )}

        {/* Social Actions - Right Side */}
        <div className="absolute right-4 bottom-32 flex flex-col space-y-6 pointer-events-auto">
          {/* Like */}
          <button
            onClick={handleLike}
            className={cn(
              "flex flex-col items-center space-y-1 transition-all duration-200",
              isLiked ? "text-red-500 scale-110" : "text-white"
            )}
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/50 transition-colors">
              <Heart className={cn("w-6 h-6", isLiked && "fill-current")} />
            </div>
            <span className="text-xs font-medium">
              {item.post._count.reactions}
            </span>
          </button>

          {/* Comment */}
          <button
            onClick={handleComment}
            className="flex flex-col items-center space-y-1 text-white transition-transform hover:scale-110"
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/50 transition-colors">
              <MessageCircle className="w-6 h-6" />
            </div>
            <span className="text-xs font-medium">
              {item.post._count.memories}
            </span>
          </button>

          {/* Save */}
          <button
            onClick={handleSave}
            className={cn(
              "flex flex-col items-center space-y-1 transition-all duration-200",
              isSaved ? "text-yellow-500 scale-110" : "text-white"
            )}
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/50 transition-colors">
              <Bookmark className={cn("w-6 h-6", isSaved && "fill-current")} />
            </div>
            <span className="text-xs font-medium">Save</span>
          </button>

          {/* Share */}
          <button
            onClick={handleShare}
            className="flex flex-col items-center space-y-1 text-white transition-transform hover:scale-110"
          >
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/50 transition-colors">
              <Share className="w-6 h-6" />
            </div>
            <span className="text-xs font-medium">Share</span>
          </button>

          {/* More Options */}
          <button className="flex flex-col items-center space-y-1 text-white transition-transform hover:scale-110">
            <div className="w-12 h-12 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/50 transition-colors">
              <MoreHorizontal className="w-6 h-6" />
            </div>
          </button>
        </div>

        {/* Content Info - Bottom */}
        <div className="absolute bottom-0 left-0 right-16 p-4 bg-gradient-to-t from-black/80 via-black/40 to-transparent pointer-events-auto">
          <div className="space-y-3">
            <h3 className="text-white font-bold text-lg leading-tight">
              {item.post.title}
            </h3>
            
            {item.post.description && (
              <div>
                <p className={cn(
                  "text-white/90 text-sm leading-relaxed",
                  !showFullDescription && "line-clamp-2"
                )}>
                  {item.post.description}
                </p>
                {item.post.description.length > 100 && (
                  <button
                    onClick={() => setShowFullDescription(!showFullDescription)}
                    className="text-white/70 text-sm mt-1 hover:text-white transition-colors"
                  >
                    {showFullDescription ? 'Show less' : 'Show more'}
                  </button>
                )}
              </div>
            )}

            {/* Tags */}
            {item.post.tags && item.post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {item.post.tags.slice(0, 5).map((tag) => (
                  <span
                    key={tag}
                    className="text-white/70 text-sm hover:text-purple-400 cursor-pointer transition-colors"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}

            {/* Engagement Stats */}
            <div className="flex items-center space-x-4 text-white/70 text-sm">
              <span>{item.post._count.reactions} likes</span>
              <span>{item.post._count.memories} saves</span>
              {item.feedScore && (
                <span className="text-purple-400">
                  {Math.round(item.feedScore * 100)}% match
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Active Indicator */}
        {isActive && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
          </div>
        )}
      </div>
    </div>
  )
}
