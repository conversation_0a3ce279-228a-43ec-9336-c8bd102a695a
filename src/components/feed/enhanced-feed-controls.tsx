"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { EnhancedMoodSelector } from './enhanced-mood-selector'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { 
  Settings, 
  Filter, 
  Shuffle, 
  TrendingUp,
  Clock,
  Heart,
  Bookmark,
  Music,
  Video,
  Headphones,
  Volume2,
  VolumeX,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Repeat,
  List,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface EnhancedFeedControlsProps {
  feedType: 'discover' | 'following' | 'trending' | 'mood'
  currentIndex: number
  totalItems: number
  isPlaying: boolean
  onFeedTypeChange: (type: string) => void
  onPrevious: () => void
  onNext: () => void
  onTogglePlay: () => void
  onShuffle: () => void
  className?: string
}

export function EnhancedFeedControls({
  feedType,
  currentIndex,
  totalItems,
  isPlaying,
  onFeedTypeChange,
  onPrevious,
  onNext,
  onTogglePlay,
  onShuffle,
  className
}: EnhancedFeedControlsProps) {
  const [showMoodSelector, setShowMoodSelector] = useState(false)
  const [showPlayerControls, setShowPlayerControls] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const {
    currentPlayer,
    globalVolume,
    globalMuted,
    queue,
    currentTrack,
    setGlobalVolume,
    setGlobalMuted,
    playNext,
    playPrevious
  } = usePlayerStore()

  const {
    selectedMoods,
    clearMoods
  } = useMoodFilter()

  const handleVolumeChange = useCallback((volume: number) => {
    setGlobalVolume(volume / 100)
  }, [setGlobalVolume])

  const handleVolumeToggle = useCallback(() => {
    setGlobalMuted(!globalMuted)
  }, [globalMuted, setGlobalMuted])

  const feedTypes = [
    { id: 'discover', label: 'Discover', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'following', label: 'Following', icon: <Heart className="w-4 h-4" /> },
    { id: 'trending', label: 'Trending', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'mood', label: 'Mood', icon: <Music className="w-4 h-4" /> }
  ]

  return (
    <div className={cn('space-y-4', className)}>
      {/* Feed Type Selector */}
      <Card className="p-4 bg-black/40 backdrop-blur-md border-white/10">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-semibold">Feed Type</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={cn(
              "text-white hover:bg-white/20 p-2",
              showFilters && "bg-white/10"
            )}
          >
            <Filter className="w-4 h-4" />
          </Button>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {feedTypes.map((type) => (
            <Button
              key={type.id}
              variant={feedType === type.id ? "default" : "outline"}
              size="sm"
              onClick={() => onFeedTypeChange(type.id)}
              className={cn(
                "flex items-center space-x-2 transition-all duration-200",
                feedType === type.id
                  ? "bg-purple-500 text-white hover:bg-purple-600"
                  : "text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
              )}
            >
              {type.icon}
              <span>{type.label}</span>
            </Button>
          ))}
        </div>

        {/* Active Filters Summary */}
        {(selectedMoods.length > 0 || showFilters) && (
          <div className="mt-3 pt-3 border-t border-white/10">
            <div className="flex items-center justify-between">
              <span className="text-white/70 text-sm">
                {selectedMoods.length > 0 
                  ? `${selectedMoods.length} mood filter${selectedMoods.length > 1 ? 's' : ''} active`
                  : 'No filters active'
                }
              </span>
              {selectedMoods.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearMoods}
                  className="text-white/70 hover:text-white text-xs"
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        )}
      </Card>

      {/* Mood Selector */}
      {(feedType === 'mood' || showMoodSelector) && (
        <Card className="p-4 bg-black/40 backdrop-blur-md border-white/10">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-semibold">Mood Selection</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMoodSelector(!showMoodSelector)}
              className="text-white hover:bg-white/20 p-2"
            >
              {showMoodSelector ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
          </div>

          <EnhancedMoodSelector
            variant="grid"
            showCounts={true}
            showEQPreview={true}
            maxVisible={8}
          />
        </Card>
      )}

      {/* Player Controls */}
      <Card className="p-4 bg-black/40 backdrop-blur-md border-white/10">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-semibold">Playback</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowPlayerControls(!showPlayerControls)}
            className="text-white hover:bg-white/20 p-2"
          >
            {showPlayerControls ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>
        </div>

        {/* Basic Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onPrevious}
              disabled={currentIndex === 0}
              className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
            >
              <SkipBack className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onTogglePlay}
              className="text-white hover:bg-white/20 p-2"
            >
              {isPlaying ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onNext}
              disabled={currentIndex === totalItems - 1}
              className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-white/70 text-sm">
              {currentIndex + 1} / {totalItems}
            </span>

            <Button
              variant="ghost"
              size="sm"
              onClick={onShuffle}
              className="text-white hover:bg-white/20 p-2"
            >
              <Shuffle className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Extended Controls */}
        {showPlayerControls && (
          <div className="mt-4 space-y-3">
            {/* Volume Control */}
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleVolumeToggle}
                className="text-white hover:bg-white/20 p-2"
              >
                {globalMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>

              <div className="flex-1">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={globalMuted ? 0 : globalVolume * 100}
                  onChange={(e) => handleVolumeChange(Number(e.target.value))}
                  className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              <span className="text-white/70 text-sm w-10">
                {globalMuted ? '0%' : `${Math.round(globalVolume * 100)}%`}
              </span>
            </div>

            {/* Current Track Info */}
            {currentTrack && (
              <div className="p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded flex items-center justify-center">
                    {currentTrack.source.type === 'audio' ? (
                      <Music className="w-5 h-5 text-purple-400" />
                    ) : (
                      <Video className="w-5 h-5 text-purple-400" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">
                      {currentTrack.metadata?.title || 'Unknown Track'}
                    </p>
                    <p className="text-white/70 text-sm truncate">
                      {currentTrack.metadata?.artist || 'Unknown Artist'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Queue Info */}
            {queue.length > 0 && (
              <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-2">
                  <List className="w-4 h-4 text-white/70" />
                  <span className="text-white/70 text-sm">
                    {queue.length} item{queue.length > 1 ? 's' : ''} in queue
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white text-xs"
                >
                  View Queue
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Quick Actions */}
      <Card className="p-4 bg-black/40 backdrop-blur-md border-white/10">
        <h3 className="text-white font-semibold mb-3">Quick Actions</h3>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMoodSelector(!showMoodSelector)}
            className="text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
          >
            <Music className="w-4 h-4 mr-2" />
            Moods
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
          >
            <Bookmark className="w-4 h-4 mr-2" />
            Saved
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
          >
            <Clock className="w-4 h-4 mr-2" />
            History
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
          >
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </Card>
    </div>
  )
}
