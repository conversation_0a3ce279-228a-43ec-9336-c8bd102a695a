"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { VerticalFeedContainer } from './vertical-feed-container'
import { EnhancedFeedNavigation } from './enhanced-feed-navigation'
import { EnhancedFeedControls } from './enhanced-feed-controls'
import { EnhancedMoodSelector } from './enhanced-mood-selector'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { FeedItem, InteractionData } from '@/types/feed'

interface EnhancedFeedExampleProps {
  className?: string
  initialFeedType?: 'discover' | 'following' | 'trending' | 'mood'
  showSidebar?: boolean
}

export function EnhancedFeedExample({
  className,
  initialFeedType = 'discover',
  showSidebar = true
}: EnhancedFeedExampleProps) {
  const [feedType, setFeedType] = useState(initialFeedType)
  const [currentItem, setCurrentItem] = useState<FeedItem | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [totalItems, setTotalItems] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showControls, setShowControls] = useState(false)

  const { currentPlayer, pauseAll } = usePlayerStore()
  const { selectedMoods } = useMoodFilter()

  // Handle feed type changes
  const handleFeedTypeChange = useCallback((type: string) => {
    setFeedType(type as any)
    // Reset to first item when changing feed type
    setCurrentIndex(0)
  }, [])

  // Handle item changes in the feed
  const handleItemChange = useCallback((item: FeedItem, index: number) => {
    setCurrentItem(item)
    setCurrentIndex(index)
    
    // Update playing state based on current player
    if (currentPlayer) {
      const state = currentPlayer.getState()
      setIsPlaying(state.state === 'playing')
    }
  }, [currentPlayer])

  // Handle user interactions
  const handleInteraction = useCallback((interaction: InteractionData) => {
    console.log('Feed interaction:', interaction)
    
    // Track analytics
    // analytics.track('feed_interaction', {
    //   type: interaction.type,
    //   postId: interaction.postId,
    //   timestamp: interaction.timestamp
    // })
  }, [])

  // Navigation controls
  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      // This would be handled by the VerticalFeedContainer's scroll logic
      console.log('Navigate to previous item')
    }
  }, [currentIndex])

  const handleNext = useCallback(() => {
    // This would be handled by the VerticalFeedContainer's scroll logic
    console.log('Navigate to next item')
  }, [])

  const handleTogglePlay = useCallback(() => {
    if (currentPlayer) {
      if (isPlaying) {
        currentPlayer.pause()
      } else {
        currentPlayer.play().catch(console.error)
      }
    }
  }, [currentPlayer, isPlaying])

  const handleShuffle = useCallback(() => {
    // Implement shuffle logic
    console.log('Shuffle feed')
  }, [])

  const handleScrollToIndex = useCallback((index: number) => {
    // This would be implemented by the VerticalFeedContainer
    console.log('Scroll to index:', index)
  }, [])

  // Create filters based on selected moods
  const filters = {
    moods: selectedMoods,
    contentType: feedType === 'mood' ? undefined : 'ALL'
  }

  return (
    <div className={cn('flex h-screen bg-black', className)}>
      {/* Sidebar Controls */}
      {showSidebar && (
        <div className="w-80 bg-gray-900/50 backdrop-blur-md border-r border-white/10 overflow-y-auto">
          <div className="p-4 space-y-6">
            {/* Feed Controls */}
            <EnhancedFeedControls
              feedType={feedType}
              currentIndex={currentIndex}
              totalItems={totalItems}
              isPlaying={isPlaying}
              onFeedTypeChange={handleFeedTypeChange}
              onPrevious={handlePrevious}
              onNext={handleNext}
              onTogglePlay={handleTogglePlay}
              onShuffle={handleShuffle}
            />

            {/* Mood Selector for Mood Feed */}
            {feedType === 'mood' && (
              <div className="p-4 bg-black/40 backdrop-blur-md border border-white/10 rounded-lg">
                <h3 className="text-white font-semibold mb-4">Select Moods</h3>
                <EnhancedMoodSelector
                  variant="compact"
                  showCounts={true}
                  showEQPreview={true}
                  maxVisible={6}
                />
              </div>
            )}

            {/* Current Item Info */}
            {currentItem && (
              <div className="p-4 bg-black/40 backdrop-blur-md border border-white/10 rounded-lg">
                <h3 className="text-white font-semibold mb-3">Now Playing</h3>
                <div className="space-y-2">
                  <h4 className="text-white font-medium">
                    {currentItem.post.title}
                  </h4>
                  <p className="text-white/70 text-sm">
                    {currentItem.post.creator?.name || currentItem.post.user.name}
                  </p>
                  {currentItem.post.moods && currentItem.post.moods.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {currentItem.post.moods.slice(0, 3).map((mood) => (
                        <span
                          key={mood}
                          className="px-2 py-1 bg-purple-500/20 text-purple-200 text-xs rounded-full"
                        >
                          {mood}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Debug Info */}
            <div className="p-4 bg-black/40 backdrop-blur-md border border-white/10 rounded-lg">
              <h3 className="text-white font-semibold mb-3">Debug Info</h3>
              <div className="space-y-1 text-xs text-white/70">
                <div>Feed Type: {feedType}</div>
                <div>Current Index: {currentIndex}</div>
                <div>Total Items: {totalItems}</div>
                <div>Is Playing: {isPlaying ? 'Yes' : 'No'}</div>
                <div>Selected Moods: {selectedMoods.join(', ') || 'None'}</div>
                <div>Has Player: {currentPlayer ? 'Yes' : 'No'}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Feed Container */}
      <div className="flex-1 relative">
        <VerticalFeedContainer
          feedType={feedType}
          filters={filters}
          onItemChange={(item, index) => {
            handleItemChange(item, index)
            setTotalItems(index + 1) // This is a simplified way to track total
          }}
          onInteraction={handleInteraction}
          autoPlay={true}
          className="h-full"
        />

        {/* Bottom Navigation */}
        <EnhancedFeedNavigation
          currentIndex={currentIndex}
          totalItems={totalItems}
          isPlaying={isPlaying}
          onPrevious={handlePrevious}
          onNext={handleNext}
          onTogglePlay={handleTogglePlay}
          onScrollToIndex={handleScrollToIndex}
          showMoodFilter={!showSidebar}
          showPlayerControls={true}
        />

        {/* Floating Controls Toggle */}
        {!showSidebar && (
          <button
            onClick={() => setShowControls(!showControls)}
            className="fixed top-4 left-4 z-50 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        )}

        {/* Floating Controls Panel */}
        {!showSidebar && showControls && (
          <div className="fixed top-16 left-4 z-50 w-80 max-h-[80vh] overflow-y-auto">
            <EnhancedFeedControls
              feedType={feedType}
              currentIndex={currentIndex}
              totalItems={totalItems}
              isPlaying={isPlaying}
              onFeedTypeChange={handleFeedTypeChange}
              onPrevious={handlePrevious}
              onNext={handleNext}
              onTogglePlay={handleTogglePlay}
              onShuffle={handleShuffle}
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Mobile-optimized version
export function MobileEnhancedFeed(props: Omit<EnhancedFeedExampleProps, 'showSidebar'>) {
  return (
    <EnhancedFeedExample
      {...props}
      showSidebar={false}
      className="mobile-feed"
    />
  )
}

// Desktop version with sidebar
export function DesktopEnhancedFeed(props: Omit<EnhancedFeedExampleProps, 'showSidebar'>) {
  return (
    <EnhancedFeedExample
      {...props}
      showSidebar={true}
      className="desktop-feed"
    />
  )
}
