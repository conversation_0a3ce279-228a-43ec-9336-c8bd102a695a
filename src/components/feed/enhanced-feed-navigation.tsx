"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Shuffle,
  Repeat,
  Volume2,
  VolumeX,
  List,
  Heart,
  Bookmark,
  Settings,
  Filter,
  Sparkles,
  Music,
  Video,
  Headphones
} from 'lucide-react'

interface EnhancedFeedNavigationProps {
  currentIndex: number
  totalItems: number
  isPlaying: boolean
  onPrevious: () => void
  onNext: () => void
  onTogglePlay: () => void
  onScrollToIndex: (index: number) => void
  className?: string
  showMoodFilter?: boolean
  showPlayerControls?: boolean
}

export function EnhancedFeedNavigation({
  currentIndex,
  totalItems,
  isPlaying,
  onPrevious,
  onNext,
  onTogglePlay,
  onScrollToIndex,
  className,
  showMoodFilter = true,
  showPlayerControls = true
}: EnhancedFeedNavigationProps) {
  const [showFilters, setShowFilters] = useState(false)
  const [showQueue, setShowQueue] = useState(false)
  
  const {
    currentPlayer,
    globalVolume,
    globalMuted,
    queue,
    setGlobalVolume,
    setGlobalMuted,
    clearQueue
  } = usePlayerStore()

  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
    availableMoods
  } = useMoodFilter()

  const handleMoodToggle = useCallback((mood: string) => {
    const newMoods = selectedMoods.includes(mood)
      ? selectedMoods.filter(m => m !== mood)
      : [...selectedMoods, mood]
    setSelectedMoods(newMoods)
  }, [selectedMoods, setSelectedMoods])

  const handleVolumeToggle = useCallback(() => {
    setGlobalMuted(!globalMuted)
  }, [globalMuted, setGlobalMuted])

  return (
    <div className={cn(
      'fixed bottom-0 left-0 right-0 bg-black/80 backdrop-blur-md border-t border-white/10 z-50',
      className
    )}>
      {/* Mood Filter Bar */}
      {showMoodFilter && showFilters && (
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-semibold text-sm">Filter by Mood</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearMoods}
              className="text-white/70 hover:text-white text-xs"
            >
              Clear All
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {availableMoods.map((mood) => (
              <Badge
                key={mood}
                variant={selectedMoods.includes(mood) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all duration-200 text-xs",
                  selectedMoods.includes(mood)
                    ? "bg-purple-500 text-white hover:bg-purple-600"
                    : "text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
                )}
                onClick={() => handleMoodToggle(mood)}
              >
                <Sparkles className="w-3 h-3 mr-1" />
                {mood}
                {moodCounts[mood] && (
                  <span className="ml-1 opacity-70">({moodCounts[mood]})</span>
                )}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Queue Panel */}
      {showQueue && queue.length > 0 && (
        <div className="p-4 border-b border-white/10 max-h-40 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-semibold text-sm">
              Queue ({queue.length})
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearQueue}
              className="text-white/70 hover:text-white text-xs"
            >
              Clear
            </Button>
          </div>
          
          <div className="space-y-2">
            {queue.slice(0, 5).map((item, index) => (
              <div
                key={item.id}
                className="flex items-center space-x-3 p-2 rounded bg-white/5 hover:bg-white/10 transition-colors"
              >
                <div className="w-8 h-8 bg-purple-500/20 rounded flex items-center justify-center">
                  {item.source.type === 'audio' ? (
                    <Music className="w-4 h-4 text-purple-400" />
                  ) : (
                    <Video className="w-4 h-4 text-purple-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate">
                    {item.metadata?.title || 'Unknown'}
                  </p>
                  <p className="text-white/70 text-xs truncate">
                    {item.metadata?.artist || 'Unknown Artist'}
                  </p>
                </div>
              </div>
            ))}
            {queue.length > 5 && (
              <p className="text-white/50 text-xs text-center">
                +{queue.length - 5} more items
              </p>
            )}
          </div>
        </div>
      )}

      {/* Main Navigation */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          {/* Left: Navigation Controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onPrevious}
              disabled={currentIndex === 0}
              className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
            >
              <SkipBack className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onTogglePlay}
              className="text-white hover:bg-white/20 p-2"
            >
              {isPlaying ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onNext}
              disabled={currentIndex === totalItems - 1}
              className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          {/* Center: Progress Indicator */}
          <div className="flex items-center space-x-3">
            <span className="text-white/70 text-sm">
              {currentIndex + 1} / {totalItems}
            </span>
            
            <div className="w-24 h-1 bg-white/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-purple-500 transition-all duration-300"
                style={{ 
                  width: `${((currentIndex + 1) / totalItems) * 100}%` 
                }}
              />
            </div>
          </div>

          {/* Right: Action Controls */}
          <div className="flex items-center space-x-2">
            {/* Volume Control */}
            {showPlayerControls && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleVolumeToggle}
                className="text-white hover:bg-white/20 p-2"
              >
                {globalMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
            )}

            {/* Queue Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowQueue(!showQueue)}
              className={cn(
                "text-white hover:bg-white/20 p-2 relative",
                showQueue && "bg-white/10"
              )}
            >
              <List className="w-4 h-4" />
              {queue.length > 0 && (
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center">
                  {queue.length > 9 ? '9+' : queue.length}
                </span>
              )}
            </Button>

            {/* Filter Toggle */}
            {showMoodFilter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className={cn(
                  "text-white hover:bg-white/20 p-2",
                  (showFilters || selectedMoods.length > 0) && "bg-white/10"
                )}
              >
                <Filter className="w-4 h-4" />
                {selectedMoods.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center">
                    {selectedMoods.length}
                  </span>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Quick Jump Navigation */}
        <div className="flex items-center justify-center mt-3 space-x-1">
          {Array.from({ length: Math.min(totalItems, 10) }, (_, i) => {
            const index = Math.floor((i / 9) * (totalItems - 1))
            return (
              <button
                key={i}
                onClick={() => onScrollToIndex(index)}
                className={cn(
                  "w-2 h-2 rounded-full transition-all duration-200",
                  index === currentIndex 
                    ? "bg-purple-500 scale-125" 
                    : "bg-white/30 hover:bg-white/50"
                )}
                aria-label={`Go to item ${index + 1}`}
              />
            )
          })}
        </div>
      </div>
    </div>
  )
}

// Compact version for mobile
export function CompactFeedNavigation(props: EnhancedFeedNavigationProps) {
  return (
    <EnhancedFeedNavigation
      {...props}
      showMoodFilter={false}
      showPlayerControls={false}
      className="pb-safe"
    />
  )
}
