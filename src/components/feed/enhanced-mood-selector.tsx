"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import { 
  <PERSON>rkles, 
  Heart, 
  Zap, 
  Smile, 
  Music, 
  Brain,
  Flame,
  Star,
  Sun,
  Moon,
  RefreshCw,
  TrendingUp,
  Volume2
} from 'lucide-react'

interface MoodInfo {
  name: string
  description: string
  color: string
  icon: React.ReactNode
  gradient: string
  count?: number
}

const MOOD_ICONS: Record<string, React.ReactNode> = {
  energetic: <Zap className="w-4 h-4" />,
  confident: <Flame className="w-4 h-4" />,
  peaceful: <Moon className="w-4 h-4" />,
  happy: <Smile className="w-4 h-4" />,
  emotional: <Heart className="w-4 h-4" />,
  inspired: <Star className="w-4 h-4" />,
  focused: <Brain className="w-4 h-4" />,
  uplifting: <Sun className="w-4 h-4" />,
  healing: <Sparkles className="w-4 h-4" />,
  authentic: <Music className="w-4 h-4" />
}

const MOOD_GRADIENTS: Record<string, string> = {
  energetic: 'from-red-500 to-orange-500',
  confident: 'from-purple-500 to-pink-500',
  peaceful: 'from-blue-500 to-indigo-500',
  happy: 'from-yellow-400 to-orange-400',
  emotional: 'from-pink-500 to-rose-500',
  inspired: 'from-purple-400 to-blue-500',
  focused: 'from-green-500 to-teal-500',
  uplifting: 'from-yellow-300 to-pink-400',
  healing: 'from-green-400 to-blue-400',
  authentic: 'from-gray-600 to-gray-800'
}

interface EnhancedMoodSelectorProps {
  onMoodChange?: (moods: string[]) => void
  className?: string
  variant?: 'grid' | 'horizontal' | 'compact'
  showCounts?: boolean
  showEQPreview?: boolean
  maxVisible?: number
}

export function EnhancedMoodSelector({
  onMoodChange,
  className,
  variant = 'grid',
  showCounts = true,
  showEQPreview = false,
  maxVisible = 10
}: EnhancedMoodSelectorProps) {
  const [moodData, setMoodData] = useState<MoodInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [hoveredMood, setHoveredMood] = useState<string | null>(null)
  
  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
    availableMoods
  } = useMoodFilter()

  const { updatePreferences, preferences } = usePlayerStore()

  // Load mood data from API
  useEffect(() => {
    const loadMoodData = async () => {
      try {
        const response = await fetch('/api/player/content/mood/all', {
          method: 'OPTIONS' // This endpoint returns all available moods
        })
        
        if (response.ok) {
          const data = await response.json()
          const moods: MoodInfo[] = data.moods.map((mood: any) => ({
            name: mood.name,
            description: mood.description || `Content tagged with ${mood.name} mood`,
            color: mood.color || '#8B5CF6',
            icon: MOOD_ICONS[mood.name] || <Sparkles className="w-4 h-4" />,
            gradient: MOOD_GRADIENTS[mood.name] || 'from-purple-500 to-pink-500',
            count: moodCounts[mood.name] || 0
          }))
          
          setMoodData(moods.slice(0, maxVisible))
        }
      } catch (error) {
        console.error('Failed to load mood data:', error)
        // Fallback to default moods
        const defaultMoods = availableMoods.slice(0, maxVisible).map(mood => ({
          name: mood,
          description: `Content tagged with ${mood} mood`,
          color: '#8B5CF6',
          icon: MOOD_ICONS[mood] || <Sparkles className="w-4 h-4" />,
          gradient: MOOD_GRADIENTS[mood] || 'from-purple-500 to-pink-500',
          count: moodCounts[mood] || 0
        }))
        setMoodData(defaultMoods)
      } finally {
        setLoading(false)
      }
    }

    loadMoodData()
  }, [availableMoods, moodCounts, maxVisible])

  const handleMoodToggle = useCallback((mood: string) => {
    const newMoods = selectedMoods.includes(mood)
      ? selectedMoods.filter(m => m !== mood)
      : [...selectedMoods, mood]
    
    setSelectedMoods(newMoods)
    onMoodChange?.(newMoods)

    // Update EQ preference if enabled
    if (showEQPreview && newMoods.length === 1) {
      updatePreferences({ equalizerPreset: newMoods[0] })
    }
  }, [selectedMoods, setSelectedMoods, onMoodChange, showEQPreview, updatePreferences])

  const handleMoodHover = useCallback((mood: string | null) => {
    setHoveredMood(mood)
    
    // Preview EQ settings on hover
    if (showEQPreview && mood && preferences.enableVisualizer) {
      // This would trigger a preview of the EQ settings
      console.log(`Previewing EQ for mood: ${mood}`)
    }
  }, [showEQPreview, preferences.enableVisualizer])

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center p-4', className)}>
        <RefreshCw className="w-5 h-5 animate-spin text-white/50" />
      </div>
    )
  }

  if (variant === 'horizontal') {
    return (
      <div className={cn('flex items-center space-x-2 overflow-x-auto pb-2', className)}>
        {moodData.map((mood) => (
          <Badge
            key={mood.name}
            variant={selectedMoods.includes(mood.name) ? "default" : "outline"}
            className={cn(
              "cursor-pointer transition-all duration-200 whitespace-nowrap",
              selectedMoods.includes(mood.name)
                ? `bg-gradient-to-r ${mood.gradient} text-white hover:scale-105`
                : "text-white/70 border-white/30 hover:bg-white/10 hover:text-white"
            )}
            onClick={() => handleMoodToggle(mood.name)}
            onMouseEnter={() => handleMoodHover(mood.name)}
            onMouseLeave={() => handleMoodHover(null)}
          >
            {mood.icon}
            <span className="ml-1 capitalize">{mood.name}</span>
            {showCounts && mood.count && mood.count > 0 && (
              <span className="ml-1 opacity-70">({mood.count})</span>
            )}
          </Badge>
        ))}
        
        {selectedMoods.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearMoods}
            className="text-white/70 hover:text-white whitespace-nowrap"
          >
            Clear
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex flex-wrap gap-2', className)}>
        {moodData.slice(0, 6).map((mood) => (
          <button
            key={mood.name}
            onClick={() => handleMoodToggle(mood.name)}
            onMouseEnter={() => handleMoodHover(mood.name)}
            onMouseLeave={() => handleMoodHover(null)}
            className={cn(
              "flex items-center space-x-1 px-3 py-1 rounded-full text-sm transition-all duration-200",
              selectedMoods.includes(mood.name)
                ? `bg-gradient-to-r ${mood.gradient} text-white scale-105`
                : "bg-white/10 text-white/70 hover:bg-white/20 hover:text-white"
            )}
          >
            {mood.icon}
            <span className="capitalize">{mood.name}</span>
          </button>
        ))}
      </div>
    )
  }

  // Grid variant (default)
  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-white font-semibold">Choose Your Mood</h3>
        {selectedMoods.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearMoods}
            className="text-white/70 hover:text-white"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Mood Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
        {moodData.map((mood) => (
          <Card
            key={mood.name}
            className={cn(
              "relative overflow-hidden cursor-pointer transition-all duration-300 border-white/10",
              selectedMoods.includes(mood.name)
                ? `bg-gradient-to-br ${mood.gradient} border-transparent scale-105 shadow-lg`
                : "bg-white/5 hover:bg-white/10 hover:scale-102",
              hoveredMood === mood.name && "ring-2 ring-white/30"
            )}
            onClick={() => handleMoodToggle(mood.name)}
            onMouseEnter={() => handleMoodHover(mood.name)}
            onMouseLeave={() => handleMoodHover(null)}
          >
            <div className="p-4 text-center">
              <div className="flex justify-center mb-2">
                <div className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center",
                  selectedMoods.includes(mood.name)
                    ? "bg-white/20"
                    : "bg-white/10"
                )}>
                  {mood.icon}
                </div>
              </div>
              
              <h4 className="text-white font-medium text-sm capitalize mb-1">
                {mood.name}
              </h4>
              
              {showCounts && mood.count && mood.count > 0 && (
                <p className="text-white/70 text-xs">
                  {mood.count} tracks
                </p>
              )}

              {/* EQ Preview Indicator */}
              {showEQPreview && selectedMoods.includes(mood.name) && (
                <div className="absolute top-2 right-2">
                  <Volume2 className="w-3 h-3 text-white/70" />
                </div>
              )}

              {/* Trending Indicator */}
              {mood.count && mood.count > 10 && (
                <div className="absolute top-2 left-2">
                  <TrendingUp className="w-3 h-3 text-white/70" />
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* Selected Moods Summary */}
      {selectedMoods.length > 0 && (
        <div className="p-3 bg-white/5 rounded-lg border border-white/10">
          <div className="flex items-center space-x-2 mb-2">
            <Sparkles className="w-4 h-4 text-purple-400" />
            <span className="text-white font-medium text-sm">
              Active Filters ({selectedMoods.length})
            </span>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {selectedMoods.map((mood) => {
              const moodInfo = moodData.find(m => m.name === mood)
              return (
                <Badge
                  key={mood}
                  className={cn(
                    "bg-gradient-to-r text-white",
                    moodInfo?.gradient || 'from-purple-500 to-pink-500'
                  )}
                >
                  {moodInfo?.icon}
                  <span className="ml-1 capitalize">{mood}</span>
                </Badge>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
