import { useRef, useEffect } from 'react';
import { Post } from './post';

export function MobileFeed({ posts }) {
  const containerRef = useRef(null);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const video = entry.target.querySelector('video');
          if (video) {
            video.play();
          }
        } else {
          const video = entry.target.querySelector('video');
          if (video) {
            video.pause();
          }
        }
      });
    }, options);

    const container = containerRef.current;
    if (container) {
      const postElements = container.querySelectorAll('.post');
      postElements.forEach((post) => observer.observe(post));
    }

    return () => {
      if (container) {
        const postElements = container.querySelectorAll('.post');
        postElements.forEach((post) => observer.unobserve(post));
      }
    };
  }, [posts]);

  return (
    <div ref={containerRef} className="h-screen w-screen overflow-y-scroll snap-y snap-mandatory">
      {posts.map((post) => (
        <div key={post.id} className="post h-screen w-screen snap-start flex items-center justify-center">
          <Post post={post} />
        </div>
      ))}
    </div>
  );
}
