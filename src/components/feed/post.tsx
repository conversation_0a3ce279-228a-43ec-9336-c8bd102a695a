import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Heart, MessageCircle, Share2 } from 'lucide-react';

export function Post({ post }) {
  return (
    <div className="relative h-full w-full">
      <video src={post.mediaUrls[0]} className="h-full w-full object-cover" loop />
      <div className="absolute bottom-0 left-0 w-full p-4 bg-gradient-to-t from-black to-transparent text-white">
        <div className="flex items-center mb-2">
          <Avatar>
            <AvatarImage src={post.user.avatar} />
            <AvatarFallback>{post.user.username[0]}</AvatarFallback>
          </Avatar>
          <div className="ml-2">
            <p className="font-bold">{post.user.username}</p>
            <p className="text-sm">{post.title}</p>
          </div>
        </div>
        <p className="text-sm">{post.content}</p>
        <div className="flex items-center justify-between mt-4">
          <Button variant="ghost" size="icon">
            <Heart />
          </Button>
          <Button variant="ghost" size="icon">
            <MessageCircle />
          </Button>
          <Button variant="ghost" size="icon">
            <Share2 />
          </Button>
        </div>
      </div>
    </div>
  );
}
