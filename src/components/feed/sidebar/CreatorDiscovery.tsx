"use client"

import React, { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Plus, 
  Crown, 
  Zap, 
  Users, 
  Play,
  Music,
  Video,
  Mic,
  RefreshCw,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Creator {
  id: string
  name: string
  stageName: string
  avatar?: string
  isVerified: boolean
  genre: string[]
  followers: number
  isLive?: boolean
  contentType: 'music' | 'video' | 'podcast' | 'mixed'
  recentViews: number
}

const mockCreators: Creator[] = [
  {
    id: '1',
    name: '<PERSON> Rivers',
    stageName: 'alexrivers',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
    isVerified: true,
    genre: ['Electronic', 'Ambient'],
    followers: 15420,
    contentType: 'music',
    recentViews: 89500,
  },
  {
    id: '2',
    name: 'Maya Chen',
    stageName: 'mayabeats',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    isVerified: true,
    genre: ['Hip-Hop', 'R&B'],
    followers: 23100,
    isLive: true,
    contentType: 'mixed',
    recentViews: 156000,
  },
  {
    id: '3',
    name: 'Sam Rodriguez',
    stageName: 'samtalks',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    isVerified: false,
    genre: ['Podcast', 'Talk'],
    followers: 8900,
    contentType: 'podcast',
    recentViews: 34200,
  },
  {
    id: '4',
    name: 'Luna Park',
    stageName: 'lunavisuals',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    isVerified: true,
    genre: ['Visual Art', 'Music Video'],
    followers: 19800,
    contentType: 'video',
    recentViews: 78900,
  },
]

const contentTypeIcons = {
  music: Music,
  video: Video,
  podcast: Mic,
  mixed: Play,
}

const contentTypeColors = {
  music: 'text-purple-500',
  video: 'text-red-500',
  podcast: 'text-green-500',
  mixed: 'text-blue-500',
}

export function CreatorDiscovery() {
  const [creators, setCreators] = useState<Creator[]>([])
  const [loading, setLoading] = useState(true)
  const [followingStates, setFollowingStates] = useState<Record<string, boolean>>({})

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setCreators(mockCreators)
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const handleFollow = (creatorId: string) => {
    setFollowingStates(prev => ({
      ...prev,
      [creatorId]: !prev[creatorId]
    }))
  }

  const refreshCreators = () => {
    setLoading(true)
    // Simulate refresh
    setTimeout(() => {
      setCreators([...mockCreators].sort(() => Math.random() - 0.5))
      setLoading(false)
    }, 800)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-2 w-16" />
            </div>
            <Skeleton className="h-6 w-12" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-xs text-sidebar-foreground/70">
          Suggested for you
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={refreshCreators}
          className="h-6 w-6 p-0"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      </div>

      <ScrollArea className="h-64">
        <div className="space-y-2 pr-3">
          {creators.map((creator) => {
            const isFollowing = followingStates[creator.id]
            const ContentIcon = contentTypeIcons[creator.contentType]
            const contentColor = contentTypeColors[creator.contentType]

            return (
              <div
                key={creator.id}
                className="group relative p-2 rounded-lg hover:bg-sidebar-accent transition-colors"
              >
                <div className="flex items-start gap-3">
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={creator.avatar} alt={creator.name} />
                      <AvatarFallback className="text-xs">
                        {creator.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    {creator.isLive && (
                      <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 border-2 border-sidebar animate-pulse" />
                    )}
                    
                    <div className={cn(
                      "absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-sidebar flex items-center justify-center",
                      "bg-sidebar"
                    )}>
                      <ContentIcon className={cn("h-2 w-2", contentColor)} />
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-medium truncate">
                        {creator.name}
                      </span>
                      {creator.isVerified && (
                        <Zap className="h-3 w-3 text-blue-500 flex-shrink-0" />
                      )}
                    </div>
                    
                    <div className="text-xs text-sidebar-foreground/50 truncate">
                      @{creator.stageName}
                    </div>
                    
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center gap-1">
                        <Users className="h-2 w-2 text-sidebar-foreground/40" />
                        <span className="text-xs text-sidebar-foreground/50">
                          {formatNumber(creator.followers)}
                        </span>
                      </div>
                      
                      {creator.isLive && (
                        <Badge variant="destructive" className="text-xs px-1 py-0 h-4">
                          LIVE
                        </Badge>
                      )}
                    </div>

                    <div className="flex flex-wrap gap-1 mt-1">
                      {creator.genre.slice(0, 2).map((genre) => (
                        <Badge
                          key={genre}
                          variant="outline"
                          className="text-xs px-1 py-0 h-4"
                        >
                          {genre}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Button
                    variant={isFollowing ? "secondary" : "default"}
                    size="sm"
                    onClick={() => handleFollow(creator.id)}
                    className="h-6 px-2 text-xs"
                  >
                    {isFollowing ? (
                      "Following"
                    ) : (
                      <>
                        <Plus className="h-2 w-2 mr-1" />
                        Follow
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}