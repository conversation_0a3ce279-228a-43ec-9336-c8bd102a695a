"use client"

import React from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FeedType } from '@/types/feed'
import { 
  Home,
  Compass,
  TrendingUp,
  Users,
  Radio,
  Heart,
  Bookmark,
  Clock,
  Zap,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface FeedNavigationProps {
  currentFeed: FeedType
  onFeedChange: (feedType: FeedType) => void
  className?: string
}

const navigationItems = [
  {
    type: FeedType.FOLLOWING,
    label: 'Following',
    icon: Home,
    description: 'Content from creators you follow',
    badge: null,
  },
  {
    type: FeedType.DISCOVER,
    label: 'Discover',
    icon: Compass,
    description: 'Explore new content and creators',
    badge: 'New',
  },
  {
    type: FeedType.TRENDING,
    label: 'Trending',
    icon: TrendingUp,
    description: "What's hot right now",
    badge: 'Hot',
  },
  {
    type: FeedType.MOOD,
    label: 'Mood',
    icon: Zap,
    description: 'Content based on your current mood',
    badge: null,
  },
  {
    type: FeedType.CREATOR,
    label: 'Creators',
    icon: Users,
    description: 'Browse by your favorite creators',
    badge: null,
  },
  {
    type: FeedType.PERSONA,
    label: 'Personas',
    icon: Radio,
    description: 'Explore different creator personas',
    badge: null,
  },
]

const secondaryItems = [
  {
    type: 'liked' as FeedType,
    label: 'Liked',
    icon: Heart,
    description: 'Content you\'ve liked',
    count: 127,
  },
  {
    type: 'saved' as FeedType,
    label: 'Saved',
    icon: Bookmark,
    description: 'Your saved memories',
    count: 43,
  },
  {
    type: 'recent' as FeedType,
    label: 'Recent',
    icon: Clock,
    description: 'Recently viewed content',
    count: 89,
  },
]

export function FeedNavigation({ currentFeed, onFeedChange, className }: FeedNavigationProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Primary Navigation */}
      <div className="space-y-1">
        <div className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider px-2 mb-2">
          Feeds
        </div>
        {navigationItems.map((item) => (
          <Button
            key={item.type}
            variant={currentFeed === item.type ? "secondary" : "ghost"}
            className={cn(
              "w-full justify-start gap-3 px-2 py-2 h-auto",
              currentFeed === item.type && "bg-primary/10 text-primary border border-primary/20"
            )}
            onClick={() => onFeedChange(item.type)}
          >
            <item.icon className="h-4 w-4" />
            <span className="flex-1 text-left font-medium">{item.label}</span>
            {item.badge && (
              <Badge 
                variant={item.badge === 'Hot' ? 'destructive' : 'secondary'} 
                className="text-xs px-1 py-0 h-4"
              >
                {item.badge}
              </Badge>
            )}
            {currentFeed === item.type && (
              <div className="absolute left-0 top-1/2 h-4 w-1 -translate-y-1/2 rounded-r-full bg-primary" />
            )}
          </Button>
        ))}
      </div>

      {/* Secondary Navigation */}
      <div className="space-y-1">
        <div className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider px-2 mb-2">
          Your Content
        </div>
        {secondaryItems.map((item) => (
          <Button
            key={item.type}
            variant="ghost"
            className="w-full justify-start gap-3 px-2 py-2 h-auto"
            onClick={() => onFeedChange(item.type)}
          >
            <item.icon className="h-4 w-4" />
            <span className="flex-1 text-left">{item.label}</span>
            <span className="text-xs text-sidebar-foreground/50">
              {item.count}
            </span>
          </Button>
        ))}
      </div>
    </div>
  )
}