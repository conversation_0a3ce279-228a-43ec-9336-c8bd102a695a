"use client"

import React from 'react'
import { useSession } from 'next-auth/react'
import { 
  <PERSON><PERSON>,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar'
import { FeedNavigation } from './FeedNavigation'
import { MoodSelector } from './MoodSelector'
import { CreatorDiscovery } from './CreatorDiscovery'
import { TrendingTopics } from './TrendingTopics'
import { UserProfile } from './UserProfile'
import { QuickActions } from './QuickActions'
import { LiveStreams } from './LiveStreams'
import { RecentMemories } from './RecentMemories'
import { FeedType } from '@/types/feed'
import { 
  Home,
  Compass,
  TrendingUp,
  Users,
  Heart,
  Bookmark,
  Radio,
  Settings,
  HelpCircle,
  Zap,
  Music,
  Video,
  Image,
  Mic,
  Sparkles,
} from 'lucide-react'

interface FeedSidebarProps {
  currentFeed: FeedType
  onFeedChange: (feedType: FeedType) => void
  selectedMoods: string[]
  onMoodChange: (moods: string[]) => void
  className?: string
}

export function FeedSidebar({
  currentFeed,
  onFeedChange,
  selectedMoods,
  onMoodChange,
  className,
}: FeedSidebarProps) {
  const { data: session } = useSession()

  const mainNavItems = [
    {
      title: "Home",
      icon: Home,
      feedType: FeedType.FOLLOWING,
      description: "Content from creators you follow",
    },
    {
      title: "Discover",
      icon: Compass,
      feedType: FeedType.DISCOVER,
      description: "Explore new content and creators",
    },
    {
      title: "Trending",
      icon: TrendingUp,
      feedType: FeedType.TRENDING,
      description: "What's hot right now",
    },
    {
      title: "Live",
      icon: Radio,
      feedType: FeedType.LIVE,
      description: "Live streams and performances",
    },
  ]

  const contentTypeItems = [
    {
      title: "Music",
      icon: Music,
      count: "2.4k",
      color: "text-purple-500",
    },
    {
      title: "Videos",
      icon: Video,
      count: "1.8k",
      color: "text-red-500",
    },
    {
      title: "Images",
      icon: Image,
      count: "956",
      color: "text-blue-500",
    },
    {
      title: "Podcasts",
      icon: Mic,
      count: "342",
      color: "text-green-500",
    },
  ]

  const userItems = [
    {
      title: "Liked Content",
      icon: Heart,
      count: session ? "127" : "0",
    },
    {
      title: "Saved Memories",
      icon: Bookmark,
      count: session ? "43" : "0",
    },
    {
      title: "Following",
      icon: Users,
      count: session ? "89" : "0",
    },
  ]

  return (
    <Sidebar className={className} variant="floating">
      <SidebarHeader className="border-b border-sidebar-border">
        <UserProfile />
        <QuickActions />
      </SidebarHeader>

      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainNavItems.map((item) => (
                <SidebarMenuItem key={item.feedType}>
                  <SidebarMenuButton
                    onClick={() => onFeedChange(item.feedType)}
                    isActive={currentFeed === item.feedType}
                    className="group relative"
                    tooltip={item.description}
                  >
                    <item.icon className="h-4 w-4" />
                    <span className="font-medium">{item.title}</span>
                    {currentFeed === item.feedType && (
                      <div className="absolute left-0 top-1/2 h-4 w-1 -translate-y-1/2 rounded-r-full bg-primary" />
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Mood Selector */}
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2 text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            <Sparkles className="h-3 w-3" />
            Mood Filter
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <MoodSelector
              selectedMoods={selectedMoods}
              onMoodChange={onMoodChange}
            />
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Content Types */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            Content Types
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {contentTypeItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton className="group">
                    <item.icon className={`h-4 w-4 ${item.color}`} />
                    <span className="flex-1">{item.title}</span>
                    <span className="text-xs text-sidebar-foreground/50">
                      {item.count}
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Live Streams */}
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2 text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            <div className="h-2 w-2 rounded-full bg-red-500 animate-pulse" />
            Live Now
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <LiveStreams />
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Trending Topics */}
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2 text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            <Zap className="h-3 w-3" />
            Trending
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <TrendingTopics />
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* Creator Discovery */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
            Discover Creators
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <CreatorDiscovery />
          </SidebarGroupContent>
        </SidebarGroup>

        {session && (
          <>
            <SidebarSeparator />

            {/* User Content */}
            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
                Your Content
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {userItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton>
                        <item.icon className="h-4 w-4" />
                        <span className="flex-1">{item.title}</span>
                        <span className="text-xs text-sidebar-foreground/50">
                          {item.count}
                        </span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarSeparator />

            {/* Recent Memories */}
            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
                Recent Memories
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <RecentMemories />
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <HelpCircle className="h-4 w-4" />
              <span>Help & Support</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}