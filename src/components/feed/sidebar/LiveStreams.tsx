"use client"

import React, { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Play, 
  Users, 
  Eye,
  Music,
  Mic,
  Video,
  Radio,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface LiveStream {
  id: string
  creatorName: string
  creatorAvatar?: string
  title: string
  viewers: number
  category: 'music' | 'podcast' | 'video' | 'talk'
  duration: number // minutes
  isVerified: boolean
}

const mockStreams: LiveStream[] = [
  {
    id: '1',
    creatorName: '<PERSON>',
    creatorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    title: 'Late Night Beats Session',
    viewers: 1247,
    category: 'music',
    duration: 45,
    isVerified: true,
  },
  {
    id: '2',
    creatorName: '<PERSON>',
    creatorAvatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
    title: 'Producer Q&A + Live Mixing',
    viewers: 892,
    category: 'music',
    duration: 23,
    isVerified: true,
  },
  {
    id: '3',
    creatorName: 'Sam Rodriguez',
    creatorAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    title: 'Morning Coffee Chat',
    viewers: 456,
    category: 'talk',
    duration: 67,
    isVerified: false,
  },
  {
    id: '4',
    creatorName: 'Luna Park',
    creatorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    title: 'Behind the Music Video',
    viewers: 234,
    category: 'video',
    duration: 12,
    isVerified: true,
  },
]

const categoryIcons = {
  music: Music,
  podcast: Mic,
  video: Video,
  talk: Radio,
}

const categoryColors = {
  music: 'text-purple-500',
  podcast: 'text-green-500',
  video: 'text-red-500',
  talk: 'text-blue-500',
}

export function LiveStreams() {
  const [streams, setStreams] = useState<LiveStream[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setStreams(mockStreams)
      setLoading(false)
    }, 600)

    return () => clearTimeout(timer)
  }, [])

  const formatViewers = (viewers: number) => {
    if (viewers >= 1000) {
      return `${(viewers / 1000).toFixed(1)}K`
    }
    return viewers.toString()
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center gap-2 p-2">
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-2 w-16" />
            </div>
            <Skeleton className="h-4 w-8" />
          </div>
        ))}
      </div>
    )
  }

  if (streams.length === 0) {
    return (
      <div className="text-center py-4 text-xs text-sidebar-foreground/50">
        No live streams right now
      </div>
    )
  }

  return (
    <ScrollArea className="h-40">
      <div className="space-y-1 pr-3">
        {streams.map((stream) => {
          const CategoryIcon = categoryIcons[stream.category]
          const categoryColor = categoryColors[stream.category]

          return (
            <Button
              key={stream.id}
              variant="ghost"
              className="w-full justify-start p-2 h-auto hover:bg-sidebar-accent group"
            >
              <div className="flex items-center gap-2 w-full">
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={stream.creatorAvatar} alt={stream.creatorName} />
                    <AvatarFallback className="text-xs">
                      {stream.creatorName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Live indicator */}
                  <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 border-2 border-sidebar animate-pulse" />
                  
                  {/* Category icon */}
                  <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-sidebar border-2 border-sidebar flex items-center justify-center">
                    <CategoryIcon className={cn("h-2 w-2", categoryColor)} />
                  </div>
                </div>

                <div className="flex-1 min-w-0 text-left">
                  <div className="text-xs font-medium truncate">
                    {stream.title}
                  </div>
                  <div className="text-xs text-sidebar-foreground/50 truncate">
                    {stream.creatorName}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-sidebar-foreground/40">
                    <div className="flex items-center gap-1">
                      <Eye className="h-2 w-2" />
                      <span>{formatViewers(stream.viewers)}</span>
                    </div>
                    <span>•</span>
                    <span>{formatDuration(stream.duration)}</span>
                  </div>
                </div>

                <div className="flex flex-col items-end gap-1">
                  <Badge variant="destructive" className="text-xs px-1 py-0 h-4">
                    LIVE
                  </Badge>
                  <Play className="h-3 w-3 text-sidebar-foreground/40 group-hover:text-primary transition-colors" />
                </div>
              </div>
            </Button>
          )
        })}
      </div>
    </ScrollArea>
  )
}