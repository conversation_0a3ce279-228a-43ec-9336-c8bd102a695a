"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { 
  ChevronDown, 
  X, 
  Sparkles,
  Heart,
  Zap,
  Sun,
  Moon,
  Cloud,
  Flame,
  Snowflake,
  Music,
  Coffee,
  Headphones,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MoodSelectorProps {
  selectedMoods: string[]
  onMoodChange: (moods: string[]) => void
  className?: string
}

const moodCategories = [
  {
    name: 'Energy',
    icon: Zap,
    moods: [
      { name: 'energetic', icon: Zap, color: 'bg-yellow-500', count: 1240 },
      { name: 'uplifting', icon: Sun, color: 'bg-orange-500', count: 890 },
      { name: 'intense', icon: Flame, color: 'bg-red-500', count: 560 },
      { name: 'chill', icon: Snowflake, color: 'bg-blue-500', count: 2100 },
    ]
  },
  {
    name: 'Emotions',
    icon: Heart,
    moods: [
      { name: 'happy', icon: Sun, color: 'bg-yellow-400', count: 1250 },
      { name: 'romantic', icon: Heart, color: 'bg-pink-500', count: 650 },
      { name: 'melancholic', icon: Cloud, color: 'bg-gray-500', count: 420 },
      { name: 'nostalgic', icon: Moon, color: 'bg-purple-500', count: 380 },
    ]
  },
  {
    name: 'Vibes',
    icon: Music,
    moods: [
      { name: 'dreamy', icon: Cloud, color: 'bg-indigo-500', count: 340 },
      { name: 'groovy', icon: Music, color: 'bg-green-500', count: 720 },
      { name: 'ambient', icon: Headphones, color: 'bg-teal-500', count: 450 },
      { name: 'cozy', icon: Coffee, color: 'bg-amber-600', count: 680 },
    ]
  }
]

export function MoodSelector({ selectedMoods, onMoodChange, className }: MoodSelectorProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['Energy'])

  const toggleMood = (mood: string) => {
    if (selectedMoods.includes(mood)) {
      onMoodChange(selectedMoods.filter(m => m !== mood))
    } else {
      onMoodChange([...selectedMoods, mood])
    }
  }

  const clearAllMoods = () => {
    onMoodChange([])
  }

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryName)
        ? prev.filter(c => c !== categoryName)
        : [...prev, categoryName]
    )
  }

  const totalSelectedCount = selectedMoods.length

  return (
    <div className={cn("space-y-2", className)}>
      {/* Selected Moods Summary */}
      {totalSelectedCount > 0 && (
        <div className="flex items-center justify-between p-2 bg-sidebar-accent rounded-md">
          <div className="flex items-center gap-2">
            <Sparkles className="h-3 w-3 text-primary" />
            <span className="text-xs font-medium">
              {totalSelectedCount} mood{totalSelectedCount !== 1 ? 's' : ''} selected
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllMoods}
            className="h-6 w-6 p-0 hover:bg-sidebar-accent-foreground/10"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Selected Mood Tags */}
      {selectedMoods.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedMoods.map((mood) => {
            const moodData = moodCategories
              .flatMap(cat => cat.moods)
              .find(m => m.name === mood)
            
            return (
              <Badge
                key={mood}
                variant="secondary"
                className="text-xs px-2 py-1 cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-colors"
                onClick={() => toggleMood(mood)}
              >
                {mood}
                <X className="ml-1 h-2 w-2" />
              </Badge>
            )
          })}
        </div>
      )}

      {/* Mood Categories */}
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-2 h-auto text-xs"
          >
            <span className="flex items-center gap-2">
              <Sparkles className="h-3 w-3" />
              Browse Moods
            </span>
            <ChevronDown className={cn(
              "h-3 w-3 transition-transform",
              isExpanded && "rotate-180"
            )} />
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="space-y-2">
          <ScrollArea className="h-64">
            <div className="space-y-3 pr-3">
              {moodCategories.map((category) => (
                <div key={category.name} className="space-y-1">
                  <Collapsible
                    open={expandedCategories.includes(category.name)}
                    onOpenChange={() => toggleCategory(category.name)}
                  >
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        className="w-full justify-between p-1 h-auto text-xs font-medium"
                      >
                        <span className="flex items-center gap-2">
                          <category.icon className="h-3 w-3" />
                          {category.name}
                        </span>
                        <ChevronDown className={cn(
                          "h-3 w-3 transition-transform",
                          expandedCategories.includes(category.name) && "rotate-180"
                        )} />
                      </Button>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="space-y-1 ml-2">
                      {category.moods.map((mood) => {
                        const isSelected = selectedMoods.includes(mood.name)
                        
                        return (
                          <Button
                            key={mood.name}
                            variant={isSelected ? "secondary" : "ghost"}
                            className={cn(
                              "w-full justify-between p-2 h-auto text-xs",
                              isSelected && "bg-primary/10 text-primary border border-primary/20"
                            )}
                            onClick={() => toggleMood(mood.name)}
                          >
                            <span className="flex items-center gap-2">
                              <div className={cn(
                                "h-2 w-2 rounded-full",
                                mood.color
                              )} />
                              <span className="capitalize">{mood.name}</span>
                            </span>
                            <span className="text-xs text-sidebar-foreground/50">
                              {mood.count}
                            </span>
                          </Button>
                        )
                      })}
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}