"use client"

import React from 'react'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  Upload, 
  Radio, 
  Mic,
  Video,
  Image,
  Music,
  Zap,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export function QuickActions() {
  const { data: session } = useSession()
  const router = useRouter()

  const isCreator = session?.user?.role === 'CREATOR'

  const handleAction = (action: string) => {
    switch (action) {
      case 'upload':
        router.push('/upload')
        break
      case 'live':
        router.push('/live/create')
        break
      case 'music':
        router.push('/upload?type=music')
        break
      case 'video':
        router.push('/upload?type=video')
        break
      case 'image':
        router.push('/upload?type=image')
        break
      case 'podcast':
        router.push('/upload?type=podcast')
        break
      default:
        break
    }
  }

  if (!session) {
    return null
  }

  return (
    <div className="flex gap-2">
      {/* Quick Upload */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="default" 
            size="sm" 
            className="flex-1 gap-2"
          >
            <Plus className="h-3 w-3" />
            Create
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          <DropdownMenuLabel>Create Content</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => handleAction('music')}>
            <Music className="mr-2 h-4 w-4 text-purple-500" />
            <span>Upload Music</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => handleAction('video')}>
            <Video className="mr-2 h-4 w-4 text-red-500" />
            <span>Upload Video</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => handleAction('image')}>
            <Image className="mr-2 h-4 w-4 text-blue-500" />
            <span>Upload Image</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => handleAction('podcast')}>
            <Mic className="mr-2 h-4 w-4 text-green-500" />
            <span>Record Podcast</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => handleAction('upload')}>
            <Upload className="mr-2 h-4 w-4" />
            <span>General Upload</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Go Live */}
      {isCreator && (
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => handleAction('live')}
          className="gap-2 border-red-500/20 text-red-500 hover:bg-red-500/10"
        >
          <Radio className="h-3 w-3" />
          Live
        </Button>
      )}

      {/* AI Assistant */}
      <Button 
        variant="outline" 
        size="sm"
        className="gap-2 border-purple-500/20 text-purple-500 hover:bg-purple-500/10"
      >
        <Zap className="h-3 w-3" />
        AI
      </Button>
    </div>
  )
}