"use client"

import React, { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Bookmark, 
  Play, 
  Music,
  Video,
  Image,
  Clock,
  Heart,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Memory {
  id: string
  postId: string
  title: string
  creatorName: string
  creatorAvatar?: string
  contentType: 'music' | 'video' | 'image'
  thumbnail?: string
  savedAt: Date
  duration?: number // for video/audio in seconds
  timestamp?: number // saved timestamp for video/audio
}

const mockMemories: Memory[] = [
  {
    id: '1',
    postId: 'post1',
    title: 'Midnight Synthwave',
    creatorName: 'Alex Rivers',
    creatorAvatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
    contentType: 'music',
    thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=100&h=100&fit=crop',
    savedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    duration: 245,
    timestamp: 120,
  },
  {
    id: '2',
    postId: 'post2',
    title: 'Studio Session Vibes',
    creatorName: 'Maya Chen',
    creatorAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    contentType: 'video',
    thumbnail: 'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=100&h=100&fit=crop',
    savedAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    duration: 180,
    timestamp: 45,
  },
  {
    id: '3',
    postId: 'post3',
    title: 'Album Cover Art',
    creatorName: 'Luna Park',
    creatorAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    contentType: 'image',
    thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',
    savedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
  },
]

const contentTypeIcons = {
  music: Music,
  video: Video,
  image: Image,
}

const contentTypeColors = {
  music: 'text-purple-500',
  video: 'text-red-500',
  image: 'text-blue-500',
}

export function RecentMemories() {
  const [memories, setMemories] = useState<Memory[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setMemories(mockMemories)
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center gap-2 p-2">
            <Skeleton className="h-10 w-10 rounded" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-2 w-16" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (memories.length === 0) {
    return (
      <div className="text-center py-4 space-y-2">
        <Bookmark className="h-6 w-6 mx-auto text-sidebar-foreground/30" />
        <div className="text-xs text-sidebar-foreground/50">
          No saved memories yet
        </div>
        <div className="text-xs text-sidebar-foreground/30">
          Save moments you love to revisit them later
        </div>
      </div>
    )
  }

  return (
    <ScrollArea className="h-40">
      <div className="space-y-1 pr-3">
        {memories.map((memory) => {
          const ContentIcon = contentTypeIcons[memory.contentType]
          const contentColor = contentTypeColors[memory.contentType]

          return (
            <Button
              key={memory.id}
              variant="ghost"
              className="w-full justify-start p-2 h-auto hover:bg-sidebar-accent group"
            >
              <div className="flex items-center gap-2 w-full">
                <div className="relative">
                  {memory.thumbnail ? (
                    <div className="h-10 w-10 rounded overflow-hidden bg-sidebar-accent">
                      <img
                        src={memory.thumbnail}
                        alt={memory.title}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="h-10 w-10 rounded bg-sidebar-accent flex items-center justify-center">
                      <ContentIcon className={cn("h-4 w-4", contentColor)} />
                    </div>
                  )}
                  
                  {/* Content type indicator */}
                  <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-sidebar border-2 border-sidebar flex items-center justify-center">
                    <ContentIcon className={cn("h-2 w-2", contentColor)} />
                  </div>
                </div>

                <div className="flex-1 min-w-0 text-left">
                  <div className="text-xs font-medium truncate">
                    {memory.title}
                  </div>
                  <div className="text-xs text-sidebar-foreground/50 truncate">
                    {memory.creatorName}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-sidebar-foreground/40">
                    <div className="flex items-center gap-1">
                      <Clock className="h-2 w-2" />
                      <span>{formatTimeAgo(memory.savedAt)}</span>
                    </div>
                    {memory.duration && (
                      <>
                        <span>•</span>
                        <span>{formatDuration(memory.duration)}</span>
                      </>
                    )}
                  </div>
                  {memory.timestamp && (
                    <div className="text-xs text-sidebar-foreground/30">
                      Saved at {formatDuration(memory.timestamp)}
                    </div>
                  )}
                </div>

                <div className="flex flex-col items-center gap-1">
                  <Heart className="h-3 w-3 text-pink-500 fill-current" />
                  <Play className="h-3 w-3 text-sidebar-foreground/40 group-hover:text-primary transition-colors" />
                </div>
              </div>
            </Button>
          )
        })}
      </div>
    </ScrollArea>
  )
}