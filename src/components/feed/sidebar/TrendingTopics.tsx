"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  TrendingUp, 
  Hash, 
  Music, 
  Video, 
  Mic,
  Flame,
  ArrowUp,
  ArrowDown,
  Minus,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrendingTopic {
  id: string
  name: string
  category: 'hashtag' | 'genre' | 'creator' | 'mood'
  posts: number
  growth: number // percentage change
  isHot: boolean
  contentType?: 'music' | 'video' | 'podcast' | 'mixed'
}

const mockTrends: TrendingTopic[] = [
  {
    id: '1',
    name: '#MidnightVibes',
    category: 'hashtag',
    posts: 2340,
    growth: 45.2,
    isHot: true,
    contentType: 'music',
  },
  {
    id: '2',
    name: 'Lo-Fi Hip Hop',
    category: 'genre',
    posts: 1890,
    growth: 23.1,
    isHot: false,
    contentType: 'music',
  },
  {
    id: '3',
    name: '#CreatorSpotlight',
    category: 'hashtag',
    posts: 1560,
    growth: 67.8,
    isHot: true,
    contentType: 'mixed',
  },
  {
    id: '4',
    name: 'Ambient Soundscapes',
    category: 'mood',
    posts: 1234,
    growth: 12.4,
    isHot: false,
    contentType: 'music',
  },
  {
    id: '5',
    name: '#BehindTheScenes',
    category: 'hashtag',
    posts: 987,
    growth: -5.2,
    isHot: false,
    contentType: 'video',
  },
  {
    id: '6',
    name: 'Indie Folk',
    category: 'genre',
    posts: 876,
    growth: 34.7,
    isHot: true,
    contentType: 'music',
  },
  {
    id: '7',
    name: '#MorningMotivation',
    category: 'hashtag',
    posts: 654,
    growth: 18.9,
    isHot: false,
    contentType: 'podcast',
  },
]

const categoryIcons = {
  hashtag: Hash,
  genre: Music,
  creator: Video,
  mood: Mic,
}

const contentTypeColors = {
  music: 'text-purple-500',
  video: 'text-red-500',
  podcast: 'text-green-500',
  mixed: 'text-blue-500',
}

export function TrendingTopics() {
  const [trends, setTrends] = useState<TrendingTopic[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setTrends(mockTrends)
      setLoading(false)
    }, 800)

    return () => clearTimeout(timer)
  }, [])

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return ArrowUp
    if (growth < 0) return ArrowDown
    return Minus
  }

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-500'
    if (growth < 0) return 'text-red-500'
    return 'text-gray-500'
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center gap-2 p-2">
            <Skeleton className="h-4 w-4 rounded" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-2 w-16" />
            </div>
            <Skeleton className="h-4 w-8" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <ScrollArea className="h-48">
      <div className="space-y-1 pr-3">
        {trends.map((trend, index) => {
          const CategoryIcon = categoryIcons[trend.category]
          const GrowthIcon = getGrowthIcon(trend.growth)
          const growthColor = getGrowthColor(trend.growth)
          const contentColor = trend.contentType ? contentTypeColors[trend.contentType] : 'text-gray-500'

          return (
            <Button
              key={trend.id}
              variant="ghost"
              className="w-full justify-start p-2 h-auto hover:bg-sidebar-accent group"
            >
              <div className="flex items-center gap-2 w-full">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-mono text-sidebar-foreground/40 w-4">
                    {index + 1}
                  </span>
                  <CategoryIcon className={cn("h-3 w-3", contentColor)} />
                </div>

                <div className="flex-1 min-w-0 text-left">
                  <div className="flex items-center gap-1">
                    <span className="text-xs font-medium truncate">
                      {trend.name}
                    </span>
                    {trend.isHot && (
                      <Flame className="h-2 w-2 text-orange-500 flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-sidebar-foreground/50">
                    <span>{formatNumber(trend.posts)} posts</span>
                    <div className={cn("flex items-center gap-0.5", growthColor)}>
                      <GrowthIcon className="h-2 w-2" />
                      <span>{Math.abs(trend.growth).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                {trend.isHot && (
                  <Badge variant="destructive" className="text-xs px-1 py-0 h-4">
                    HOT
                  </Badge>
                )}
              </div>
            </Button>
          )
        })}
      </div>
    </ScrollArea>
  )
}