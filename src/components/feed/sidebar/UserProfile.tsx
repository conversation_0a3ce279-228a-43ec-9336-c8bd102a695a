"use client"

import React from 'react'
import { useSession, signIn, signOut } from 'next-auth/react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  Settings, 
  LogOut, 
  Crown, 
  Zap,
  ChevronDown,
  Plus,
  Palette,
} from 'lucide-react'

export function UserProfile() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="flex items-center gap-3 p-2">
        <div className="h-8 w-8 rounded-full bg-sidebar-accent animate-pulse" />
        <div className="flex-1 space-y-1">
          <div className="h-3 w-20 bg-sidebar-accent rounded animate-pulse" />
          <div className="h-2 w-16 bg-sidebar-accent rounded animate-pulse" />
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="space-y-2 p-2">
        <div className="text-center space-y-2">
          <div className="text-sm text-sidebar-foreground/70">
            Join HVPPY Central
          </div>
          <Button 
            onClick={() => signIn()} 
            className="w-full"
            size="sm"
          >
            Sign In
          </Button>
        </div>
      </div>
    )
  }

  const userInitials = session.user?.name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || 'U'

  const isCreator = session.user?.role === 'CREATOR'
  const isVerified = session.user?.isVerified

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-start gap-3 p-2 h-auto hover:bg-sidebar-accent"
        >
          <div className="relative">
            <Avatar className="h-8 w-8">
              <AvatarImage 
                src={session.user?.image || undefined} 
                alt={session.user?.name || 'User'} 
              />
              <AvatarFallback className="text-xs font-medium">
                {userInitials}
              </AvatarFallback>
            </Avatar>
            {isCreator && (
              <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-purple-500 border-2 border-sidebar flex items-center justify-center">
                <Crown className="h-1.5 w-1.5 text-white" />
              </div>
            )}
          </div>
          
          <div className="flex-1 text-left min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium truncate">
                {session.user?.displayName || session.user?.name || 'User'}
              </span>
              {isVerified && (
                <Zap className="h-3 w-3 text-blue-500 flex-shrink-0" />
              )}
            </div>
            <div className="flex items-center gap-1">
              <span className="text-xs text-sidebar-foreground/50 truncate">
                @{session.user?.username || 'user'}
              </span>
              {isCreator && (
                <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                  Creator
                </Badge>
              )}
            </div>
          </div>
          
          <ChevronDown className="h-4 w-4 text-sidebar-foreground/50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        className="w-64" 
        align="start"
        side="right"
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {session.user?.displayName || session.user?.name}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {session.user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        
        {!isCreator && (
          <DropdownMenuItem>
            <Plus className="mr-2 h-4 w-4" />
            <span>Become a Creator</span>
          </DropdownMenuItem>
        )}
        
        <DropdownMenuItem>
          <Palette className="mr-2 h-4 w-4" />
          <span>Customize Feed</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={() => signOut()}
          className="text-red-600 focus:text-red-600"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}