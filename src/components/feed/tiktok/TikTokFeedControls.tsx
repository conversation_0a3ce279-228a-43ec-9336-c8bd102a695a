"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { 
  Heart, 
  MessageCircle, 
  Share, 
  Bookmark,
  MoreHorizontal,
  Sparkles,
  Flame,
  Zap,
  Music,
  Eye
} from 'lucide-react'

interface TikTokFeedControlsProps {
  item: FeedItem
  isLiked: boolean
  isSaved: boolean
  onLike: () => void
  onSave: () => void
  onShare: () => void
  onComment: () => void
  className?: string
}

export function TikTokFeedControls({
  item,
  isLiked,
  isSaved,
  onLike,
  onSave,
  onShare,
  onComment,
  className
}: TikTokFeedControlsProps) {
  const [isAnimating, setIsAnimating] = useState<string | null>(null)

  // Handle button animations
  const handleButtonClick = useCallback((action: string, callback: () => void) => {
    setIsAnimating(action)
    callback()
    
    // Add haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
    
    setTimeout(() => setIsAnimating(null), 300)
  }, [])

  // Format count numbers (1000 -> 1K, 1000000 -> 1M)
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  return (
    <div className={cn("flex flex-col items-center space-y-6", className)}>
      {/* Like Button */}
      <div className="flex flex-col items-center space-y-1">
        <Button
          onClick={() => handleButtonClick('like', onLike)}
          variant="ghost"
          size="icon"
          className={cn(
            "h-12 w-12 rounded-full transition-all duration-300 transform",
            "hover:scale-110 active:scale-95",
            isLiked 
              ? "bg-red-500/20 text-red-500 hover:bg-red-500/30" 
              : "bg-white/10 text-white hover:bg-white/20",
            isAnimating === 'like' && "animate-pulse scale-125"
          )}
          aria-label={isLiked ? "Unlike" : "Like"}
        >
          <Heart 
            className={cn(
              "h-6 w-6 transition-all duration-200",
              isLiked && "fill-current"
            )} 
          />
        </Button>
        <span className="text-white text-xs font-medium">
          {formatCount(item.post.likeCount)}
        </span>
      </div>

      {/* Comment Button */}
      <div className="flex flex-col items-center space-y-1">
        <Button
          onClick={() => handleButtonClick('comment', onComment)}
          variant="ghost"
          size="icon"
          className={cn(
            "h-12 w-12 rounded-full bg-white/10 text-white transition-all duration-300 transform",
            "hover:bg-white/20 hover:scale-110 active:scale-95",
            isAnimating === 'comment' && "animate-pulse scale-125"
          )}
          aria-label="Comment"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
        <span className="text-white text-xs font-medium">
          {formatCount(item.post._count?.reactions || 0)}
        </span>
      </div>

      {/* Save/Bookmark Button */}
      <div className="flex flex-col items-center space-y-1">
        <Button
          onClick={() => handleButtonClick('save', onSave)}
          variant="ghost"
          size="icon"
          className={cn(
            "h-12 w-12 rounded-full transition-all duration-300 transform",
            "hover:scale-110 active:scale-95",
            isSaved 
              ? "bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30" 
              : "bg-white/10 text-white hover:bg-white/20",
            isAnimating === 'save' && "animate-pulse scale-125"
          )}
          aria-label={isSaved ? "Remove from saved" : "Save"}
        >
          <Bookmark 
            className={cn(
              "h-6 w-6 transition-all duration-200",
              isSaved && "fill-current"
            )} 
          />
        </Button>
        <span className="text-white text-xs font-medium">
          {formatCount(item.post._count?.memories || 0)}
        </span>
      </div>

      {/* Share Button */}
      <div className="flex flex-col items-center space-y-1">
        <Button
          onClick={() => handleButtonClick('share', onShare)}
          variant="ghost"
          size="icon"
          className={cn(
            "h-12 w-12 rounded-full bg-white/10 text-white transition-all duration-300 transform",
            "hover:bg-white/20 hover:scale-110 active:scale-95",
            isAnimating === 'share' && "animate-pulse scale-125"
          )}
          aria-label="Share"
        >
          <Share className="h-6 w-6" />
        </Button>
        <span className="text-white text-xs font-medium">
          {formatCount(item.post.shareCount)}
        </span>
      </div>

      {/* Mood Reaction Buttons */}
      <div className="flex flex-col items-center space-y-2">
        {/* Fire Reaction */}
        <Button
          onClick={() => handleButtonClick('fire', () => {})}
          variant="ghost"
          size="icon"
          className={cn(
            "h-10 w-10 rounded-full bg-white/10 text-white transition-all duration-300 transform",
            "hover:bg-orange-500/20 hover:text-orange-500 hover:scale-110 active:scale-95",
            isAnimating === 'fire' && "animate-pulse scale-125"
          )}
          aria-label="Fire reaction"
        >
          <Flame className="h-5 w-5" />
        </Button>

        {/* Sparkles Reaction */}
        <Button
          onClick={() => handleButtonClick('sparkles', () => {})}
          variant="ghost"
          size="icon"
          className={cn(
            "h-10 w-10 rounded-full bg-white/10 text-white transition-all duration-300 transform",
            "hover:bg-purple-500/20 hover:text-purple-500 hover:scale-110 active:scale-95",
            isAnimating === 'sparkles' && "animate-pulse scale-125"
          )}
          aria-label="Sparkles reaction"
        >
          <Sparkles className="h-5 w-5" />
        </Button>

        {/* Energy Reaction */}
        <Button
          onClick={() => handleButtonClick('energy', () => {})}
          variant="ghost"
          size="icon"
          className={cn(
            "h-10 w-10 rounded-full bg-white/10 text-white transition-all duration-300 transform",
            "hover:bg-yellow-500/20 hover:text-yellow-500 hover:scale-110 active:scale-95",
            isAnimating === 'energy' && "animate-pulse scale-125"
          )}
          aria-label="Energy reaction"
        >
          <Zap className="h-5 w-5" />
        </Button>
      </div>

      {/* More Options */}
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          "h-10 w-10 rounded-full bg-white/10 text-white transition-all duration-300 transform",
          "hover:bg-white/20 hover:scale-110 active:scale-95"
        )}
        aria-label="More options"
      >
        <MoreHorizontal className="h-5 w-5" />
      </Button>

      {/* Creator Avatar (for following) */}
      <div className="mt-4">
        <div className="relative">
          <div className="h-12 w-12 rounded-full border-2 border-white overflow-hidden">
            <img
              src={item.post.creator?.avatarUrl || item.post.user.avatarUrl || '/default-avatar.png'}
              alt={item.post.creator?.name || item.post.user.displayName}
              className="h-full w-full object-cover"
            />
          </div>
          {/* Plus icon for follow */}
          <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">+</span>
          </div>
        </div>
      </div>

      {/* View Count Indicator */}
      <div className="flex flex-col items-center space-y-1 mt-2">
        <Eye className="h-4 w-4 text-white/60" />
        <span className="text-white/60 text-xs">
          {formatCount(item.post.viewCount)}
        </span>
      </div>
    </div>
  )
}
