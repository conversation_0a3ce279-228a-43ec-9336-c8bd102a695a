"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { FeedType } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Home, 
  Compass, 
  TrendingUp, 
  Heart, 
  User, 
  Sparkles,
  Search,
  Bell
} from 'lucide-react'

interface TikTokFeedNavigationProps {
  currentFeed: FeedType
  onFeedChange: (feedType: FeedType) => void
  unreadCounts?: Record<FeedType, number>
  className?: string
}

const feedConfig = {
  [FeedType.FOLLOWING]: {
    label: 'Following',
    icon: Home,
    description: 'Content from creators you follow'
  },
  [FeedType.DISCOVER]: {
    label: 'For You',
    icon: Compass,
    description: 'Personalized content discovery'
  },
  [FeedType.TRENDING]: {
    label: 'Trending',
    icon: TrendingUp,
    description: 'What\'s hot right now'
  },
  [FeedType.MOOD_BASED]: {
    label: 'Mood',
    icon: Heart,
    description: 'Content matching your vibe'
  },
  [FeedType.PERSONA]: {
    label: 'Personas',
    icon: User,
    description: 'Creator personas you follow'
  },
  [FeedType.EXPERIMENTAL]: {
    label: 'Experimental',
    icon: Sparkles,
    description: 'New and experimental content'
  }
}

export function TikTokFeedNavigation({
  currentFeed,
  onFeedChange,
  unreadCounts,
  className
}: TikTokFeedNavigationProps) {
  return (
    <div className={cn(
      "bg-gradient-to-b from-black/80 via-black/60 to-transparent",
      "backdrop-blur-sm border-b border-white/10",
      className
    )}>
      <div className="flex items-center justify-between p-4">
        {/* Left side - Main navigation */}
        <div className="flex items-center space-x-1">
          {Object.entries(feedConfig).map(([feedType, config]) => {
            const isActive = currentFeed === feedType
            const Icon = config.icon
            const unreadCount = unreadCounts?.[feedType as FeedType]
            
            return (
              <div key={feedType} className="relative">
                <Button
                  onClick={() => onFeedChange(feedType as FeedType)}
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "relative px-3 py-2 text-sm font-medium transition-all duration-200",
                    "hover:bg-white/10 hover:text-white",
                    isActive 
                      ? "text-white bg-white/20" 
                      : "text-white/70"
                  )}
                  aria-label={`Switch to ${config.label} feed`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {config.label}
                  
                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-hvppy-500 rounded-full" />
                  )}
                </Button>
                
                {/* Unread count badge */}
                {unreadCount && unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </div>
            )
          })}
        </div>

        {/* Right side - Search and notifications */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9 text-white/70 hover:text-white hover:bg-white/10"
            aria-label="Search"
          >
            <Search className="h-5 w-5" />
          </Button>
          
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 text-white/70 hover:text-white hover:bg-white/10"
              aria-label="Notifications"
            >
              <Bell className="h-5 w-5" />
            </Button>
            
            {/* Notification indicator */}
            <div className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full" />
          </div>
        </div>
      </div>

      {/* Current feed description */}
      <div className="px-4 pb-3">
        <p className="text-white/60 text-xs">
          {feedConfig[currentFeed]?.description}
        </p>
      </div>
    </div>
  )
}
