"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { MoodType } from '@/types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>rk<PERSON>, 
  <PERSON>, 
  <PERSON>,
  <PERSON>,
  Star
} from 'lucide-react'

interface TikTokMoodSelectorProps {
  selectedMoods: MoodType[]
  onMoodChange: (moods: MoodType[]) => void
  moodCounts?: Record<MoodType, number>
  maxSelections?: number
  showCounts?: boolean
  className?: string
}

const moodConfig: Record<MoodType, { 
  label: string
  icon: React.ComponentType<any>
  color: string
  bgColor: string
  description: string
}> = {
  happy: {
    label: 'Happy',
    icon: Smile,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20 hover:bg-yellow-500/30',
    description: 'Uplifting and joyful content'
  },
  chill: {
    label: 'Chill',
    icon: Wind,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20 hover:bg-blue-500/30',
    description: 'Relaxed and laid-back vibes'
  },
  heartbroken: {
    label: 'Heartbroken',
    icon: Heart,
    color: 'text-red-400',
    bgColor: 'bg-red-500/20 hover:bg-red-500/30',
    description: 'Emotional and touching content'
  },
  inspired: {
    label: 'Inspired',
    icon: Sparkles,
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20 hover:bg-purple-500/30',
    description: 'Motivational and creative content'
  },
  energetic: {
    label: 'Energetic',
    icon: Zap,
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/20 hover:bg-orange-500/30',
    description: 'High-energy and exciting content'
  },
  peaceful: {
    label: 'Peaceful',
    icon: Moon,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20 hover:bg-green-500/30',
    description: 'Calm and serene content'
  },
  nostalgic: {
    label: 'Nostalgic',
    icon: Star,
    color: 'text-amber-400',
    bgColor: 'bg-amber-500/20 hover:bg-amber-500/30',
    description: 'Memories and throwback content'
  },
  excited: {
    label: 'Excited',
    icon: Sun,
    color: 'text-pink-400',
    bgColor: 'bg-pink-500/20 hover:bg-pink-500/30',
    description: 'Thrilling and anticipatory content'
  }
}

export function TikTokMoodSelector({
  selectedMoods,
  onMoodChange,
  moodCounts,
  maxSelections = 3,
  showCounts = true,
  className
}: TikTokMoodSelectorProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleMoodToggle = (mood: MoodType) => {
    if (selectedMoods.includes(mood)) {
      // Remove mood
      onMoodChange(selectedMoods.filter(m => m !== mood))
    } else {
      // Add mood (respect max selections)
      if (selectedMoods.length < maxSelections) {
        onMoodChange([...selectedMoods, mood])
      }
    }
  }

  const clearAllMoods = () => {
    onMoodChange([])
  }

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  return (
    <div className={cn(
      "bg-gradient-to-b from-black/60 to-transparent backdrop-blur-sm",
      className
    )}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Sparkles className="h-4 w-4 text-hvppy-400" />
            <span className="text-white text-sm font-medium">
              Mood Filter
            </span>
            <Badge variant="secondary" className="text-xs">
              {selectedMoods.length}/{maxSelections}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setIsExpanded(!isExpanded)}
              variant="ghost"
              size="sm"
              className="text-white/70 hover:text-white text-xs"
            >
              {isExpanded ? 'Less' : 'More'}
            </Button>
            
            {selectedMoods.length > 0 && (
              <Button
                onClick={clearAllMoods}
                variant="ghost"
                size="sm"
                className="text-white/70 hover:text-white"
                aria-label="Clear all moods"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Selected Moods Display */}
        {selectedMoods.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {selectedMoods.map((mood) => {
              const config = moodConfig[mood]
              const Icon = config.icon
              
              return (
                <Badge
                  key={mood}
                  variant="secondary"
                  className={cn(
                    "flex items-center space-x-1 px-3 py-1 cursor-pointer transition-all duration-200",
                    config.bgColor,
                    config.color,
                    "hover:scale-105"
                  )}
                  onClick={() => handleMoodToggle(mood)}
                >
                  <Icon className="h-3 w-3" />
                  <span className="text-xs font-medium">{config.label}</span>
                  {showCounts && moodCounts?.[mood] && (
                    <span className="text-xs opacity-70">
                      {formatCount(moodCounts[mood])}
                    </span>
                  )}
                  <X className="h-3 w-3 ml-1 opacity-70 hover:opacity-100" />
                </Badge>
              )
            })}
          </div>
        )}

        {/* Mood Options */}
        <div className={cn(
          "grid gap-2 transition-all duration-300",
          isExpanded ? "grid-cols-4" : "grid-cols-6"
        )}>
          {Object.entries(moodConfig).map(([mood, config]) => {
            const isSelected = selectedMoods.includes(mood as MoodType)
            const isDisabled = !isSelected && selectedMoods.length >= maxSelections
            const Icon = config.icon
            const count = moodCounts?.[mood as MoodType]
            
            return (
              <Button
                key={mood}
                onClick={() => handleMoodToggle(mood as MoodType)}
                disabled={isDisabled}
                variant="ghost"
                size="sm"
                className={cn(
                  "flex flex-col items-center space-y-1 p-2 h-auto transition-all duration-200",
                  "hover:scale-105 active:scale-95",
                  isSelected 
                    ? cn(config.bgColor, config.color, "border border-current") 
                    : "bg-white/10 text-white/70 hover:bg-white/20 hover:text-white",
                  isDisabled && "opacity-50 cursor-not-allowed"
                )}
                aria-label={`${isSelected ? 'Remove' : 'Add'} ${config.label} mood filter`}
              >
                <Icon className="h-4 w-4" />
                {isExpanded && (
                  <>
                    <span className="text-xs font-medium">{config.label}</span>
                    {showCounts && count && (
                      <span className="text-xs opacity-70">
                        {formatCount(count)}
                      </span>
                    )}
                  </>
                )}
              </Button>
            )
          })}
        </div>

        {/* Mood Description */}
        {selectedMoods.length === 1 && (
          <div className="mt-3 p-2 bg-white/5 rounded-lg">
            <p className="text-white/60 text-xs">
              {moodConfig[selectedMoods[0]].description}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
