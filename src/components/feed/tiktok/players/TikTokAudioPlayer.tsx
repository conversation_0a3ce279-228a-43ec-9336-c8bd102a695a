"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  SkipBack,
  SkipForward,
  RotateCcw,
  Music,
  Disc,
  Waves
} from 'lucide-react'

interface TikTokAudioPlayerProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onRegisterMedia?: (element: HTMLAudioElement, itemId: string, type: 'audio') => void
  onUnregisterMedia?: (itemId: string) => void
  preloadedContent?: any
  isMuted?: boolean
  onVolumeToggle?: () => void
  className?: string
}

export function TikTokAudioPlayer({
  item,
  isActive,
  autoPlay = true,
  onRegisterMedia,
  onUnregisterMedia,
  preloadedContent,
  isMuted = false, // Audio should not be muted by default
  onVolumeToggle,
  className
}: TikTokAudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const animationRef = useRef<number>()

  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [showControls, setShowControls] = useState(true)

  // Register audio element with media manager
  useEffect(() => {
    const audio = audioRef.current
    if (audio && onRegisterMedia) {
      onRegisterMedia(audio, item.post.id, 'audio')
    }

    return () => {
      if (onUnregisterMedia) {
        onUnregisterMedia(item.post.id)
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [item.post.id, onRegisterMedia, onUnregisterMedia])

  // Handle autoplay when active
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    if (isActive && autoPlay) {
      audio.play().catch((err) => {
        console.warn('Autoplay failed:', err)
        setError('Autoplay failed. Tap to play.')
      })
    } else {
      audio.pause()
    }
  }, [isActive, autoPlay])

  // Handle mute state
  useEffect(() => {
    const audio = audioRef.current
    if (audio) {
      audio.muted = isMuted || false
    }
  }, [isMuted])

  // Setup audio visualization
  useEffect(() => {
    const audio = audioRef.current
    const canvas = canvasRef.current
    if (!audio || !canvas) return

    const setupAudioContext = () => {
      try {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
        const source = audioContextRef.current.createMediaElementSource(audio)
        analyserRef.current = audioContextRef.current.createAnalyser()
        
        source.connect(analyserRef.current)
        analyserRef.current.connect(audioContextRef.current.destination)
        
        analyserRef.current.fftSize = 256
      } catch (err) {
        console.warn('Audio context setup failed:', err)
      }
    }

    audio.addEventListener('play', setupAudioContext, { once: true })
    
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  // Audio visualization animation
  const drawVisualization = useCallback(() => {
    const canvas = canvasRef.current
    const analyser = analyserRef.current
    if (!canvas || !analyser) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const bufferLength = analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    
    const draw = () => {
      analyser.getByteFrequencyData(dataArray)
      
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      const barWidth = canvas.width / bufferLength
      let x = 0
      
      // Get mood-based colors
      const primaryMood = item.post.moods[0]
      const colors = getMoodColors(primaryMood)
      
      for (let i = 0; i < bufferLength; i++) {
        const barHeight = (dataArray[i] / 255) * canvas.height * 0.8
        
        const gradient = ctx.createLinearGradient(0, canvas.height, 0, canvas.height - barHeight)
        gradient.addColorStop(0, colors.primary)
        gradient.addColorStop(1, colors.secondary)
        
        ctx.fillStyle = gradient
        ctx.fillRect(x, canvas.height - barHeight, barWidth - 1, barHeight)
        
        x += barWidth
      }
      
      if (isPlaying) {
        animationRef.current = requestAnimationFrame(draw)
      }
    }
    
    if (isPlaying) {
      draw()
    }
  }, [isPlaying, item.post.moods])

  useEffect(() => {
    if (isPlaying) {
      drawVisualization()
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }
  }, [isPlaying, drawVisualization])

  // Audio event handlers
  const handleLoadStart = useCallback(() => {
    setIsLoading(true)
    setError(null)
  }, [])

  const handleLoadedData = useCallback(() => {
    setIsLoading(false)
    const audio = audioRef.current
    if (audio) {
      setDuration(audio.duration)
    }
  }, [])

  const handlePlay = useCallback(() => {
    setIsPlaying(true)
  }, [])

  const handlePause = useCallback(() => {
    setIsPlaying(false)
  }, [])

  const handleTimeUpdate = useCallback(() => {
    const audio = audioRef.current
    if (audio && duration > 0) {
      setCurrentTime(audio.currentTime)
      setProgress((audio.currentTime / duration) * 100)
    }
  }, [duration])

  const handleError = useCallback(() => {
    setError('Failed to load audio')
    setIsLoading(false)
  }, [])

  const handleEnded = useCallback(() => {
    setIsPlaying(false)
    setProgress(0)
    setCurrentTime(0)
  }, [])

  // Control handlers
  const togglePlayPause = useCallback(() => {
    const audio = audioRef.current
    if (!audio) return

    if (audio.paused) {
      audio.play().catch((err) => {
        console.error('Play failed:', err)
        setError('Playback failed')
      })
    } else {
      audio.pause()
    }
  }, [])

  const handleSeek = useCallback((percentage: number) => {
    const audio = audioRef.current
    if (audio && duration > 0) {
      audio.currentTime = (percentage / 100) * duration
    }
  }, [duration])

  const handleSkip = useCallback((seconds: number) => {
    const audio = audioRef.current
    if (audio) {
      audio.currentTime = Math.max(0, Math.min(duration, audio.currentTime + seconds))
    }
  }, [duration])

  // Get mood-based colors for visualization
  const getMoodColors = (mood?: string) => {
    switch (mood) {
      case 'happy':
        return { primary: '#FFD700', secondary: '#FFA500' }
      case 'chill':
        return { primary: '#87CEEB', secondary: '#4682B4' }
      case 'heartbroken':
        return { primary: '#DC143C', secondary: '#8B0000' }
      case 'inspired':
        return { primary: '#9370DB', secondary: '#4B0082' }
      case 'energetic':
        return { primary: '#FF6347', secondary: '#FF4500' }
      case 'peaceful':
        return { primary: '#98FB98', secondary: '#32CD32' }
      default:
        return { primary: '#ec5eff', secondary: '#d936f0' } // HVPPY colors
    }
  }

  // Format time display
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Get audio source URL
  const getAudioSource = () => {
    if (preloadedContent?.url) {
      return preloadedContent.url
    }
    return item.post.contentUrl || item.post.mediaUrls[0]
  }

  return (
    <div className={cn("relative w-full h-full bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden", className)}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-gradient-to-r from-hvppy-500/20 to-hvppy-700/20" />
      </div>

      {/* Audio Element */}
      <audio
        ref={audioRef}
        preload={isActive ? "auto" : "metadata"}
        loop
        muted={isMuted}
        onLoadStart={handleLoadStart}
        onLoadedData={handleLoadedData}
        onPlay={handlePlay}
        onPause={handlePause}
        onTimeUpdate={handleTimeUpdate}
        onError={handleError}
        onEnded={handleEnded}
      >
        <source src={getAudioSource()} type="audio/mpeg" />
        <source src={getAudioSource()} type="audio/wav" />
        Your browser does not support the audio element.
      </audio>

      {/* Album Art / Visualization Background */}
      <div className="absolute inset-0 flex items-center justify-center">
        {item.post.thumbnailUrl ? (
          <img
            src={item.post.thumbnailUrl}
            alt="Album art"
            className="w-64 h-64 rounded-full object-cover shadow-2xl opacity-80"
          />
        ) : (
          <div className="w-64 h-64 rounded-full bg-gradient-to-br from-hvppy-500 to-hvppy-700 flex items-center justify-center shadow-2xl">
            <Music className="h-24 w-24 text-white opacity-80" />
          </div>
        )}
      </div>

      {/* Audio Visualization */}
      <canvas
        ref={canvasRef}
        width={400}
        height={200}
        className="absolute bottom-32 left-1/2 transform -translate-x-1/2 opacity-70"
      />

      {/* Rotating Disc Effect */}
      {isPlaying && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-64 h-64 rounded-full border-4 border-white/20 animate-spin" style={{ animationDuration: '3s' }}>
            <div className="w-full h-full rounded-full border-2 border-white/10" />
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">Loading audio...</p>
          </div>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-center text-white">
            <p className="text-sm mb-2">{error}</p>
            <Button
              onClick={togglePlayPause}
              variant="outline"
              size="sm"
              className="text-white border-white hover:bg-white hover:text-black"
            >
              <Play className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Controls */}
      {showControls && (
        <div className="absolute bottom-8 left-8 right-8">
          {/* Progress Bar */}
          <div 
            className="w-full bg-white/20 rounded-full h-2 mb-4 cursor-pointer"
            onClick={(e) => {
              const rect = e.currentTarget.getBoundingClientRect()
              const percentage = ((e.clientX - rect.left) / rect.width) * 100
              handleSeek(percentage)
            }}
          >
            <div 
              className="bg-hvppy-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Time Display */}
          <div className="flex justify-between text-white text-sm mb-4">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-center space-x-4">
            <Button
              onClick={() => handleSkip(-10)}
              variant="ghost"
              size="icon"
              className="h-12 w-12 rounded-full bg-white/10 text-white hover:bg-white/20"
            >
              <SkipBack className="h-6 w-6" />
            </Button>

            <Button
              onClick={togglePlayPause}
              variant="ghost"
              size="icon"
              className="h-16 w-16 rounded-full bg-hvppy-500 text-white hover:bg-hvppy-600"
            >
              {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
            </Button>

            <Button
              onClick={() => handleSkip(10)}
              variant="ghost"
              size="icon"
              className="h-12 w-12 rounded-full bg-white/10 text-white hover:bg-white/20"
            >
              <SkipForward className="h-6 w-6" />
            </Button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center justify-center mt-4">
            <Button
              onClick={onVolumeToggle}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-white/10 text-white hover:bg-white/20"
            >
              {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      {isActive && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
          <div 
            className="h-full bg-hvppy-500 transition-all duration-100"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  )
}
