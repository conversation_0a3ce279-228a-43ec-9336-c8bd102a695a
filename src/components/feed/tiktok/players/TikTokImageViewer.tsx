"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  Minimize,
  RotateCw,
  Download,
  Eye,
  Heart,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface TikTokImageViewerProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onRegisterMedia?: (element: HTMLImageElement, itemId: string, type: 'image') => void
  onUnregisterMedia?: (itemId: string) => void
  preloadedContent?: any
  isMuted?: boolean
  onVolumeToggle?: () => void
  className?: string
}

export function TikTokImageViewer({
  item,
  isActive,
  autoPlay,
  onRegisterMedia,
  onUnregisterMedia,
  preloadedContent,
  className
}: TikTokImageViewerProps) {
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [zoom, setZoom] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [showControls, setShowControls] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [rotation, setRotation] = useState(0)
  const [lastTap, setLastTap] = useState(0)

  // Get all image URLs
  const imageUrls = [
    item.post.contentUrl,
    ...item.post.mediaUrls
  ].filter(Boolean) as string[]

  // Register image element
  useEffect(() => {
    const image = imageRef.current
    if (image && onRegisterMedia) {
      onRegisterMedia(image, item.post.id, 'image')
    }

    return () => {
      if (onUnregisterMedia) {
        onUnregisterMedia(item.post.id)
      }
    }
  }, [item.post.id, onRegisterMedia, onUnregisterMedia])

  // Image event handlers
  const handleImageLoad = useCallback(() => {
    setIsLoading(false)
    setError(null)
  }, [])

  const handleImageError = useCallback(() => {
    setError('Failed to load image')
    setIsLoading(false)
  }, [])

  // Zoom controls
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.5, 5))
  }, [])

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.5, 0.5))
    if (zoom <= 1) {
      setPosition({ x: 0, y: 0 })
    }
  }, [zoom])

  const resetZoom = useCallback(() => {
    setZoom(1)
    setPosition({ x: 0, y: 0 })
    setRotation(0)
  }, [])

  // Rotation control
  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360)
  }, [])

  // Fullscreen control
  const toggleFullscreen = useCallback(() => {
    const container = containerRef.current
    if (!container) return

    if (!document.fullscreenElement) {
      container.requestFullscreen().then(() => {
        setIsFullscreen(true)
      }).catch((err) => {
        console.error('Fullscreen failed:', err)
      })
    } else {
      document.exitFullscreen().then(() => {
        setIsFullscreen(false)
      })
    }
  }, [])

  // Image navigation
  const nextImage = useCallback(() => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex(prev => (prev + 1) % imageUrls.length)
      resetZoom()
    }
  }, [imageUrls.length, resetZoom])

  const prevImage = useCallback(() => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex(prev => (prev - 1 + imageUrls.length) % imageUrls.length)
      resetZoom()
    }
  }, [imageUrls.length, resetZoom])

  // Touch/Mouse interactions
  const handleInteractionStart = useCallback((clientX: number, clientY: number) => {
    setIsDragging(true)
    setDragStart({ x: clientX - position.x, y: clientY - position.y })
  }, [position])

  const handleInteractionMove = useCallback((clientX: number, clientY: number) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: clientX - dragStart.x,
        y: clientY - dragStart.y
      })
    }
  }, [isDragging, dragStart, zoom])

  const handleInteractionEnd = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Mouse events
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    handleInteractionStart(e.clientX, e.clientY)
  }, [handleInteractionStart])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    handleInteractionMove(e.clientX, e.clientY)
  }, [handleInteractionMove])

  const handleMouseUp = useCallback(() => {
    handleInteractionEnd()
  }, [handleInteractionEnd])

  // Touch events
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0]
      handleInteractionStart(touch.clientX, touch.clientY)
    }
  }, [handleInteractionStart])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0]
      handleInteractionMove(touch.clientX, touch.clientY)
    }
  }, [handleInteractionMove])

  const handleTouchEnd = useCallback(() => {
    handleInteractionEnd()
  }, [handleInteractionEnd])

  // Double tap to zoom
  const handleImageTap = useCallback(() => {
    const now = Date.now()
    const timeDiff = now - lastTap

    if (timeDiff < 300) {
      // Double tap - toggle zoom
      if (zoom === 1) {
        setZoom(2)
      } else {
        resetZoom()
      }
    } else {
      // Single tap - toggle controls
      setShowControls(!showControls)
      setTimeout(() => setShowControls(false), 3000)
    }

    setLastTap(now)
  }, [lastTap, zoom, showControls, resetZoom])

  // Get mood-based filter
  const getMoodFilter = () => {
    const primaryMood = item.post.moods[0]
    switch (primaryMood) {
      case 'happy':
        return 'brightness(1.1) saturate(1.2) hue-rotate(10deg)'
      case 'chill':
        return 'brightness(0.95) saturate(0.9) hue-rotate(-10deg)'
      case 'heartbroken':
        return 'brightness(0.8) saturate(0.7) contrast(1.1) sepia(0.2)'
      case 'energetic':
        return 'brightness(1.2) saturate(1.3) contrast(1.1)'
      case 'peaceful':
        return 'brightness(0.95) saturate(0.8) hue-rotate(5deg)'
      case 'inspired':
        return 'brightness(1.05) saturate(1.1) contrast(1.05)'
      default:
        return 'none'
    }
  }

  const currentImageUrl = imageUrls[currentImageIndex] || item.post.thumbnailUrl

  return (
    <div 
      ref={containerRef}
      className={cn("relative w-full h-full bg-black overflow-hidden cursor-grab", className)}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Main Image */}
      <div className="absolute inset-0 flex items-center justify-center">
        <img
          ref={imageRef}
          src={currentImageUrl}
          alt={item.post.title || 'Image content'}
          className="max-w-full max-h-full object-contain cursor-pointer transition-transform duration-200"
          style={{
            transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px) rotate(${rotation}deg)`,
            filter: getMoodFilter()
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
          onClick={handleImageTap}
          draggable={false}
        />
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">Loading image...</p>
          </div>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-center text-white">
            <p className="text-sm mb-2">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              size="sm"
              className="text-white border-white hover:bg-white hover:text-black"
            >
              <Eye className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* Image Navigation (for multiple images) */}
      {imageUrls.length > 1 && (
        <>
          <Button
            onClick={prevImage}
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 h-12 w-12 rounded-full bg-black/50 text-white hover:bg-black/70"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          
          <Button
            onClick={nextImage}
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 h-12 w-12 rounded-full bg-black/50 text-white hover:bg-black/70"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Image Counter */}
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            {currentImageIndex + 1} / {imageUrls.length}
          </div>
        </>
      )}

      {/* Controls Overlay */}
      {showControls && (
        <div className="absolute inset-0 bg-black/20">
          {/* Top Controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              onClick={handleZoomOut}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
              disabled={zoom <= 0.5}
            >
              <ZoomOut className="h-5 w-5" />
            </Button>
            
            <Button
              onClick={handleZoomIn}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
              disabled={zoom >= 5}
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
            
            <Button
              onClick={handleRotate}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              <RotateCw className="h-5 w-5" />
            </Button>
            
            <Button
              onClick={toggleFullscreen}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
            </Button>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <Button
              onClick={resetZoom}
              variant="ghost"
              size="sm"
              className="bg-black/50 text-white hover:bg-black/70"
            >
              Reset View
            </Button>
            
            <Button
              onClick={() => {
                if (currentImageUrl) {
                  const link = document.createElement('a')
                  link.href = currentImageUrl
                  link.download = `hvppy-image-${item.post.id}`
                  link.click()
                }
              }}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              <Download className="h-5 w-5" />
            </Button>
          </div>
        </div>
      )}

      {/* Zoom Indicator */}
      {zoom !== 1 && (
        <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
          {Math.round(zoom * 100)}%
        </div>
      )}

      {/* Tap Instructions */}
      {!showControls && zoom === 1 && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 text-sm text-center">
          <p>Tap to show controls • Double tap to zoom</p>
        </div>
      )}
    </div>
  )
}
