"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Type, 
  Palette, 
  ZoomIn, 
  ZoomOut,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Copy,
  Share,
  BookOpen,
  Quote
} from 'lucide-react'

interface TikTokTextRendererProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onRegisterMedia?: (element: HTMLDivElement, itemId: string, type: 'text') => void
  onUnregisterMedia?: (itemId: string) => void
  preloadedContent?: any
  isMuted?: boolean
  onVolumeToggle?: () => void
  className?: string
}

export function TikTokTextRenderer({
  item,
  isActive,
  autoPlay,
  onRegisterMedia,
  onUnregisterMedia,
  className
}: TikTokTextRendererProps) {
  const [fontSize, setFontSize] = useState(18)
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right'>('left')
  const [showControls, setShowControls] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [estimatedReadTime, setEstimatedReadTime] = useState(0)
  const [currentTheme, setCurrentTheme] = useState(0)

  // Text themes based on moods
  const textThemes = [
    {
      name: 'Default',
      background: 'bg-gradient-to-br from-gray-900 via-black to-gray-800',
      textColor: 'text-white',
      accentColor: 'text-hvppy-400'
    },
    {
      name: 'Happy',
      background: 'bg-gradient-to-br from-yellow-900 via-orange-900 to-red-900',
      textColor: 'text-yellow-100',
      accentColor: 'text-yellow-400'
    },
    {
      name: 'Chill',
      background: 'bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900',
      textColor: 'text-blue-100',
      accentColor: 'text-blue-400'
    },
    {
      name: 'Peaceful',
      background: 'bg-gradient-to-br from-green-900 via-teal-900 to-cyan-900',
      textColor: 'text-green-100',
      accentColor: 'text-green-400'
    },
    {
      name: 'Inspired',
      background: 'bg-gradient-to-br from-purple-900 via-pink-900 to-rose-900',
      textColor: 'text-purple-100',
      accentColor: 'text-purple-400'
    }
  ]

  // Calculate reading time and progress
  useEffect(() => {
    const wordCount = item.post.content.split(/\s+/).length
    const avgWordsPerMinute = 200
    setEstimatedReadTime(Math.ceil(wordCount / avgWordsPerMinute))
  }, [item.post.content])

  // Auto-scroll reading progress simulation
  useEffect(() => {
    if (!isActive || !autoPlay) return

    const duration = estimatedReadTime * 60 * 1000 // Convert to milliseconds
    const interval = 100 // Update every 100ms
    const increment = (interval / duration) * 100

    const timer = setInterval(() => {
      setReadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer)
          return 100
        }
        return prev + increment
      })
    }, interval)

    return () => clearInterval(timer)
  }, [isActive, autoPlay, estimatedReadTime])

  // Get theme based on mood
  useEffect(() => {
    const primaryMood = item.post.moods[0]
    switch (primaryMood) {
      case 'happy':
        setCurrentTheme(1)
        break
      case 'chill':
        setCurrentTheme(2)
        break
      case 'peaceful':
        setCurrentTheme(3)
        break
      case 'inspired':
        setCurrentTheme(4)
        break
      default:
        setCurrentTheme(0)
    }
  }, [item.post.moods])

  // Font size controls
  const increaseFontSize = useCallback(() => {
    setFontSize(prev => Math.min(prev + 2, 32))
  }, [])

  const decreaseFontSize = useCallback(() => {
    setFontSize(prev => Math.max(prev - 2, 12))
  }, [])

  // Text alignment controls
  const cycleTextAlign = useCallback(() => {
    setTextAlign(prev => {
      switch (prev) {
        case 'left': return 'center'
        case 'center': return 'right'
        case 'right': return 'left'
        default: return 'left'
      }
    })
  }, [])

  // Copy text to clipboard
  const copyText = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(item.post.content)
      // TODO: Show toast notification
    } catch (err) {
      console.error('Failed to copy text:', err)
    }
  }, [item.post.content])

  // Share text
  const shareText = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: item.post.title || 'HVPPY Content',
          text: item.post.content,
          url: window.location.href
        })
      } catch (err) {
        console.error('Failed to share:', err)
      }
    } else {
      copyText()
    }
  }, [item.post.content, item.post.title, copyText])

  // Toggle controls
  const toggleControls = useCallback(() => {
    setShowControls(!showControls)
    setTimeout(() => setShowControls(false), 5000)
  }, [showControls])

  // Format text with basic markdown-like styling
  const formatText = (text: string) => {
    return text
      .split('\n')
      .map((paragraph, index) => {
        if (paragraph.trim() === '') return null
        
        // Handle quotes
        if (paragraph.trim().startsWith('>')) {
          return (
            <blockquote key={index} className="border-l-4 border-current pl-4 italic opacity-80 my-4">
              {paragraph.replace(/^>\s*/, '')}
            </blockquote>
          )
        }
        
        // Handle headers
        if (paragraph.startsWith('#')) {
          const level = paragraph.match(/^#+/)?.[0].length || 1
          const text = paragraph.replace(/^#+\s*/, '')
          const HeaderTag = `h${Math.min(level, 6)}` as keyof JSX.IntrinsicElements
          
          return (
            <HeaderTag key={index} className="font-bold mb-4 text-current">
              {text}
            </HeaderTag>
          )
        }
        
        return (
          <p key={index} className="mb-4 leading-relaxed">
            {paragraph}
          </p>
        )
      })
      .filter(Boolean)
  }

  const theme = textThemes[currentTheme]

  return (
    <div 
      className={cn(
        "relative w-full h-full overflow-hidden",
        theme.background,
        className
      )}
      onClick={toggleControls}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full bg-gradient-to-br from-white/10 to-transparent" />
      </div>

      {/* Main Content */}
      <div className="relative h-full overflow-y-auto scrollbar-hide">
        <div className="p-8 min-h-full flex flex-col">
          {/* Header */}
          <div className="mb-6">
            {item.post.title && (
              <h1 className={cn("text-2xl font-bold mb-4", theme.textColor)}>
                {item.post.title}
              </h1>
            )}
            
            {/* Metadata */}
            <div className="flex items-center space-x-4 text-sm opacity-70">
              <div className={cn("flex items-center space-x-1", theme.textColor)}>
                <BookOpen className="h-4 w-4" />
                <span>{estimatedReadTime} min read</span>
              </div>
              
              {item.post.moods.length > 0 && (
                <div className="flex space-x-1">
                  {item.post.moods.slice(0, 3).map((mood) => (
                    <Badge
                      key={mood}
                      variant="secondary"
                      className={cn(
                        "text-xs px-2 py-1 bg-white/10 border-none",
                        theme.textColor
                      )}
                    >
                      {mood}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Text Content */}
          <div 
            className={cn(
              "flex-1 leading-relaxed",
              theme.textColor,
              `text-${textAlign}`
            )}
            style={{ 
              fontSize: `${fontSize}px`,
              lineHeight: 1.6
            }}
          >
            {formatText(item.post.content)}
          </div>

          {/* Footer */}
          <div className="mt-8 pt-4 border-t border-white/10">
            <div className={cn("text-sm opacity-60", theme.textColor)}>
              <p>By {item.post.creator?.name || item.post.user.displayName}</p>
              {item.post.creator?.stageName && (
                <p>@{item.post.creator.stageName}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Reading Progress Bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
        <div 
          className={cn("h-full transition-all duration-100", theme.accentColor.replace('text-', 'bg-'))}
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Controls Overlay */}
      {showControls && (
        <div className="absolute inset-0 bg-black/20">
          {/* Top Controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              onClick={decreaseFontSize}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
              disabled={fontSize <= 12}
            >
              <ZoomOut className="h-5 w-5" />
            </Button>
            
            <Button
              onClick={increaseFontSize}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
              disabled={fontSize >= 32}
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
            
            <Button
              onClick={cycleTextAlign}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              {textAlign === 'left' && <AlignLeft className="h-5 w-5" />}
              {textAlign === 'center' && <AlignCenter className="h-5 w-5" />}
              {textAlign === 'right' && <AlignRight className="h-5 w-5" />}
            </Button>
            
            <Button
              onClick={() => setCurrentTheme((prev) => (prev + 1) % textThemes.length)}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              <Palette className="h-5 w-5" />
            </Button>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <Button
              onClick={copyText}
              variant="ghost"
              size="sm"
              className="bg-black/50 text-white hover:bg-black/70"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
            
            <Button
              onClick={shareText}
              variant="ghost"
              size="sm"
              className="bg-black/50 text-white hover:bg-black/70"
            >
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </div>
      )}

      {/* Font Size Indicator */}
      {showControls && (
        <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
          {fontSize}px
        </div>
      )}

      {/* Theme Indicator */}
      {showControls && (
        <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
          {theme.name}
        </div>
      )}

      {/* Tap Instructions */}
      {!showControls && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 text-sm text-center">
          <p>Tap to show reading controls</p>
        </div>
      )}
    </div>
  )
}
