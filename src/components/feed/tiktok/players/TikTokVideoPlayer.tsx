"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { FeedItem } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Minimize,
  RotateCcw,
  Settings
} from 'lucide-react'

interface TikTokVideoPlayerProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onRegisterMedia?: (element: HTMLVideoElement, itemId: string, type: 'video') => void
  onUnregisterMedia?: (itemId: string) => void
  preloadedContent?: any
  isMuted?: boolean
  onVolumeToggle?: () => void
  className?: string
}

export function TikTokVideoPlayer({
  item,
  isActive,
  autoPlay = true,
  onRegisterMedia,
  onUnregisterMedia,
  preloadedContent,
  isMuted = true,
  onVolumeToggle,
  className
}: TikTokVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControls, setShowControls] = useState(false)
  const [lastTap, setLastTap] = useState(0)

  // Register video element with media manager
  useEffect(() => {
    const video = videoRef.current
    if (video && onRegisterMedia) {
      onRegisterMedia(video, item.post.id, 'video')
    }

    return () => {
      if (onUnregisterMedia) {
        onUnregisterMedia(item.post.id)
      }
    }
  }, [item.post.id, onRegisterMedia, onUnregisterMedia])

  // Handle autoplay when active
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    if (isActive && autoPlay) {
      video.play().catch((err) => {
        console.warn('Autoplay failed:', err)
        setError('Autoplay failed. Tap to play.')
      })
    } else {
      video.pause()
    }
  }, [isActive, autoPlay])

  // Handle mute state
  useEffect(() => {
    const video = videoRef.current
    if (video) {
      video.muted = isMuted || false
    }
  }, [isMuted])

  // Video event handlers
  const handleLoadStart = useCallback(() => {
    setIsLoading(true)
    setError(null)
  }, [])

  const handleLoadedData = useCallback(() => {
    setIsLoading(false)
    const video = videoRef.current
    if (video) {
      setDuration(video.duration)
    }
  }, [])

  const handlePlay = useCallback(() => {
    setIsPlaying(true)
  }, [])

  const handlePause = useCallback(() => {
    setIsPlaying(false)
  }, [])

  const handleTimeUpdate = useCallback(() => {
    const video = videoRef.current
    if (video && duration > 0) {
      setProgress((video.currentTime / duration) * 100)
    }
  }, [duration])

  const handleError = useCallback(() => {
    setError('Failed to load video')
    setIsLoading(false)
  }, [])

  const handleEnded = useCallback(() => {
    setIsPlaying(false)
    setProgress(0)
  }, [])

  // Control handlers
  const togglePlayPause = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (video.paused) {
      video.play().catch((err) => {
        console.error('Play failed:', err)
        setError('Playback failed')
      })
    } else {
      video.pause()
    }
  }, [])

  const toggleFullscreen = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (!document.fullscreenElement) {
      video.requestFullscreen().then(() => {
        setIsFullscreen(true)
      }).catch((err) => {
        console.error('Fullscreen failed:', err)
      })
    } else {
      document.exitFullscreen().then(() => {
        setIsFullscreen(false)
      })
    }
  }, [])

  const handleRestart = useCallback(() => {
    const video = videoRef.current
    if (video) {
      video.currentTime = 0
      video.play()
    }
  }, [])

  // Double tap to like (TikTok-style)
  const handleVideoTap = useCallback(() => {
    const now = Date.now()
    const timeDiff = now - lastTap

    if (timeDiff < 300) {
      // Double tap - trigger like
      // TODO: Implement like functionality
      console.log('Double tap like!')
    } else {
      // Single tap - toggle controls
      setShowControls(!showControls)
      setTimeout(() => setShowControls(false), 3000)
    }

    setLastTap(now)
  }, [lastTap, showControls])

  // Get video source URL
  const getVideoSource = () => {
    if (preloadedContent?.url) {
      return preloadedContent.url
    }
    return item.post.contentUrl || item.post.mediaUrls[0]
  }

  // Get mood-based filter
  const getMoodFilter = () => {
    const primaryMood = item.post.moods[0]
    switch (primaryMood) {
      case 'happy':
        return 'brightness(1.1) saturate(1.2)'
      case 'chill':
        return 'brightness(0.9) saturate(0.8) hue-rotate(10deg)'
      case 'heartbroken':
        return 'brightness(0.8) saturate(0.7) contrast(1.1)'
      case 'energetic':
        return 'brightness(1.2) saturate(1.3) contrast(1.1)'
      case 'peaceful':
        return 'brightness(0.95) saturate(0.9) hue-rotate(-10deg)'
      default:
        return 'none'
    }
  }

  return (
    <div className={cn("relative w-full h-full bg-black overflow-hidden", className)}>
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover cursor-pointer"
        style={{ filter: getMoodFilter() }}
        poster={item.post.thumbnailUrl}
        preload={isActive ? "auto" : "metadata"}
        playsInline
        loop
        muted={isMuted}
        onClick={handleVideoTap}
        onLoadStart={handleLoadStart}
        onLoadedData={handleLoadedData}
        onPlay={handlePlay}
        onPause={handlePause}
        onTimeUpdate={handleTimeUpdate}
        onError={handleError}
        onEnded={handleEnded}
        aria-label={`Video: ${item.post.title || 'Untitled'}`}
      >
        <source src={getVideoSource()} type="video/mp4" />
        <source src={getVideoSource()} type="video/webm" />
        Your browser does not support the video tag.
      </video>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}

      {/* Error Overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-center text-white">
            <p className="text-sm mb-2">{error}</p>
            <Button
              onClick={togglePlayPause}
              variant="outline"
              size="sm"
              className="text-white border-white hover:bg-white hover:text-black"
            >
              <Play className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Play/Pause Overlay */}
      {!isPlaying && !isLoading && !error && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            onClick={togglePlayPause}
            variant="ghost"
            size="icon"
            className="h-16 w-16 rounded-full bg-black/50 text-white hover:bg-black/70"
          >
            <Play className="h-8 w-8" />
          </Button>
        </div>
      )}

      {/* Controls Overlay */}
      {showControls && (
        <div className="absolute inset-0 bg-black/20">
          {/* Top Controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              onClick={onVolumeToggle}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
            </Button>
            
            <Button
              onClick={toggleFullscreen}
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            >
              {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
            </Button>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-4 left-4 right-4">
            {/* Progress Bar */}
            <div className="w-full bg-white/20 rounded-full h-1 mb-3">
              <div 
                className="bg-white h-1 rounded-full transition-all duration-100"
                style={{ width: `${progress}%` }}
              />
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <Button
                  onClick={handleRestart}
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
                
                <Button
                  onClick={togglePlayPause}
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
              </div>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      {isActive && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
          <div 
            className="h-full bg-hvppy-500 transition-all duration-100"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  )
}
