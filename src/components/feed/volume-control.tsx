"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { 
  Volume2, 
  VolumeX, 
  Volume1,
  Settings,
  Maximize,
  Minimize
} from 'lucide-react'

interface VolumeControlProps {
  volume: number
  muted: boolean
  onVolumeChange: (volume: number) => void
  onMuteToggle: () => void
  onFullscreenToggle?: () => void
  isFullscreen?: boolean
  className?: string
  showSlider?: boolean
  orientation?: 'horizontal' | 'vertical'
  size?: 'sm' | 'md' | 'lg'
}

export function VolumeControl({
  volume,
  muted,
  onVolumeChange,
  onMuteToggle,
  onFullscreenToggle,
  isFullscreen = false,
  className,
  showSlider = true,
  orientation = 'horizontal',
  size = 'md',
}: VolumeControlProps) {
  const [isHovering, setIsHovering] = useState(false)
  const [showVolumeSlider, setShowVolumeSlider] = useState(false)
  const [previousVolume, setPreviousVolume] = useState(volume)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Get volume icon based on current volume and mute state
  const getVolumeIcon = useCallback(() => {
    if (muted || volume === 0) {
      return VolumeX
    } else if (volume < 0.5) {
      return Volume1
    } else {
      return Volume2
    }
  }, [muted, volume])

  // Handle volume slider change
  const handleVolumeChange = useCallback((newVolume: number[]) => {
    const volumeValue = newVolume[0] / 100
    onVolumeChange(volumeValue)
    
    // If volume is changed from 0, unmute
    if (volumeValue > 0 && muted) {
      onMuteToggle()
    }
  }, [onVolumeChange, onMuteToggle, muted])

  // Handle mute toggle with volume memory
  const handleMuteToggle = useCallback(() => {
    if (!muted) {
      // Muting: remember current volume
      setPreviousVolume(volume)
    } else {
      // Unmuting: restore previous volume if it was 0
      if (volume === 0 && previousVolume > 0) {
        onVolumeChange(previousVolume)
      }
    }
    onMuteToggle()
  }, [muted, volume, previousVolume, onVolumeToggle, onVolumeChange])

  // Handle mouse enter/leave for hover effects
  const handleMouseEnter = useCallback(() => {
    setIsHovering(true)
    if (showSlider) {
      setShowVolumeSlider(true)
    }
    
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [showSlider])

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false)
    
    // Hide slider after a delay
    timeoutRef.current = setTimeout(() => {
      setShowVolumeSlider(false)
    }, 1000)
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'm':
        case 'M':
          event.preventDefault()
          handleMuteToggle()
          break
        case 'ArrowUp':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            onVolumeChange(Math.min(1, volume + 0.1))
          }
          break
        case 'ArrowDown':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            onVolumeChange(Math.max(0, volume - 0.1))
          }
          break
        case 'f':
        case 'F':
          if (onFullscreenToggle) {
            event.preventDefault()
            onFullscreenToggle()
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [volume, onVolumeChange, handleMuteToggle, onFullscreenToggle])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const VolumeIcon = getVolumeIcon()
  
  const buttonSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }

  return (
    <div
      className={cn(
        'relative flex items-center space-x-2',
        orientation === 'vertical' && 'flex-col space-x-0 space-y-2',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Volume Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleMuteToggle}
        className={cn(
          'text-white hover:bg-white/20 transition-colors',
          buttonSizes[size],
          muted && 'text-red-400'
        )}
        aria-label={muted ? 'Unmute' : 'Mute'}
      >
        <VolumeIcon className={iconSizes[size]} />
      </Button>

      {/* Volume Slider */}
      {showSlider && (showVolumeSlider || isHovering) && (
        <div
          className={cn(
            'transition-all duration-200 ease-in-out',
            orientation === 'horizontal' 
              ? 'w-20 opacity-100' 
              : 'h-20 opacity-100 flex justify-center',
            !showVolumeSlider && !isHovering && 'opacity-0 pointer-events-none'
          )}
        >
          <Slider
            value={[muted ? 0 : volume * 100]}
            onValueChange={handleVolumeChange}
            max={100}
            step={1}
            orientation={orientation}
            className={cn(
              'cursor-pointer',
              orientation === 'vertical' && 'h-full'
            )}
            aria-label="Volume"
          />
        </div>
      )}

      {/* Fullscreen Button */}
      {onFullscreenToggle && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onFullscreenToggle}
          className={cn(
            'text-white hover:bg-white/20 transition-colors',
            buttonSizes[size]
          )}
          aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
        >
          {isFullscreen ? (
            <Minimize className={iconSizes[size]} />
          ) : (
            <Maximize className={iconSizes[size]} />
          )}
        </Button>
      )}

      {/* Volume Percentage Tooltip */}
      {(showVolumeSlider || isHovering) && (
        <div
          className={cn(
            'absolute bg-black/80 text-white text-xs px-2 py-1 rounded pointer-events-none transition-opacity duration-200',
            orientation === 'horizontal' 
              ? 'top-full mt-2 left-1/2 transform -translate-x-1/2'
              : 'left-full ml-2 top-1/2 transform -translate-y-1/2'
          )}
        >
          {muted ? 'Muted' : `${Math.round(volume * 100)}%`}
        </div>
      )}
    </div>
  )
}

// Hook for managing volume state
export function useVolumeControl(initialVolume = 0.8, initialMuted = false) {
  const [volume, setVolume] = useState(initialVolume)
  const [muted, setMuted] = useState(initialMuted)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume)
    
    // Unmute if volume is increased from 0
    if (newVolume > 0 && muted) {
      setMuted(false)
    }
  }, [muted])

  const handleMuteToggle = useCallback(() => {
    setMuted(prev => !prev)
  }, [])

  const handleFullscreenToggle = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen?.()
      setIsFullscreen(false)
    }
  }, [])

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  return {
    volume,
    muted,
    isFullscreen,
    handleVolumeChange,
    handleMuteToggle,
    handleFullscreenToggle,
    effectiveVolume: muted ? 0 : volume,
  }
}
