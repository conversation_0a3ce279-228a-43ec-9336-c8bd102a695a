# HVPPY Central Layout System

A comprehensive social network layout system that combines Discord's sidebar navigation structure with TikTok's vertical content feed approach, designed specifically for HVPPY Central's unique content and mood-based features.

## 🎨 Design Philosophy

The layout system merges the best of both worlds:
- **Discord-inspired Navigation**: Familiar sidebar structure with channels, communities, and user management
- **TikTok-inspired Content**: Immersive, full-screen vertical content consumption
- **HVPPY Central Branding**: Purple gradient theme with mood-based color system

## 📁 Component Structure

```
src/components/layout/
├── main-layout.tsx          # Main layout wrapper component
├── sidebar.tsx              # Left sidebar navigation
├── top-navigation.tsx       # Top navigation bar
├── right-sidebar.tsx        # Right sidebar (activity/chat)
├── index.ts                 # Component exports
└── README.md               # This documentation
```

## 🧩 Components

### MainLayout
The primary layout wrapper that orchestrates all layout components.

**Features:**
- Responsive design with mobile adaptations
- Keyboard shortcuts (Ctrl+B for sidebar, Ctrl+R for right panel)
- Automatic mobile detection and layout adjustments
- Overlay management for mobile interactions

**Props:**
```typescript
interface MainLayoutProps {
  children: React.ReactNode
  showRightSidebar?: boolean
  sidebarCollapsed?: boolean
  className?: string
}
```

**Usage:**
```tsx
import { MainLayout } from '@/components/layout'

export default function MyPage() {
  return (
    <MainLayout showRightSidebar={true}>
      <YourContent />
    </MainLayout>
  )
}
```

### Sidebar
Discord-inspired left sidebar with HVPPY Central branding and navigation.

**Features:**
- Feed type navigation (Discover, Following, Mood-based, Trending, Experimental)
- Content type filtering (Music, Videos, Images, Podcasts)
- Quick actions (Messages, Profile, Creator Studio, Settings)
- User profile section with avatar and status
- Collapsible design with hover states
- Active route highlighting

**Navigation Structure:**
- **Feeds**: Different content discovery methods
- **Content**: Filter by media type
- **Quick Actions**: Common user actions
- **User Profile**: Current user information

### TopNavigation
Modern top navigation bar with search and user management.

**Features:**
- Global search with intelligent suggestions
- Create content dropdown with upload options
- Notifications with unread count badges
- Messages toggle for right sidebar
- User menu with profile, settings, theme toggle
- Mobile-responsive hamburger menu

**Search Functionality:**
- Real-time search suggestions
- Content, creator, and hashtag search
- Recent searches and trending topics

### RightSidebar
Activity and communication panel with tabbed interface.

**Features:**
- **Trending Tab**: Hot topics, trending hashtags, popular content
- **Creators Tab**: Suggested creators, search functionality
- **Messages Tab**: Direct messaging with chat interface
- Real-time activity updates
- Collapsible and mobile-responsive

## 🎯 Page Integration

### Feed Pages
All feed pages use the layout system with the right sidebar enabled:

```tsx
// src/app/feed/layout.tsx
import { MainLayout } from '@/components/layout/main-layout'

export default function FeedLayout({ children }: { children: React.ReactNode }) {
  return (
    <MainLayout showRightSidebar={true}>
      {children}
    </MainLayout>
  )
}
```

### Individual Feed Types
- `/feed/discover` - Algorithm-curated content
- `/feed/following` - Content from followed creators
- `/feed/mood` - Mood-based content filtering
- `/feed/trending` - Trending and popular content
- `/feed/experimental` - Beta and experimental content

### Other Pages
- `/profile` - User profile with content grid and analytics
- `/studio` - Creator dashboard and content management
- `/search` - Global search with filtered results

## 📱 Responsive Design

### Desktop (1024px+)
- Full three-column layout (sidebar + content + right panel)
- Sidebar can be collapsed to icon-only mode
- Right sidebar can be toggled on/off
- Keyboard shortcuts enabled

### Tablet (768px - 1023px)
- Sidebar collapses to icons by default
- Right sidebar overlays when opened
- Touch-friendly interactions
- Optimized spacing and sizing

### Mobile (< 768px)
- Sidebar becomes a slide-out overlay
- Right sidebar becomes full-screen overlay
- Top navigation adapts with hamburger menu
- Touch gestures for navigation

## 🎨 Theming & Customization

### HVPPY Central Branding
- Purple gradient primary colors (#ec5eff to #b821d1)
- Mood-based color system integration
- Custom CSS classes for mood states
- Dark/light theme support

### Mood Colors
```css
.mood-happy { background: #FFD700; color: black; }
.mood-chill { background: #87CEEB; color: black; }
.mood-heartbroken { background: #DC143C; color: white; }
.mood-inspired { background: #9370DB; color: white; }
.mood-energetic { background: #FF6347; color: white; }
.mood-peaceful { background: #98FB98; color: black; }
```

## ⌨️ Keyboard Shortcuts

- **Ctrl/Cmd + B**: Toggle left sidebar
- **Ctrl/Cmd + R**: Toggle right sidebar
- **Ctrl/Cmd + K**: Focus search (planned)
- **Escape**: Close overlays/modals

## 🔧 Technical Implementation

### State Management
- Uses React hooks for local component state
- Responsive breakpoint detection with `useEffect`
- Keyboard event handling with cleanup
- Mobile detection and adaptation

### Performance Optimizations
- Lazy loading of sidebar content
- Efficient re-renders with proper dependencies
- CSS transitions for smooth animations
- Optimized mobile touch interactions

### Accessibility
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management for overlays

## 🚀 Future Enhancements

### Planned Features
- [ ] Customizable sidebar order and content
- [ ] Persistent layout preferences
- [ ] Advanced search filters and suggestions
- [ ] Real-time notifications and updates
- [ ] Voice commands integration
- [ ] Gesture-based navigation

### Integration Opportunities
- [ ] Appwrite real-time subscriptions
- [ ] AI-powered content suggestions in sidebar
- [ ] Advanced analytics in right sidebar
- [ ] Live streaming integration
- [ ] Collaborative features

## 📖 Usage Examples

### Basic Page with Layout
```tsx
import { MainLayout } from '@/components/layout'

export default function MyPage() {
  return (
    <MainLayout>
      <div className="p-6">
        <h1>My Content</h1>
      </div>
    </MainLayout>
  )
}
```

### Feed Page with Right Sidebar
```tsx
import { MainLayout } from '@/components/layout'
import { VerticalFeedContainer } from '@/components/feed'

export default function FeedPage() {
  return (
    <MainLayout showRightSidebar={true}>
      <VerticalFeedContainer feedType="discover" />
    </MainLayout>
  )
}
```

### Custom Layout Configuration
```tsx
import { MainLayout } from '@/components/layout'

export default function CustomPage() {
  return (
    <MainLayout 
      showRightSidebar={false}
      sidebarCollapsed={true}
      className="custom-layout"
    >
      <YourCustomContent />
    </MainLayout>
  )
}
```

This layout system provides a solid foundation for HVPPY Central's social network interface, combining familiar navigation patterns with innovative content discovery and mood-based features.
