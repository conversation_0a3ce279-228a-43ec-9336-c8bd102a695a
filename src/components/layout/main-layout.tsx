"use client"

// Enhanced MainLayout with shadcn UI components integration and improved styling

import React, { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Sidebar } from "./sidebar"
import { TopNavigation } from "./top-navigation"
import { RightSidebar } from "./right-sidebar"

interface MainLayoutProps {
  children: React.ReactNode
  showRightSidebar?: boolean
  sidebarCollapsed?: boolean
  className?: string
}

export function MainLayout({
  children,
  showRightSidebar = false,
  sidebarCollapsed = false,
  className,
}: MainLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(sidebarCollapsed)
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(showRightSidebar)
  const [isMobile, setIsMobile] = useState(false)

  // Check for mobile viewport
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth < 768) {
        setIsSidebarCollapsed(true)
        setIsRightSidebarOpen(false)
      }
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case "b":
            event.preventDefault()
            setIsSidebarCollapsed(!isSidebarCollapsed)
            break
          case "r":
            event.preventDefault()
            setIsRightSidebarOpen(!isRightSidebarOpen)
            break
        }
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isSidebarCollapsed, isRightSidebarOpen])

  return (
    <div className={cn("h-screen bg-background overflow-hidden", className)}>
      {/* Top Navigation */}
      <TopNavigation
        onSidebarToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        onRightSidebarToggle={() => setIsRightSidebarOpen(!isRightSidebarOpen)}
        isSidebarCollapsed={isSidebarCollapsed}
        isRightSidebarOpen={isRightSidebarOpen}
        isMobile={isMobile}
      />

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Left Sidebar */}
        <Sidebar
          collapsed={isSidebarCollapsed}
          onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          isMobile={isMobile}
        />

        {/* Main Content Area */}
        <main
          className={cn(
            "flex-1 relative transition-all duration-300 ease-in-out",
            isSidebarCollapsed ? "ml-16" : "ml-64",
            isRightSidebarOpen ? "mr-80" : "mr-0",
            isMobile && "ml-0 mr-0"
          )}
        >
          {children}
        </main>

        {/* Right Sidebar */}
        {isRightSidebarOpen && (
          <RightSidebar
            onClose={() => setIsRightSidebarOpen(false)}
            isMobile={isMobile}
          />
        )}
      </div>

      {/* Mobile Overlay */}
      {isMobile && !isSidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsSidebarCollapsed(true)}
        />
      )}

      {/* Keyboard Shortcuts Help */}
      <div className="fixed bottom-4 left-4 text-xs text-muted-foreground hidden lg:block">
        <div className="bg-background/80 backdrop-blur-sm rounded p-2 border">
          <div>Ctrl+B: Toggle sidebar</div>
          <div>Ctrl+R: Toggle right panel</div>
        </div>
      </div>
    </div>
  )
}
