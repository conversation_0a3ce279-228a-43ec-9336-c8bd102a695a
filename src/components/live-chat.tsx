"use client";

import React, { useState, useEffect, useRef } from 'react';
import { databases, client } from '@/lib/appwrite';
import { ID, Query } from 'appwrite';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';

interface ChatMessage {
  $id: string;
  streamId: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: string;
}

interface LiveChatProps {
  streamId: string;
}

const APPWRITE_DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID as string;
const APPWRITE_COLLECTION_ID_CHAT = process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ID_CHAT as string;

export default function LiveChat({ streamId }: LiveChatProps) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!APPWRITE_DATABASE_ID || !APPWRITE_COLLECTION_ID_CHAT) {
      console.error("Appwrite database or collection IDs for chat are not set.");
      toast.error("Chat service not configured.");
      return;
    }

    // Fetch initial messages
    const fetchMessages = async () => {
      try {
        const response = await databases.listDocuments(
          APPWRITE_DATABASE_ID,
          APPWRITE_COLLECTION_ID_CHAT,
          [Query.equal('streamId', streamId), Query.orderAsc('timestamp')] // Order by timestamp
        );
        setMessages(response.documents as ChatMessage[]);
      } catch (error) {
        console.error('Error fetching chat messages:', error);
        toast.error('Failed to load chat messages.');
      }
    };

    fetchMessages();

    // Subscribe to real-time updates
    const unsubscribe = client.subscribe(
      `databases.${APPWRITE_DATABASE_ID}.collections.${APPWRITE_COLLECTION_ID_CHAT}.documents`,
      (response) => {
        if (response.events.includes("databases.*.collections.*.documents.*.create")) {
          const newMsg = response.payload as ChatMessage;
          if (newMsg.streamId === streamId) {
            setMessages((prev) => [...prev, newMsg]);
          }
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, [streamId]);

  useEffect(() => {
    // Scroll to bottom on new message
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !session?.user?.id || !session?.user?.name) {
      toast.error("Please type a message and ensure you are logged in.");
      return;
    }

    try {
      await databases.createDocument(
        APPWRITE_DATABASE_ID,
        APPWRITE_COLLECTION_ID_CHAT,
        ID.unique(),
        {
          streamId,
          userId: session.user.id,
          userName: session.user.name,
          message: newMessage,
          timestamp: new Date().toISOString(),
        }
      );
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message.');
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle>Live Chat</CardTitle>
        <CardDescription>Join the conversation!</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto p-4 space-y-3" ref={chatContainerRef}>
        {messages.map((msg) => (
          <div key={msg.$id} className="text-sm">
            <span className="font-semibold">{msg.userName}:</span> {msg.message}
            <span className="text-xs text-gray-500 ml-2">{new Date(msg.timestamp).toLocaleTimeString()}</span>
          </div>
        ))}
      </CardContent>
      <CardFooter className="p-4 border-t">
        <div className="flex w-full space-x-2">
          <Input
            type="text"
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') handleSendMessage();
            }}
            disabled={!session?.user?.id}
          />
          <Button onClick={handleSendMessage} disabled={!session?.user?.id}>Send</Button>
        </div>
      </CardFooter>
    </Card>
  );
}
