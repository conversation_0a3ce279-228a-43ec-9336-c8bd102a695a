"use client";

import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import * as mediasoupClient from 'mediasoup-client';

const MEDIASOUP_SERVER_URL = 'wss://localhost:3001';

export default function LiveStream() {
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  const device = useRef<mediasoupClient.Device | null>(null);
  const producerTransport = useRef<mediasoupClient.Transport | null>(null);
  const consumerTransport = useRef<mediasoupClient.Transport | null>(null);
  const producer = useRef<mediasoupClient.Producer | null>(null);
  const consumer = useRef<mediasoupClient.Consumer | null>(null);
  const ws = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Clean up on unmount
    return () => {
      if (producer.current) producer.current.close();
      if (consumer.current) consumer.current.close();
      if (producerTransport.current) producerTransport.current.close();
      if (consumerTransport.current) consumerTransport.current.close();
      if (ws.current) ws.current.close();
      if (localVideoRef.current && localVideoRef.current.srcObject) {
        (localVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      }
      if (remoteVideoRef.current && remoteVideoRef.current.srcObject) {
        (remoteVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const connectToMediasoup = async () => {
    setIsConnecting(true);

    // 1. Fetch JWT from Next.js API
    let token = null;
    try {
      const response = await fetch("/api/mediasoup-token");
      if (response.ok) {
        const data = await response.json();
        token = data.token;
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to get Mediasoup token.");
        setIsConnecting(false);
        return;
      }
    } catch (error) {
      console.error("Error fetching Mediasoup token:", error);
      toast.error("An unexpected error occurred while fetching Mediasoup token.");
      setIsConnecting(false);
      return;
    }

    ws.current = new WebSocket(MEDIASOUP_SERVER_URL);

    ws.current.onopen = async () => {
      toast.success('Connected to Mediasoup server. Authenticating...');
      // 2. Send JWT for authentication
      ws.current?.send(JSON.stringify({ type: 'authenticate', token }));
    };

    ws.current.onmessage = async (event) => {
      const msg = JSON.parse(event.data as string);

      switch (msg.type) {
        case 'authenticated':
          if (msg.success) {
            toast.success('Authenticated with Mediasoup server.');
            setIsConnecting(false);
            // Now get router RTP capabilities
            ws.current?.send(JSON.stringify({ type: 'getRouterRtpCapabilities' }));
          } else {
            toast.error(msg.error || 'Authentication failed.');
            ws.current?.close();
            setIsConnecting(false);
          }
          break;

        case 'routerRtpCapabilities':
          // 2. Load Mediasoup Device
          if (!device.current) {
            device.current = new mediasoupClient.Device();
          }
          try {
            await device.current.load({ routerRtpCapabilities: msg.data });
            toast.info('Mediasoup device loaded.');
          } catch (error: any) {
            if (error.name === 'UnsupportedError') {
              toast.error('Browser not supported for Mediasoup.');
            } else {
              console.error(error);
              toast.error('Failed to load Mediasoup device.');
            }
          }
          break;

        case 'webRtcTransportCreated':
          const { id, iceParameters, iceCandidates, dtlsParameters, sctpParameters, producing, consuming } = msg.data;
          const transport = device.current?.createTransport({
            id,
            iceParameters,
            iceCandidates,
            dtlsParameters,
            sctpParameters,
          });

          if (producing) {
            producerTransport.current = transport;
            producerTransport.current?.on('connect', ({ dtlsParameters }, callback, errback) => {
              ws.current?.send(JSON.stringify({ type: 'connectWebRtcTransport', data: { transportId: producerTransport.current?.id, dtlsParameters } }));
              callback();
            });
            producerTransport.current?.on('produce', ({ kind, rtpParameters, appData }, callback, errback) => {
              ws.current?.send(JSON.stringify({ type: 'produce', data: { transportId: producerTransport.current?.id, kind, rtpParameters, appData } }));
              callback({ id: 'someProducerId' }); // Server will send actual producer ID
            });
          }

          if (consuming) {
            consumerTransport.current = transport;
            consumerTransport.current?.on('connect', ({ dtlsParameters }, callback, errback) => {
              ws.current?.send(JSON.stringify({ type: 'connectWebRtcTransport', data: { transportId: consumerTransport.current?.id, dtlsParameters } }));
              callback();
            });
          }
          break;

        case 'webRtcTransportConnected':
          // Transport connected, can now produce/consume
          break;

        case 'producerCreated':
          producer.current = msg.data;
          toast.success('Producer created on Mediasoup server.');
          break;

        case 'consumerCreated':
          if (msg.data) {
            const { id: consumerId, producerId, kind, rtpParameters, type, sctpParameters, paused } = msg.data;
            consumer.current = await consumerTransport.current?.consume({
              id: consumerId,
              producerId,
              kind,
              rtpParameters,
              appData: { type },
            });

            if (remoteVideoRef.current) {
              remoteVideoRef.current.srcObject = new MediaStream([consumer.current!.track]);
            }
            if (paused) {
              ws.current?.send(JSON.stringify({ type: 'resumeConsumer', data: { consumerId: consumer.current?.id } }));
            }
            toast.success('Consumer created and remote stream received.');
          } else {
            toast.error('Failed to create consumer.');
          }
          break;

        case 'consumerResumed':
          toast.info('Consumer resumed.');
          break;

        default:
          console.warn('Unknown message type from mediasoup server:', msg.type);
      }
    };

    ws.current.onclose = () => {
      toast.info('Disconnected from Mediasoup server.');
      setIsConnecting(false);
      setIsStreaming(false);
    };

    ws.current.onerror = (err) => {
      toast.error('Mediasoup connection error.');
      console.error('Mediasoup WebSocket error:', err);
      setIsConnecting(false);
      setIsStreaming(false);
    };
  };

  const startStreaming = async () => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
      toast.error('Not connected to Mediasoup server. Please connect first.');
      return;
    }
    if (!device.current?.loaded) {
      toast.error('Mediasoup device not loaded. Please connect and wait.');
      return;
    }

    try {
      const localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = localStream;
      }
      setIsStreaming(true);

      // Create producer transport
      ws.current?.send(JSON.stringify({ type: 'createWebRtcTransport', data: { producing: true } }));

      // Create consumer transport
      ws.current?.send(JSON.stringify({ type: 'createWebRtcTransport', data: { consuming: true } }));

      // Wait for transports to be created and connected (simplified, in real app use promises/callbacks)
      setTimeout(async () => {
        if (producerTransport.current && localStream) {
          const track = localStream.getVideoTracks()[0] || localStream.getAudioTracks()[0];
          if (track) {
            producer.current = await producerTransport.current.produce({
              track,
              encodings: [
                { rid: 'r0', maxBitrate: 100000, scaleResolutionDownBy: 4 },
                { rid: 'r1', maxBitrate: 300000, scaleResolutionDownBy: 2 },
                { rid: 'r2', maxBitrate: 900000, scaleResolutionDownBy: 1 },
              ],
              codecOptions: {
                videoGoogleStartBitrate: 1000,
              },
            });
            toast.success('Producing local stream.');
          }
        }

        if (consumerTransport.current && device.current?.rtpCapabilities && producer.current) {
          ws.current?.send(JSON.stringify({ type: 'consume', data: { rtpCapabilities: device.current?.rtpCapabilities } }));
        }
      }, 2000); // Give some time for transports to be created

    } catch (err) {
      console.error('Error accessing media devices or producing:', err);
      toast.error('Failed to start streaming.');
      setIsStreaming(false);
    }
  };

  const stopStreaming = () => {
    if (producer.current) producer.current.close();
    if (consumer.current) consumer.current.close();
    if (producerTransport.current) producerTransport.current.close();
    if (consumerTransport.current) consumerTransport.current.close();
    if (ws.current) ws.current.close();

    if (localVideoRef.current && localVideoRef.current.srcObject) {
      (localVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      localVideoRef.current.srcObject = null;
    }
    if (remoteVideoRef.current && remoteVideoRef.current.srcObject) {
      (remoteVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      remoteVideoRef.current.srcObject = null;
    }

    setIsStreaming(false);
    setProducer(null);
    setConsumer(null);
    setProducerTransport(null);
    setConsumerTransport(null);
    device.current = null;
    ws.current = null;
    toast.info('Streaming stopped.');
  };

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Live Streaming</h2>
      <div className="flex space-x-4 mb-4">
        <Button onClick={connectToMediasoup} disabled={isConnecting || (ws.current && ws.current.readyState === WebSocket.OPEN)}>
          {isConnecting ? 'Connecting...' : 'Connect to Mediasoup'}
        </Button>
        <Button onClick={startStreaming} disabled={isStreaming || !ws.current || ws.current.readyState !== WebSocket.OPEN || !device.current?.loaded}>
          Start Stream
        </Button>
        <Button onClick={stopStreaming} disabled={!isStreaming}>
          Stop Stream
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="text-xl font-semibold mb-2">Local Stream</h3>
          <video ref={localVideoRef} autoPlay muted className="w-full border rounded-md"></video>
        </div>
        <div>
          <h3 className="text-xl font-semibold mb-2">Remote Stream</h3>
          <video ref={remoteVideoRef} autoPlay className="w-full border rounded-md"></video>
        </div>
      </div>
    </div>
  );
}
