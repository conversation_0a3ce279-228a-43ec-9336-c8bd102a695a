"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { AudioPlayer as AudioPlayerType, MediaSource, PlayerConfig } from '@/lib/player/types'
import { createAudioPlayer } from '@/lib/player'
import { AudioPlayerControls } from './audio-player-controls'
import { AudioVisualizer } from './audio-visualizer'
import { AudioPlayerSettings } from './audio-player-settings'
import { Loader2, Music } from 'lucide-react'

interface AudioPlayerProps {
  source?: MediaSource
  config?: Partial<PlayerConfig>
  className?: string
  onPlayerReady?: (player: AudioPlayerType) => void
  onStateChange?: (state: any) => void
  onTimeUpdate?: (currentTime: number) => void
  onError?: (error: any) => void
  showControls?: boolean
  showVisualizer?: boolean
  autoPlay?: boolean
  loop?: boolean
  variant?: 'default' | 'compact' | 'minimal' | 'card'
  artwork?: string
  title?: string
  artist?: string
  album?: string
}

export function AudioPlayer({
  source,
  config = {},
  className,
  onPlayerReady,
  onStateChange,
  onTimeUpdate,
  onError,
  showControls = true,
  showVisualizer = true,
  autoPlay = false,
  loop = false,
  variant = 'default',
  artwork,
  title,
  artist,
  album
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [player, setPlayer] = useState<AudioPlayerType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [frequencyData, setFrequencyData] = useState<Uint8Array | null>(null)

  // Initialize player
  useEffect(() => {
    if (!audioRef.current) return

    const playerConfig: Partial<PlayerConfig> = {
      autoPlay,
      loop,
      controls: false, // We use custom controls
      ...config
    }

    try {
      const audioPlayer = createAudioPlayer(audioRef.current, playerConfig)
      setPlayer(audioPlayer)

      // Setup event listeners
      audioPlayer.on('onStateChange', (state) => {
        setIsLoading(state.state === 'loading' || state.state === 'buffering')
        onStateChange?.(state)
      })

      audioPlayer.on('onTimeUpdate', (currentTime) => {
        // Update frequency data for visualizer
        if (showVisualizer) {
          const data = audioPlayer.getFrequencyData()
          setFrequencyData(data)
        }
        onTimeUpdate?.(currentTime)
      })

      audioPlayer.on('onError', (error) => {
        setError(error.message)
        onError?.(error)
      })

      onPlayerReady?.(audioPlayer)

      return () => {
        audioPlayer.destroy()
      }
    } catch (err) {
      setError('Failed to initialize audio player')
      console.error('Audio player initialization error:', err)
    }
  }, [autoPlay, loop, config, onPlayerReady, onStateChange, onTimeUpdate, onError, showVisualizer])

  // Load source when it changes
  useEffect(() => {
    if (player && source) {
      setIsLoading(true)
      setError(null)
      
      player.load(source).catch((err) => {
        setError('Failed to load audio source')
        console.error('Audio load error:', err)
      })
    }
  }, [player, source])

  const getVariantClasses = () => {
    switch (variant) {
      case 'compact':
        return 'p-4 bg-gray-900/50 backdrop-blur-sm rounded-lg border border-white/10'
      case 'minimal':
        return 'p-2 bg-transparent'
      case 'card':
        return 'p-6 bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm rounded-xl border border-white/10'
      default:
        return 'p-6 bg-gray-900/80 backdrop-blur-md rounded-lg border border-white/20'
    }
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden',
        getVariantClasses(),
        className
      )}
    >
      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        className="hidden"
        preload="metadata"
      />

      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg z-10">
          <div className="text-center text-white">
            <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
            <p className="text-sm">Loading...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 rounded-lg z-10">
          <div className="text-center text-white">
            <div className="text-lg font-semibold mb-2">Audio Error</div>
            <div className="text-sm opacity-80">{error}</div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {!error && (
        <div className="space-y-4">
          {/* Track Info and Artwork */}
          {variant !== 'minimal' && (
            <div className="flex items-center space-x-4">
              {/* Artwork */}
              <div className="relative flex-shrink-0">
                {artwork ? (
                  <img
                    src={artwork}
                    alt={title || 'Track artwork'}
                    className={cn(
                      'object-cover rounded-lg',
                      variant === 'compact' ? 'w-12 h-12' : 'w-16 h-16'
                    )}
                  />
                ) : (
                  <div className={cn(
                    'bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center',
                    variant === 'compact' ? 'w-12 h-12' : 'w-16 h-16'
                  )}>
                    <Music className={cn(
                      'text-white',
                      variant === 'compact' ? 'w-6 h-6' : 'w-8 h-8'
                    )} />
                  </div>
                )}

                {/* Playing Indicator */}
                {player && player.getState().state === 'playing' && (
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                    <div className="flex space-x-1">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="w-1 bg-white rounded-full animate-pulse"
                          style={{
                            height: '12px',
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: '1s'
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                {title && (
                  <h3 className={cn(
                    'text-white font-semibold truncate',
                    variant === 'compact' ? 'text-sm' : 'text-base'
                  )}>
                    {title}
                  </h3>
                )}
                {artist && (
                  <p className={cn(
                    'text-white/70 truncate',
                    variant === 'compact' ? 'text-xs' : 'text-sm'
                  )}>
                    {artist}
                  </p>
                )}
                {album && variant !== 'compact' && (
                  <p className="text-white/50 text-xs truncate">
                    {album}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Visualizer */}
          {showVisualizer && frequencyData && variant !== 'minimal' && (
            <AudioVisualizer
              frequencyData={frequencyData}
              isPlaying={player?.getState().state === 'playing'}
              className={variant === 'compact' ? 'h-16' : 'h-24'}
            />
          )}

          {/* Controls */}
          {showControls && player && (
            <AudioPlayerControls
              player={player}
              variant={variant}
              onSettingsClick={() => setShowSettings(true)}
            />
          )}
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && player && (
        <AudioPlayerSettings
          player={player}
          onClose={() => setShowSettings(false)}
        />
      )}
    </div>
  )
}

// Audio player variants
export function CompactAudioPlayer(props: AudioPlayerProps) {
  return (
    <AudioPlayer
      {...props}
      variant="compact"
      showVisualizer={false}
    />
  )
}

export function MinimalAudioPlayer(props: AudioPlayerProps) {
  return (
    <AudioPlayer
      {...props}
      variant="minimal"
      showVisualizer={false}
    />
  )
}

export function CardAudioPlayer(props: AudioPlayerProps) {
  return (
    <AudioPlayer
      {...props}
      variant="card"
      showVisualizer={true}
    />
  )
}

// Floating audio player for global playback
export function FloatingAudioPlayer(props: AudioPlayerProps) {
  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <AudioPlayer
        {...props}
        variant="card"
        className="shadow-2xl"
      />
    </div>
  )
}
