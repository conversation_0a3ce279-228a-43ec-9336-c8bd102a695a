"use client"

import React, { useRef, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface AudioVisualizerProps {
  frequencyData: Uint8Array
  isPlaying: boolean
  type?: 'bars' | 'wave' | 'circle' | 'spectrum'
  color?: string
  gradient?: string[]
  sensitivity?: number
  smoothing?: number
  barCount?: number
  className?: string
}

export function AudioVisualizer({
  frequencyData,
  isPlaying,
  type = 'bars',
  color = '#8B5CF6',
  gradient = ['#8B5CF6', '#EC4899'],
  sensitivity = 1,
  smoothing = 0.8,
  barCount = 64,
  className
}: AudioVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const previousDataRef = useRef<number[]>([])

  const draw = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { width, height } = canvas
    ctx.clearRect(0, 0, width, height)

    if (!isPlaying || !frequencyData || frequencyData.length === 0) {
      // Draw idle state
      drawIdleState(ctx, width, height, type, gradient)
      return
    }

    // Apply smoothing
    const smoothedData = applySmoothingToData(frequencyData, previousDataRef.current, smoothing)
    previousDataRef.current = smoothedData

    // Draw based on type
    switch (type) {
      case 'bars':
        drawBars(ctx, smoothedData, width, height, barCount, gradient, sensitivity)
        break
      case 'wave':
        drawWave(ctx, smoothedData, width, height, gradient, sensitivity)
        break
      case 'circle':
        drawCircle(ctx, smoothedData, width, height, gradient, sensitivity)
        break
      case 'spectrum':
        drawSpectrum(ctx, smoothedData, width, height, gradient, sensitivity)
        break
    }
  }, [frequencyData, isPlaying, type, gradient, sensitivity, smoothing, barCount])

  useEffect(() => {
    const animate = () => {
      draw()
      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [draw])

  // Resize canvas to match container
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio
      
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
      }
    }

    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
    
    return () => window.removeEventListener('resize', resizeCanvas)
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className={cn('w-full h-full', className)}
      style={{ width: '100%', height: '100%' }}
    />
  )
}

// Helper functions for different visualizer types
function applySmoothingToData(
  currentData: Uint8Array, 
  previousData: number[], 
  smoothing: number
): number[] {
  const smoothedData: number[] = []
  
  for (let i = 0; i < currentData.length; i++) {
    const current = currentData[i] / 255
    const previous = previousData[i] || 0
    smoothedData[i] = previous * smoothing + current * (1 - smoothing)
  }
  
  return smoothedData
}

function createGradient(
  ctx: CanvasRenderingContext2D,
  x1: number,
  y1: number,
  x2: number,
  y2: number,
  colors: string[]
): CanvasGradient {
  const gradient = ctx.createLinearGradient(x1, y1, x2, y2)
  
  colors.forEach((color, index) => {
    gradient.addColorStop(index / (colors.length - 1), color)
  })
  
  return gradient
}

function drawIdleState(
  ctx: CanvasRenderingContext2D,
  width: number,
  height: number,
  type: string,
  colors: string[]
) {
  ctx.globalAlpha = 0.3
  
  switch (type) {
    case 'bars':
      const barWidth = width / 32
      for (let i = 0; i < 32; i++) {
        const x = i * barWidth
        const barHeight = Math.random() * height * 0.1 + 2
        ctx.fillStyle = colors[0]
        ctx.fillRect(x, height - barHeight, barWidth - 1, barHeight)
      }
      break
      
    case 'wave':
      ctx.strokeStyle = colors[0]
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(0, height / 2)
      for (let x = 0; x < width; x += 4) {
        const y = height / 2 + Math.sin(x * 0.02) * 10
        ctx.lineTo(x, y)
      }
      ctx.stroke()
      break
      
    default:
      // Simple pulse for other types
      const centerX = width / 2
      const centerY = height / 2
      const radius = Math.min(width, height) * 0.1
      ctx.fillStyle = colors[0]
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)
      ctx.fill()
  }
  
  ctx.globalAlpha = 1
}

function drawBars(
  ctx: CanvasRenderingContext2D,
  data: number[],
  width: number,
  height: number,
  barCount: number,
  colors: string[],
  sensitivity: number
) {
  const barWidth = width / barCount
  const gradient = createGradient(ctx, 0, height, 0, 0, colors)
  
  for (let i = 0; i < barCount; i++) {
    const dataIndex = Math.floor((i / barCount) * data.length)
    const value = data[dataIndex] || 0
    const barHeight = value * height * sensitivity
    
    const x = i * barWidth
    const y = height - barHeight
    
    ctx.fillStyle = gradient
    ctx.fillRect(x, y, barWidth - 1, barHeight)
  }
}

function drawWave(
  ctx: CanvasRenderingContext2D,
  data: number[],
  width: number,
  height: number,
  colors: string[],
  sensitivity: number
) {
  const gradient = createGradient(ctx, 0, 0, width, 0, colors)
  ctx.strokeStyle = gradient
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  
  ctx.beginPath()
  
  for (let i = 0; i < width; i++) {
    const dataIndex = Math.floor((i / width) * data.length)
    const value = data[dataIndex] || 0
    const y = height / 2 + (value - 0.5) * height * sensitivity
    
    if (i === 0) {
      ctx.moveTo(i, y)
    } else {
      ctx.lineTo(i, y)
    }
  }
  
  ctx.stroke()
}

function drawCircle(
  ctx: CanvasRenderingContext2D,
  data: number[],
  width: number,
  height: number,
  colors: string[],
  sensitivity: number
) {
  const centerX = width / 2
  const centerY = height / 2
  const baseRadius = Math.min(width, height) * 0.2
  const maxRadius = Math.min(width, height) * 0.4
  
  const gradient = createGradient(ctx, centerX - maxRadius, centerY - maxRadius, centerX + maxRadius, centerY + maxRadius, colors)
  
  ctx.strokeStyle = gradient
  ctx.lineWidth = 2
  
  const angleStep = (Math.PI * 2) / data.length
  
  ctx.beginPath()
  
  for (let i = 0; i < data.length; i++) {
    const angle = i * angleStep
    const value = data[i] || 0
    const radius = baseRadius + value * (maxRadius - baseRadius) * sensitivity
    
    const x = centerX + Math.cos(angle) * radius
    const y = centerY + Math.sin(angle) * radius
    
    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  }
  
  ctx.closePath()
  ctx.stroke()
}

function drawSpectrum(
  ctx: CanvasRenderingContext2D,
  data: number[],
  width: number,
  height: number,
  colors: string[],
  sensitivity: number
) {
  const imageData = ctx.createImageData(width, height)
  const pixels = imageData.data
  
  for (let x = 0; x < width; x++) {
    const dataIndex = Math.floor((x / width) * data.length)
    const value = data[dataIndex] || 0
    const intensity = value * sensitivity
    
    for (let y = 0; y < height; y++) {
      const pixelIndex = (y * width + x) * 4
      const normalizedY = y / height
      
      if (normalizedY > (1 - intensity)) {
        // Color based on frequency (x position) and intensity
        const hue = (x / width) * 360
        const saturation = 100
        const lightness = intensity * 70 + 30
        
        const rgb = hslToRgb(hue, saturation, lightness)
        
        pixels[pixelIndex] = rgb.r     // Red
        pixels[pixelIndex + 1] = rgb.g // Green
        pixels[pixelIndex + 2] = rgb.b // Blue
        pixels[pixelIndex + 3] = 255   // Alpha
      } else {
        pixels[pixelIndex + 3] = 0 // Transparent
      }
    }
  }
  
  ctx.putImageData(imageData, 0, 0)
}

function hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
  h /= 360
  s /= 100
  l /= 100
  
  const c = (1 - Math.abs(2 * l - 1)) * s
  const x = c * (1 - Math.abs((h * 6) % 2 - 1))
  const m = l - c / 2
  
  let r = 0, g = 0, b = 0
  
  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0
  } else if (1/6 <= h && h < 2/6) {
    r = x; g = c; b = 0
  } else if (2/6 <= h && h < 3/6) {
    r = 0; g = c; b = x
  } else if (3/6 <= h && h < 4/6) {
    r = 0; g = x; b = c
  } else if (4/6 <= h && h < 5/6) {
    r = x; g = 0; b = c
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x
  }
  
  return {
    r: Math.round((r + m) * 255),
    g: Math.round((g + m) * 255),
    b: Math.round((b + m) * 255)
  }
}

// Preset visualizer components
export function BarsVisualizer(props: Omit<AudioVisualizerProps, 'type'>) {
  return <AudioVisualizer {...props} type="bars" />
}

export function WaveVisualizer(props: Omit<AudioVisualizerProps, 'type'>) {
  return <AudioVisualizer {...props} type="wave" />
}

export function CircleVisualizer(props: Omit<AudioVisualizerProps, 'type'>) {
  return <AudioVisualizer {...props} type="circle" />
}

export function SpectrumVisualizer(props: Omit<AudioVisualizerProps, 'type'>) {
  return <AudioVisualizer {...props} type="spectrum" />
}
