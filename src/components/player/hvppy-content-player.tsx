"use client"

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer } from './video-player'
import { AudioPlayer } from './audio-player'
import { useHVPPYVideoPlayer } from '@/hooks/player/use-video-player'
import { useHVPPYAudioPlayer } from '@/hooks/player/use-audio-player'
import { Loader2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface HVPPYContentPlayerProps {
  contentId: string
  className?: string
  autoPlay?: boolean
  showControls?: boolean
  variant?: 'default' | 'compact' | 'minimal' | 'card'
  onPlayerReady?: (player: any) => void
  onError?: (error: string) => void
}

export function HVPPYContentPlayer({
  contentId,
  className,
  autoPlay = false,
  showControls = true,
  variant = 'default',
  onPlayerReady,
  onError
}: HVPPYContentPlayerProps) {
  const [contentType, setContentType] = useState<'video' | 'audio' | null>(null)
  const [playerElement, setPlayerElement] = useState<HTMLVideoElement | HTMLAudioElement | null>(null)

  // Load content metadata to determine type
  useEffect(() => {
    const loadContentMetadata = async () => {
      try {
        const response = await fetch(`/api/player/content/${contentId}`)
        if (!response.ok) throw new Error('Failed to load content')
        
        const content = await response.json()
        const type = content.source?.type === 'audio' ? 'audio' : 'video'
        setContentType(type)
      } catch (error) {
        console.error('Failed to load content metadata:', error)
        onError?.('Failed to load content')
      }
    }

    loadContentMetadata()
  }, [contentId, onError])

  // Video player hook
  const videoPlayer = useHVPPYVideoPlayer(contentType === 'video' ? contentId : undefined)
  
  // Audio player hook  
  const audioPlayer = useHVPPYAudioPlayer(contentType === 'audio' ? contentId : undefined)

  // Handle player ready
  useEffect(() => {
    const player = contentType === 'video' ? videoPlayer.player : audioPlayer.player
    if (player) {
      onPlayerReady?.(player)
    }
  }, [contentType, videoPlayer.player, audioPlayer.player, onPlayerReady])

  // Handle errors
  useEffect(() => {
    const error = contentType === 'video' ? videoPlayer.error : audioPlayer.error
    if (error) {
      onError?.(error)
    }
  }, [contentType, videoPlayer.error, audioPlayer.error, onError])

  // Loading state
  if (!contentType || (contentType === 'video' ? videoPlayer.loading : audioPlayer.loading)) {
    return (
      <div className={cn(
        'flex items-center justify-center p-8 bg-gray-900/50 rounded-lg',
        className
      )}>
        <div className="text-center text-white">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm">Loading content...</p>
        </div>
      </div>
    )
  }

  // Error state
  const error = contentType === 'video' ? videoPlayer.error : audioPlayer.error
  if (error) {
    return (
      <div className={cn(
        'flex items-center justify-center p-8 bg-gray-900/50 rounded-lg',
        className
      )}>
        <div className="text-center text-white">
          <AlertCircle className="w-8 h-8 text-red-400 mx-auto mb-2" />
          <p className="text-sm font-semibold mb-2">Failed to Load Content</p>
          <p className="text-xs opacity-70 mb-4">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/20 hover:bg-white/10"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // Get metadata
  const metadata = contentType === 'video' ? videoPlayer.metadata : audioPlayer.metadata

  // Render appropriate player
  if (contentType === 'video') {
    return (
      <VideoPlayer
        ref={(el) => setPlayerElement(el)}
        source={videoPlayer.source}
        className={className}
        autoPlay={autoPlay}
        showControls={showControls}
        showOverlay={true}
        title={metadata?.title}
        creator={metadata?.artist}
        description={metadata?.description}
        tags={metadata?.tags}
        moods={metadata?.moods}
        poster={metadata?.thumbnail}
        aspectRatio={variant === 'compact' ? '16:9' : 'auto'}
        onPlayerReady={onPlayerReady}
        onError={onError}
      />
    )
  } else {
    return (
      <AudioPlayer
        ref={(el) => setPlayerElement(el)}
        source={audioPlayer.source}
        className={className}
        autoPlay={autoPlay}
        showControls={showControls}
        showVisualizer={variant !== 'minimal'}
        variant={variant}
        title={metadata?.title}
        artist={metadata?.artist}
        album={metadata?.album}
        artwork={metadata?.artwork}
        onPlayerReady={onPlayerReady}
        onError={onError}
      />
    )
  }
}

// Specialized variants for different use cases
export function HVPPYFeedPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="default"
      autoPlay={true}
      showControls={true}
      {...props}
    />
  )
}

export function HVPPYCompactPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="compact"
      autoPlay={false}
      showControls={true}
      {...props}
    />
  )
}

export function HVPPYMinimalPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="minimal"
      autoPlay={false}
      showControls={false}
      {...props}
    />
  )
}

export function HVPPYCardPlayer({ contentId, ...props }: Omit<HVPPYContentPlayerProps, 'variant'>) {
  return (
    <HVPPYContentPlayer
      contentId={contentId}
      variant="card"
      autoPlay={false}
      showControls={true}
      {...props}
    />
  )
}
