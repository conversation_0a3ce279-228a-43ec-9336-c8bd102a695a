// HVPPY Central Player Components - Main Export File

// Core player components
export { VideoPlayer, VerticalVideoPlayer, CompactVideoPlayer, FullscreenVideoPlayer } from './video-player'
export { AudioPlayer, CompactAudioPlayer, MinimalAudioPlayer, CardAudioPlayer, FloatingAudioPlayer } from './audio-player'

// Player controls
export { VideoPlayerControls, MinimalVideoControls } from './video-player-controls'
export { AudioPlayerControls } from './audio-player-controls'

// Player overlays and UI
export { VideoPlayerOverlay, MinimalVideoOverlay } from './video-player-overlay'
export { VideoPlayerSettings } from './video-player-settings'
export { AudioPlayerSettings } from './audio-player-settings'

// Visualizer components
export { 
  AudioVisualizer, 
  BarsVisualizer, 
  WaveVisualizer, 
  CircleVisualizer, 
  SpectrumVisualizer 
} from './audio-visualizer'

// Integration components (to be implemented)
export { HVPPYContentPlayer } from './hvppy-content-player'
export { FeedVideoPlayer } from './feed-video-player'
export { PlaylistPlayer } from './playlist-player'
export { MoodPlayer } from './mood-player'

// Player hooks
export { useVideoPlayer, useHVPPYVideoPlayer, useVerticalVideoPlayer } from '@/hooks/player/use-video-player'
export { useAudioPlayer, useHVPPYAudioPlayer, useAudioPlayerWithMoodEQ } from '@/hooks/player/use-audio-player'
export { usePlaylist, useHVPPYPlaylist, useMoodPlaylist } from '@/hooks/player/use-playlist'

// Player store
export { usePlayerStore } from '@/lib/stores/enhanced-player-store'

// Player library core
export * from '@/lib/player'

// Player utilities
export { formatTime, createMediaSource, getQualityLabel } from '@/lib/player/utils'

// Types
export type {
  VideoPlayer as VideoPlayerType,
  AudioPlayer as AudioPlayerType,
  MediaSource,
  PlayerConfig,
  PlayerState,
  PlaybackQuality,
  AudioQuality,
  UseVideoPlayerReturn,
  UseAudioPlayerReturn
} from '@/lib/player/types'
