"use client"

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { HVPPYContentPlayer } from './hvppy-content-player'
import { useMoodPlaylist } from '@/hooks/player/use-playlist'
import { useAudioPlayerWithMoodEQ } from '@/hooks/player/use-audio-player'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Shuffle, 
  Heart,
  Sparkles,
  RefreshCw
} from 'lucide-react'

interface MoodPlayerProps {
  mood: string
  className?: string
  variant?: 'default' | 'compact' | 'card'
  autoPlay?: boolean
  showMoodInfo?: boolean
  enableMoodEQ?: boolean
}

export function MoodPlayer({
  mood,
  className,
  variant = 'default',
  autoPlay = false,
  showMoodInfo = true,
  enableMoodEQ = true
}: MoodPlayerProps) {
  const [currentPlayer, setCurrentPlayer] = useState<any>(null)
  const [moodInfo, setMoodInfo] = useState<any>(null)
  const [isLiked, setIsLiked] = useState(false)
  
  const {
    playlist,
    currentItem,
    currentIndex,
    isPlaying,
    canGoNext,
    canGoPrevious,
    loadMoodPlaylist,
    next,
    previous,
    shuffle,
    loading,
    error
  } = useMoodPlaylist()

  // Load mood playlist on mount
  useEffect(() => {
    if (mood) {
      loadMoodPlaylist(mood)
      loadMoodInfo()
    }
  }, [mood, loadMoodPlaylist])

  // Load mood information
  const loadMoodInfo = async () => {
    try {
      const response = await fetch(`/api/player/content/mood/${mood}`)
      if (response.ok) {
        const data = await response.json()
        setMoodInfo(data.moodInfo)
      }
    } catch (error) {
      console.error('Failed to load mood info:', error)
    }
  }

  // Apply mood-based EQ if enabled
  const audioPlayerHook = useAudioPlayerWithMoodEQ(
    enableMoodEQ ? mood : undefined,
    {
      enableVisualizer: true
    }
  )

  if (loading) {
    return (
      <div className={cn('p-8 text-center text-white', className)}>
        <div className="flex items-center justify-center space-x-2">
          <RefreshCw className="w-5 h-5 animate-spin" />
          <p>Loading {mood} mood...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('p-8 text-center text-white', className)}>
        <p className="text-red-400">Failed to load {mood} content</p>
        <p className="text-sm text-white/70 mt-2">{error}</p>
      </div>
    )
  }

  if (!playlist || playlist.items.length === 0) {
    return (
      <div className={cn('p-8 text-center text-white', className)}>
        <p>No content found for {mood} mood</p>
      </div>
    )
  }

  const currentContentId = currentItem?.contentId || playlist.items[currentIndex]?.contentId

  return (
    <div className={cn('relative', className)}>
      {/* Mood Header */}
      {showMoodInfo && moodInfo && (
        <div className="p-4 bg-gradient-to-r from-purple-900/20 to-pink-900/20 backdrop-blur-sm border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: moodInfo.color || '#8B5CF6' }}
              />
              <div>
                <h2 className="text-white font-semibold capitalize text-lg">
                  {mood} Mood
                </h2>
                <p className="text-white/70 text-sm">
                  {moodInfo.description}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-white/10 text-white">
                {playlist.items.length} tracks
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={cn(
                  "text-white hover:bg-white/20 p-2",
                  isLiked && "text-red-400"
                )}
              >
                <Heart className={cn("w-4 h-4", isLiked && "fill-current")} />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Player */}
      <div className="relative">
        {currentContentId && (
          <HVPPYContentPlayer
            contentId={currentContentId}
            variant={variant}
            autoPlay={autoPlay}
            onPlayerReady={setCurrentPlayer}
          />
        )}

        {/* Mood Player Controls */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <div className="space-y-3">
            {/* Current Track Info */}
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-semibold truncate">
                  {currentItem?.metadata?.title || 'Unknown Track'}
                </h3>
                <div className="flex items-center space-x-2">
                  <p className="text-white/70 text-sm">
                    {currentItem?.metadata?.artist || 'HVPPY'}
                  </p>
                  <span className="text-white/50">•</span>
                  <p className="text-white/70 text-sm">
                    {currentIndex + 1} of {playlist.items.length}
                  </p>
                </div>
              </div>

              {/* Mood indicator */}
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4 text-purple-400" />
                <span className="text-purple-400 text-sm font-medium capitalize">
                  {mood}
                </span>
              </div>
            </div>

            {/* Playback Controls */}
            <div className="flex items-center justify-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={shuffle}
                className="text-white hover:bg-white/20 p-2"
                title="Shuffle mood playlist"
              >
                <Shuffle className="w-4 h-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={previous}
                disabled={!canGoPrevious}
                className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
              >
                <SkipBack className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="lg"
                onClick={() => isPlaying ? currentPlayer?.pause() : currentPlayer?.play()}
                className="text-white hover:bg-white/20 p-3 rounded-full bg-white/10"
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={next}
                disabled={!canGoNext}
                className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
              >
                <SkipForward className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => loadMoodPlaylist(mood)}
                className="text-white hover:bg-white/20 p-2"
                title="Refresh mood content"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>

            {/* Mood Tags */}
            {currentItem?.metadata?.moods && (
              <div className="flex flex-wrap gap-2">
                {currentItem.metadata.moods.slice(0, 4).map((itemMood: string) => (
                  <Badge
                    key={itemMood}
                    variant="secondary"
                    className={cn(
                      "text-xs",
                      itemMood === mood 
                        ? "bg-purple-500/30 text-purple-200 border-purple-400/30" 
                        : "bg-white/10 text-white/70 border-white/20"
                    )}
                  >
                    {itemMood}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* EQ Indicator */}
        {enableMoodEQ && (
          <div className="absolute top-4 right-4 px-2 py-1 bg-purple-500/20 text-purple-200 text-xs rounded-full">
            {mood} EQ
          </div>
        )}
      </div>
    </div>
  )
}

// Compact mood player for smaller spaces
export function CompactMoodPlayer(props: Omit<MoodPlayerProps, 'variant'>) {
  return (
    <MoodPlayer
      {...props}
      variant="compact"
      showMoodInfo={false}
    />
  )
}

// Card-style mood player
export function MoodPlayerCard(props: Omit<MoodPlayerProps, 'variant'>) {
  return (
    <div className="rounded-xl overflow-hidden border border-white/10 bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm">
      <MoodPlayer
        {...props}
        variant="card"
      />
    </div>
  )
}
