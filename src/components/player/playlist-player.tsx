"use client"

import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { HVPPYContentPlayer } from './hvppy-content-player'
import { usePlaylist } from '@/hooks/player/use-playlist'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Shuffle, 
  Repeat,
  List,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { formatTime } from '@/lib/player/utils'

interface PlaylistPlayerProps {
  playlistId: string
  className?: string
  showPlaylist?: boolean
  variant?: 'default' | 'compact' | 'minimal'
  autoPlay?: boolean
}

export function PlaylistPlayer({
  playlistId,
  className,
  showPlaylist = true,
  variant = 'default',
  autoPlay = false
}: PlaylistPlayerProps) {
  const [showPlaylistPanel, setShowPlaylistPanel] = useState(false)
  const [currentPlayer, setCurrentPlayer] = useState<any>(null)
  
  const {
    playlist,
    currentItem,
    currentIndex,
    isPlaying,
    canGoNext,
    canGoPrevious,
    loadPlaylist,
    play,
    next,
    previous,
    goToIndex,
    toggleShuffle,
    setRepeat
  } = usePlaylist()

  // Load playlist on mount
  useEffect(() => {
    if (playlistId) {
      loadPlaylist(playlistId)
    }
  }, [playlistId, loadPlaylist])

  // Auto-play first item if specified
  useEffect(() => {
    if (autoPlay && playlist && playlist.items.length > 0 && currentIndex === 0) {
      play(0)
    }
  }, [autoPlay, playlist, currentIndex, play])

  if (!playlist) {
    return (
      <div className={cn('p-8 text-center text-white', className)}>
        <p>Loading playlist...</p>
      </div>
    )
  }

  if (playlist.items.length === 0) {
    return (
      <div className={cn('p-8 text-center text-white', className)}>
        <p>This playlist is empty</p>
      </div>
    )
  }

  const currentContentId = currentItem?.contentId || playlist.items[currentIndex]?.contentId

  return (
    <div className={cn('relative', className)}>
      {/* Main Player */}
      <div className="relative">
        {currentContentId && (
          <HVPPYContentPlayer
            contentId={currentContentId}
            variant={variant}
            autoPlay={autoPlay}
            onPlayerReady={setCurrentPlayer}
          />
        )}

        {/* Playlist Controls Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <div className="flex items-center justify-between">
            {/* Left: Track Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-semibold truncate">
                {currentItem?.metadata?.title || 'Unknown Track'}
              </h3>
              <p className="text-white/70 text-sm truncate">
                {playlist.name} • {currentIndex + 1} of {playlist.items.length}
              </p>
            </div>

            {/* Center: Playback Controls */}
            <div className="flex items-center space-x-2 mx-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRepeat(
                  playlist.repeat === 'none' ? 'all' : 
                  playlist.repeat === 'all' ? 'one' : 'none'
                )}
                className={cn(
                  "text-white hover:bg-white/20 p-2",
                  playlist.repeat !== 'none' && "text-purple-400"
                )}
              >
                <Repeat className="w-4 h-4" />
                {playlist.repeat === 'one' && (
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-purple-400 rounded-full" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={previous}
                disabled={!canGoPrevious}
                className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
              >
                <SkipBack className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="lg"
                onClick={() => isPlaying ? currentPlayer?.pause() : currentPlayer?.play()}
                className="text-white hover:bg-white/20 p-3 rounded-full bg-white/10"
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={next}
                disabled={!canGoNext}
                className="text-white hover:bg-white/20 p-2 disabled:opacity-50"
              >
                <SkipForward className="w-5 h-5" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleShuffle}
                className={cn(
                  "text-white hover:bg-white/20 p-2",
                  playlist.shuffle && "text-purple-400"
                )}
              >
                <Shuffle className="w-4 h-4" />
              </Button>
            </div>

            {/* Right: Playlist Toggle */}
            {showPlaylist && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPlaylistPanel(!showPlaylistPanel)}
                className="text-white hover:bg-white/20 p-2"
              >
                <List className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Playlist Panel */}
      {showPlaylist && showPlaylistPanel && (
        <div className="absolute bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-md border-t border-white/10 max-h-80 overflow-hidden">
          {/* Panel Header */}
          <div className="flex items-center justify-between p-4 border-b border-white/10">
            <div>
              <h3 className="text-white font-semibold">{playlist.name}</h3>
              <p className="text-white/70 text-sm">
                {playlist.items.length} tracks • {formatTime(playlist.totalDuration || 0)}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPlaylistPanel(false)}
              className="text-white hover:bg-white/20 p-2"
            >
              <ChevronDown className="w-4 h-4" />
            </Button>
          </div>

          {/* Track List */}
          <div className="overflow-y-auto max-h-60">
            {playlist.items.map((item, index) => (
              <div
                key={item.id}
                className={cn(
                  "flex items-center p-3 hover:bg-white/5 cursor-pointer transition-colors",
                  index === currentIndex && "bg-purple-500/20"
                )}
                onClick={() => goToIndex(index)}
              >
                <div className="w-8 text-center">
                  {index === currentIndex && isPlaying ? (
                    <div className="flex space-x-1">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="w-1 bg-purple-400 rounded-full animate-pulse"
                          style={{
                            height: '12px',
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: '1s'
                          }}
                        />
                      ))}
                    </div>
                  ) : (
                    <span className="text-white/70 text-sm">{index + 1}</span>
                  )}
                </div>

                <div className="flex-1 min-w-0 ml-3">
                  <p className="text-white font-medium truncate">
                    {item.metadata?.title || 'Unknown Track'}
                  </p>
                  <p className="text-white/70 text-sm truncate">
                    {item.metadata?.artist || 'Unknown Artist'}
                  </p>
                </div>

                <div className="text-white/70 text-sm">
                  {formatTime(item.duration || 0)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Compact playlist player for smaller spaces
export function CompactPlaylistPlayer(props: Omit<PlaylistPlayerProps, 'variant'>) {
  return (
    <PlaylistPlayer
      {...props}
      variant="compact"
      showPlaylist={false}
    />
  )
}

// Minimal playlist player with basic controls
export function MinimalPlaylistPlayer(props: Omit<PlaylistPlayerProps, 'variant' | 'showPlaylist'>) {
  return (
    <PlaylistPlayer
      {...props}
      variant="minimal"
      showPlaylist={false}
    />
  )
}
