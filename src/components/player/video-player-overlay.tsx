"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer } from '@/lib/player/types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Share, 
  Heart, 
  MessageCircle, 
  Bookmark,
  MoreVertical,
  Volume2,
  VolumeX,
  Play,
  Pause
} from 'lucide-react'

interface VideoPlayerOverlayProps {
  player: VideoPlayer
  visible: boolean
  onSettingsClick?: () => void
  className?: string
  title?: string
  creator?: string
  description?: string
  tags?: string[]
  moods?: string[]
  showSocialActions?: boolean
  showInfo?: boolean
}

export function VideoPlayerOverlay({
  player,
  visible,
  onSettingsClick,
  className,
  title,
  creator,
  description,
  tags = [],
  moods = [],
  showSocialActions = true,
  showInfo = true
}: VideoPlayerOverlayProps) {
  const [playerState, setPlayerState] = useState(player.getState())
  const [isLiked, setIsLiked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    return () => player.off('onStateChange', handleStateChange)
  }, [player])

  const handleVolumeToggle = () => {
    player.setMuted(!playerState.muted)
  }

  const handleLike = () => {
    setIsLiked(!isLiked)
    // TODO: Integrate with HVPPY Central reaction system
  }

  const handleSave = () => {
    setIsSaved(!isSaved)
    // TODO: Integrate with HVPPY Central memory system
  }

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share video')
  }

  const handleComment = () => {
    // TODO: Implement comment functionality
    console.log('Open comments')
  }

  return (
    <div
      className={cn(
        'absolute inset-0 pointer-events-none transition-opacity duration-300',
        visible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      {/* Top Overlay - Video Info */}
      {showInfo && (title || creator) && (
        <div className="absolute top-0 left-0 right-0 p-4 bg-gradient-to-b from-black/60 to-transparent pointer-events-auto">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {title && (
                <h3 className="text-white font-semibold text-lg truncate mb-1">
                  {title}
                </h3>
              )}
              {creator && (
                <p className="text-white/80 text-sm truncate">
                  by {creator}
                </p>
              )}
              {description && (
                <p className="text-white/70 text-sm mt-2 line-clamp-2">
                  {description}
                </p>
              )}
            </div>
            
            <div className="flex items-center space-x-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onSettingsClick}
                className="text-white hover:bg-white/20 p-2"
              >
                <Settings className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 p-2"
              >
                <MoreVertical className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Tags and Moods */}
          {(tags.length > 0 || moods.length > 0) && (
            <div className="flex flex-wrap gap-2 mt-3">
              {moods.map((mood) => (
                <Badge
                  key={mood}
                  variant="secondary"
                  className="bg-purple-500/20 text-purple-200 border-purple-400/30"
                >
                  {mood}
                </Badge>
              ))}
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="bg-white/10 text-white border-white/30"
                >
                  #{tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Center Play/Pause Overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        {playerState.state === 'paused' && (
          <Button
            variant="ghost"
            size="lg"
            onClick={() => player.play().catch(console.error)}
            className="text-white hover:bg-white/20 p-6 rounded-full bg-black/50 pointer-events-auto"
          >
            <Play className="w-12 h-12" />
          </Button>
        )}
      </div>

      {/* Right Side Actions */}
      {showSocialActions && (
        <div className="absolute right-4 bottom-20 flex flex-col space-y-4 pointer-events-auto">
          {/* Volume Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleVolumeToggle}
            className="text-white hover:bg-white/20 p-3 rounded-full bg-black/30"
          >
            {playerState.muted ? (
              <VolumeX className="w-5 h-5" />
            ) : (
              <Volume2 className="w-5 h-5" />
            )}
          </Button>

          {/* Like Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLike}
            className={cn(
              "p-3 rounded-full bg-black/30 transition-colors",
              isLiked 
                ? "text-red-500 hover:bg-red-500/20" 
                : "text-white hover:bg-white/20"
            )}
          >
            <Heart className={cn("w-5 h-5", isLiked && "fill-current")} />
          </Button>

          {/* Comment Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleComment}
            className="text-white hover:bg-white/20 p-3 rounded-full bg-black/30"
          >
            <MessageCircle className="w-5 h-5" />
          </Button>

          {/* Save Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSave}
            className={cn(
              "p-3 rounded-full bg-black/30 transition-colors",
              isSaved 
                ? "text-yellow-500 hover:bg-yellow-500/20" 
                : "text-white hover:bg-white/20"
            )}
          >
            <Bookmark className={cn("w-5 h-5", isSaved && "fill-current")} />
          </Button>

          {/* Share Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShare}
            className="text-white hover:bg-white/20 p-3 rounded-full bg-black/30"
          >
            <Share className="w-5 h-5" />
          </Button>
        </div>
      )}

      {/* Loading State Overlay */}
      {(playerState.state === 'loading' || playerState.state === 'buffering') && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/30">
          <div className="text-center text-white">
            <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-2" />
            <p className="text-sm">
              {playerState.state === 'loading' ? 'Loading...' : 'Buffering...'}
            </p>
          </div>
        </div>
      )}

      {/* Error State Overlay */}
      {playerState.state === 'error' && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/60 pointer-events-auto">
          <div className="text-center text-white p-6">
            <div className="text-lg font-semibold mb-2">Playback Error</div>
            <p className="text-sm opacity-80 mb-4">
              {playerState.error?.message || 'An error occurred while playing the video'}
            </p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="text-white border-white hover:bg-white hover:text-black"
            >
              Reload Page
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

// Minimal overlay for compact players
export function MinimalVideoOverlay({
  player,
  visible,
  className
}: Pick<VideoPlayerOverlayProps, 'player' | 'visible' | 'className'>) {
  const [playerState, setPlayerState] = useState(player.getState())

  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    return () => player.off('onStateChange', handleStateChange)
  }, [player])

  return (
    <div
      className={cn(
        'absolute inset-0 pointer-events-none transition-opacity duration-300',
        visible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      {/* Center Play Button */}
      {playerState.state === 'paused' && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-auto">
          <Button
            variant="ghost"
            size="lg"
            onClick={() => player.play().catch(console.error)}
            className="text-white hover:bg-white/20 p-4 rounded-full bg-black/50"
          >
            <Play className="w-8 h-8" />
          </Button>
        </div>
      )}

      {/* Volume Indicator */}
      {playerState.muted && (
        <div className="absolute top-4 right-4 pointer-events-auto">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => player.setMuted(false)}
            className="text-white hover:bg-white/20 p-2 rounded-full bg-black/50"
          >
            <VolumeX className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
