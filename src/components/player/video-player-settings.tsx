"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer, PlaybackQuality } from '@/lib/player/types'
import { getQualityLabel } from '@/lib/player/utils'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  X, 
  Monitor, 
  Volume2, 
  Gauge, 
  Subtitles,
  ChevronRight,
  ChevronLeft,
  Check
} from 'lucide-react'

interface VideoPlayerSettingsProps {
  player: VideoPlayer
  onClose: () => void
  className?: string
}

type SettingsPanel = 'main' | 'quality' | 'playback' | 'subtitles'

export function VideoPlayerSettings({
  player,
  onClose,
  className
}: VideoPlayerSettingsProps) {
  const [playerState, setPlayerState] = useState(player.getState())
  const [currentPanel, setCurrentPanel] = useState<SettingsPanel>('main')
  const [textTracks, setTextTracks] = useState<TextTrack[]>([])
  const [activeTextTrack, setActiveTextTrack] = useState<TextTrack | null>(null)

  useEffect(() => {
    const handleStateChange = (state: any) => {
      setPlayerState(state)
    }

    player.on('onStateChange', handleStateChange)
    
    // Get text tracks
    const tracks = player.getTextTracks()
    setTextTracks(tracks)
    
    // Find active track
    const active = tracks.find(track => track.mode === 'showing') || null
    setActiveTextTrack(active)

    return () => player.off('onStateChange', handleStateChange)
  }, [player])

  const handleQualityChange = (quality: PlaybackQuality) => {
    player.setQuality(quality)
    setCurrentPanel('main')
  }

  const handlePlaybackRateChange = (rate: number) => {
    player.setPlaybackRate(rate)
  }

  const handleSubtitleChange = (track: TextTrack | null) => {
    player.setActiveTextTrack(track)
    setActiveTextTrack(track)
    setCurrentPanel('main')
  }

  const availableQualities: PlaybackQuality[] = [
    PlaybackQuality.AUTO,
    PlaybackQuality.UHD,
    PlaybackQuality.HD,
    PlaybackQuality.HIGH,
    PlaybackQuality.MEDIUM,
    PlaybackQuality.LOW
  ]

  const playbackRates = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]

  const renderMainPanel = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white mb-4">Video Settings</h3>
      
      {/* Quality Setting */}
      <div 
        className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
        onClick={() => setCurrentPanel('quality')}
      >
        <div className="flex items-center space-x-3">
          <Monitor className="w-5 h-5 text-white/70" />
          <div>
            <div className="text-white font-medium">Quality</div>
            <div className="text-white/70 text-sm">
              {getQualityLabel(playerState.quality)}
            </div>
          </div>
        </div>
        <ChevronRight className="w-5 h-5 text-white/70" />
      </div>

      {/* Playback Speed */}
      <div 
        className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
        onClick={() => setCurrentPanel('playback')}
      >
        <div className="flex items-center space-x-3">
          <Gauge className="w-5 h-5 text-white/70" />
          <div>
            <div className="text-white font-medium">Playback Speed</div>
            <div className="text-white/70 text-sm">
              {playerState.playbackRate}x
            </div>
          </div>
        </div>
        <ChevronRight className="w-5 h-5 text-white/70" />
      </div>

      {/* Subtitles */}
      {textTracks.length > 0 && (
        <div 
          className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
          onClick={() => setCurrentPanel('subtitles')}
        >
          <div className="flex items-center space-x-3">
            <Subtitles className="w-5 h-5 text-white/70" />
            <div>
              <div className="text-white font-medium">Subtitles</div>
              <div className="text-white/70 text-sm">
                {activeTextTrack ? activeTextTrack.label || 'On' : 'Off'}
              </div>
            </div>
          </div>
          <ChevronRight className="w-5 h-5 text-white/70" />
        </div>
      )}

      {/* Volume Control */}
      <div className="p-3 rounded-lg bg-white/5">
        <div className="flex items-center space-x-3 mb-3">
          <Volume2 className="w-5 h-5 text-white/70" />
          <div className="text-white font-medium">Volume</div>
        </div>
        <div className="space-y-3">
          <Slider
            value={[playerState.muted ? 0 : playerState.volume * 100]}
            onValueChange={(value) => {
              const volume = value[0] / 100
              player.setVolume(volume)
              if (volume > 0 && playerState.muted) {
                player.setMuted(false)
              }
            }}
            max={100}
            step={1}
            className="w-full"
            trackClassName="bg-white/20"
            rangeClassName="bg-purple-500"
            thumbClassName="bg-purple-500 border-2 border-white"
          />
          <div className="flex items-center justify-between">
            <span className="text-white/70 text-sm">Muted</span>
            <Switch
              checked={!playerState.muted}
              onCheckedChange={(checked) => player.setMuted(!checked)}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderQualityPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentPanel('main')}
          className="text-white hover:bg-white/20 p-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">Quality</h3>
      </div>

      <div className="space-y-2">
        {availableQualities.map((quality) => (
          <div
            key={quality}
            className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
            onClick={() => handleQualityChange(quality)}
          >
            <span className="text-white">{getQualityLabel(quality)}</span>
            {playerState.quality === quality && (
              <Check className="w-5 h-5 text-purple-500" />
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderPlaybackPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentPanel('main')}
          className="text-white hover:bg-white/20 p-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">Playback Speed</h3>
      </div>

      <div className="space-y-2">
        {playbackRates.map((rate) => (
          <div
            key={rate}
            className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
            onClick={() => handlePlaybackRateChange(rate)}
          >
            <span className="text-white">{rate}x</span>
            {playerState.playbackRate === rate && (
              <Check className="w-5 h-5 text-purple-500" />
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderSubtitlesPanel = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentPanel('main')}
          className="text-white hover:bg-white/20 p-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <h3 className="text-lg font-semibold text-white">Subtitles</h3>
      </div>

      <div className="space-y-2">
        <div
          className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
          onClick={() => handleSubtitleChange(null)}
        >
          <span className="text-white">Off</span>
          {!activeTextTrack && (
            <Check className="w-5 h-5 text-purple-500" />
          )}
        </div>
        
        {textTracks.map((track, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-colors"
            onClick={() => handleSubtitleChange(track)}
          >
            <span className="text-white">
              {track.label || track.language || `Track ${index + 1}`}
            </span>
            {activeTextTrack === track && (
              <Check className="w-5 h-5 text-purple-500" />
            )}
          </div>
        ))}
      </div>
    </div>
  )

  const renderCurrentPanel = () => {
    switch (currentPanel) {
      case 'quality':
        return renderQualityPanel()
      case 'playback':
        return renderPlaybackPanel()
      case 'subtitles':
        return renderSubtitlesPanel()
      default:
        return renderMainPanel()
    }
  }

  return (
    <div className={cn(
      'absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50',
      className
    )}>
      <div className="bg-gray-900/95 backdrop-blur-md rounded-lg border border-white/10 w-full max-w-md max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="w-6" /> {/* Spacer */}
          <h2 className="text-white font-semibold">Settings</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20 p-2"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {renderCurrentPanel()}
        </div>
      </div>
    </div>
  )
}
