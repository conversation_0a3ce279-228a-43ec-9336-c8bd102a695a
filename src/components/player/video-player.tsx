"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { VideoPlayer as VideoPlayerType, MediaSource, PlayerConfig } from '@/lib/player/types'
import { createVideoPlayer } from '@/lib/player'
import { VideoPlayerControls } from './video-player-controls'
import { VideoPlayerOverlay } from './video-player-overlay'
import { VideoPlayerSettings } from './video-player-settings'
import { Loader2 } from 'lucide-react'

interface VideoPlayerProps {
  source?: MediaSource
  config?: Partial<PlayerConfig>
  className?: string
  onPlayerReady?: (player: VideoPlayerType) => void
  onStateChange?: (state: any) => void
  onTimeUpdate?: (currentTime: number) => void
  onError?: (error: any) => void
  showControls?: boolean
  showOverlay?: boolean
  autoPlay?: boolean
  muted?: boolean
  loop?: boolean
  poster?: string
  aspectRatio?: 'auto' | '16:9' | '4:3' | '1:1' | '9:16'
}

export function VideoPlayer({
  source,
  config = {},
  className,
  onPlayerReady,
  onStateChange,
  onTimeUpdate,
  onError,
  showControls = true,
  showOverlay = true,
  autoPlay = false,
  muted = true,
  loop = false,
  poster,
  aspectRatio = 'auto'
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<VideoPlayerType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [controlsVisible, setControlsVisible] = useState(true)
  const [isHovering, setIsHovering] = useState(false)
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout>()

  // Initialize player
  useEffect(() => {
    if (!videoRef.current) return

    const playerConfig: Partial<PlayerConfig> = {
      autoPlay,
      muted,
      loop,
      controls: false, // We use custom controls
      ...config
    }

    try {
      const videoPlayer = createVideoPlayer(videoRef.current, playerConfig)
      setPlayer(videoPlayer)

      // Setup event listeners
      videoPlayer.on('onStateChange', (state) => {
        setIsLoading(state.state === 'loading' || state.state === 'buffering')
        onStateChange?.(state)
      })

      videoPlayer.on('onTimeUpdate', (currentTime) => {
        onTimeUpdate?.(currentTime)
      })

      videoPlayer.on('onError', (error) => {
        setError(error.message)
        onError?.(error)
      })

      onPlayerReady?.(videoPlayer)

      return () => {
        videoPlayer.destroy()
      }
    } catch (err) {
      setError('Failed to initialize video player')
      console.error('Video player initialization error:', err)
    }
  }, [autoPlay, muted, loop, config, onPlayerReady, onStateChange, onTimeUpdate, onError])

  // Load source when it changes
  useEffect(() => {
    if (player && source) {
      setIsLoading(true)
      setError(null)
      
      player.load(source).catch((err) => {
        setError('Failed to load video source')
        console.error('Video load error:', err)
      })
    }
  }, [player, source])

  // Auto-hide controls
  const resetControlsTimeout = useCallback(() => {
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current)
    }

    if (!isHovering) {
      hideControlsTimeoutRef.current = setTimeout(() => {
        setControlsVisible(false)
      }, 3000)
    }
  }, [isHovering])

  const showControlsTemporarily = useCallback(() => {
    setControlsVisible(true)
    resetControlsTimeout()
  }, [resetControlsTimeout])

  // Handle mouse events for controls visibility
  const handleMouseEnter = useCallback(() => {
    setIsHovering(true)
    setControlsVisible(true)
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current)
    }
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false)
    resetControlsTimeout()
  }, [resetControlsTimeout])

  const handleMouseMove = useCallback(() => {
    showControlsTemporarily()
  }, [showControlsTemporarily])

  // Handle click to play/pause
  const handleVideoClick = useCallback(() => {
    if (!player) return

    const state = player.getState()
    if (state.state === 'playing') {
      player.pause()
    } else {
      player.play().catch(console.error)
    }
    
    showControlsTemporarily()
  }, [player, showControlsTemporarily])

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!player) return

    const handled = (player as any).handleKeyboard?.(event.nativeEvent)
    if (handled) {
      showControlsTemporarily()
    }
  }, [player, showControlsTemporarily])

  // Get aspect ratio class
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case '16:9':
        return 'aspect-video'
      case '4:3':
        return 'aspect-[4/3]'
      case '1:1':
        return 'aspect-square'
      case '9:16':
        return 'aspect-[9/16]'
      default:
        return ''
    }
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative bg-black overflow-hidden group',
        getAspectRatioClass(),
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="application"
      aria-label="Video player"
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover cursor-pointer"
        poster={poster}
        onClick={handleVideoClick}
        playsInline
        webkit-playsinline="true"
      />

      {/* Loading Spinner */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <Loader2 className="w-8 h-8 text-white animate-spin" />
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80">
          <div className="text-center text-white">
            <div className="text-lg font-semibold mb-2">Playback Error</div>
            <div className="text-sm opacity-80">{error}</div>
          </div>
        </div>
      )}

      {/* Video Overlay */}
      {showOverlay && player && (
        <VideoPlayerOverlay
          player={player}
          visible={controlsVisible}
          onSettingsClick={() => setShowSettings(true)}
        />
      )}

      {/* Video Controls */}
      {showControls && player && (
        <VideoPlayerControls
          player={player}
          visible={controlsVisible}
          onInteraction={showControlsTemporarily}
        />
      )}

      {/* Settings Panel */}
      {showSettings && player && (
        <VideoPlayerSettings
          player={player}
          onClose={() => setShowSettings(false)}
        />
      )}

      {/* Gradient Overlays for Better Control Visibility */}
      <div 
        className={cn(
          'absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-black/50 to-transparent pointer-events-none transition-opacity duration-300',
          controlsVisible ? 'opacity-100' : 'opacity-0'
        )}
      />
      <div 
        className={cn(
          'absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/50 to-transparent pointer-events-none transition-opacity duration-300',
          controlsVisible ? 'opacity-100' : 'opacity-0'
        )}
      />
    </div>
  )
}

// Export additional video player variants
export function VerticalVideoPlayer(props: VideoPlayerProps) {
  return (
    <VideoPlayer
      {...props}
      aspectRatio="9:16"
      className={cn('h-screen', props.className)}
    />
  )
}

export function CompactVideoPlayer(props: VideoPlayerProps) {
  return (
    <VideoPlayer
      {...props}
      showOverlay={false}
      className={cn('rounded-lg overflow-hidden', props.className)}
    />
  )
}

export function FullscreenVideoPlayer(props: VideoPlayerProps) {
  return (
    <VideoPlayer
      {...props}
      className={cn('w-screen h-screen', props.className)}
    />
  )
}
