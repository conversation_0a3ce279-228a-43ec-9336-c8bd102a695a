"use client"

import React, { use<PERSON><PERSON>back, useState, useRef, useEffect } from 'react'
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  ReactFlowProvider,
  Panel
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Audio processing imports
import { useAudioContext, useAudioContextAutoResume } from './hooks/useAudioContext'
import { AudioRouter, AudioNodeFactory } from '@/lib/audio/audio-router'
import { BaseAudioNode } from '@/lib/audio/base-audio-node'
import { DJDeckAudioNode } from '@/lib/audio/nodes/dj-deck-audio-node'
import { ChannelMixerAudioNode } from '@/lib/audio/nodes/channel-mixer-audio-node'
import { CrossfaderAudioNode } from '@/lib/audio/nodes/crossfader-audio-node'
import { EffectAudioNode } from '@/lib/audio/nodes/effect-audio-node'
import { MasterOutputAudioNode } from '@/lib/audio/nodes/master-output-audio-node'

// Import custom node types
import { DJDeckNode } from './nodes/dj-deck-node'
import { CrossfaderNode } from './nodes/crossfader-node'
import { ChannelMixerNode } from './nodes/channel-mixer-node'
import { MasterOutputNode } from './nodes/master-output-node'
import { EffectNode } from './nodes/effect-node'
import { AnalyzerNode } from './nodes/analyzer-node'

import { Plus, Save, FolderOpen, Play, Pause, Square, Zap } from 'lucide-react'

// Define node types
const nodeTypes = {
  'dj-deck': DJDeckNode,
  'crossfader': CrossfaderNode,
  'channel-mixer': ChannelMixerNode,
  'master-output': MasterOutputNode,
  'effect': EffectNode,
  'analyzer': AnalyzerNode,
}

// Initial nodes for a basic DJ setup
const initialNodes: Node[] = [
  {
    id: 'deck-a',
    type: 'dj-deck',
    position: { x: 50, y: 100 },
    data: {
      deckId: 'A',
      track: null,
      isPlaying: false,
      volume: 0.8,
      tempo: 1.0,
      pitch: 0,
      cuePoints: [],
      waveform: null
    }
  },
  {
    id: 'deck-b',
    type: 'dj-deck',
    position: { x: 50, y: 400 },
    data: {
      deckId: 'B',
      track: null,
      isPlaying: false,
      volume: 0.8,
      tempo: 1.0,
      pitch: 0,
      cuePoints: [],
      waveform: null
    }
  },
  {
    id: 'mixer-a',
    type: 'channel-mixer',
    position: { x: 400, y: 150 },
    data: {
      channel: 'A',
      volume: 0.8,
      highEQ: 0,
      midEQ: 0,
      lowEQ: 0,
      cue: false
    }
  },
  {
    id: 'mixer-b',
    type: 'channel-mixer',
    position: { x: 400, y: 350 },
    data: {
      channel: 'B',
      volume: 0.8,
      highEQ: 0,
      midEQ: 0,
      lowEQ: 0,
      cue: false
    }
  },
  {
    id: 'crossfader',
    type: 'crossfader',
    position: { x: 600, y: 250 },
    data: {
      position: 0,
      curve: 'logarithmic'
    }
  },
  {
    id: 'master-output',
    type: 'master-output',
    position: { x: 800, y: 250 },
    data: {
      masterVolume: 0.8,
      cueVolume: 0.5,
      isRecording: false,
      isLive: false
    }
  }
]

// Initial edges connecting the nodes
const initialEdges: Edge[] = [
  { id: 'deck-a-mixer-a', source: 'deck-a', target: 'mixer-a', type: 'default' },
  { id: 'deck-b-mixer-b', source: 'deck-b', target: 'mixer-b', type: 'default' },
  { id: 'mixer-a-crossfader', source: 'mixer-a', target: 'crossfader', sourceHandle: 'output', targetHandle: 'input-a' },
  { id: 'mixer-b-crossfader', source: 'mixer-b', target: 'crossfader', sourceHandle: 'output', targetHandle: 'input-b' },
  { id: 'crossfader-master', source: 'crossfader', target: 'master-output', type: 'default' }
]

interface AudioGraphContainerProps {
  className?: string
  onNodeUpdate?: (nodeId: string, data: any) => void
  onAudioProcess?: (audioData: any) => void
}

export function AudioGraphContainer({
  className,
  // onNodeUpdate,
  onAudioProcess
}: AudioGraphContainerProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [isPlaying, setIsPlaying] = useState(false)
  // const [selectedNodeType, setSelectedNodeType] = useState<string>('')

  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null)

  // Audio processing setup
  const audioContext = useAudioContext()
  const audioRouterRef = useRef<AudioRouter | null>(null)

  // Auto-resume audio context on user interaction
  useAudioContextAutoResume(audioContext)

  // Initialize audio router
  useEffect(() => {
    const initializeAudioRouter = async () => {
      if (audioContext.isReady && !audioRouterRef.current) {
        const router = new AudioRouter()

        // Create node factory with complete implementations
        const nodeFactory: AudioNodeFactory = {
          createAudioNode: async (nodeId: string, nodeType: string, nodeData: any): Promise<BaseAudioNode> => {
            console.log(`Creating audio node: ${nodeId} (${nodeType})`, nodeData)

            switch (nodeType) {
              case 'dj-deck':
                return new DJDeckAudioNode({
                  id: nodeId,
                  type: nodeType,
                  data: nodeData
                })

              case 'channel-mixer':
                return new ChannelMixerAudioNode({
                  id: nodeId,
                  type: nodeType,
                  data: nodeData
                })

              case 'crossfader':
                return new CrossfaderAudioNode({
                  id: nodeId,
                  type: nodeType,
                  data: nodeData
                })

              case 'master-output':
                return new MasterOutputAudioNode({
                  id: nodeId,
                  type: nodeType,
                  data: nodeData
                })

              case 'effect':
                return new EffectAudioNode({
                  id: nodeId,
                  type: nodeType,
                  data: nodeData
                })

              case 'analyzer':
                // Analyzer can use effect node with spectrum analysis
                return new EffectAudioNode({
                  id: nodeId,
                  type: 'analyzer',
                  data: {
                    effectType: 'filter',
                    enabled: true,
                    wetness: 1.0,
                    parameters: { frequency: 1000, resonance: 1 }
                  }
                })

              default:
                throw new Error(`Unknown audio node type: ${nodeType}`)
            }
          }
        }

        router.setNodeFactory(nodeFactory)
        await router.initialize()
        audioRouterRef.current = router

        console.log('Audio router initialized')
      }
    }

    initializeAudioRouter()
  }, [audioContext.isReady])

  // Update audio nodes when ReactFlow nodes change
  useEffect(() => {
    if (audioRouterRef.current) {
      audioRouterRef.current.updateNodes(nodes)
    }
  }, [nodes])

  // Update audio connections when ReactFlow edges change
  useEffect(() => {
    if (audioRouterRef.current) {
      audioRouterRef.current.updateConnections(edges)
    }
  }, [edges])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onInit = useCallback((instance: any) => {
    setReactFlowInstance(instance)
  }, [])

  // Handle node data updates
  // const handleNodeUpdate = useCallback((nodeId: string, newData: any) => {
  //   setNodes((nds) =>
  //     nds.map((node) =>
  //       node.id === nodeId
  //         ? { ...node, data: { ...node.data, ...newData } }
  //         : node
  //     )
  //   )
  //   onNodeUpdate?.(nodeId, newData)
  // }, [setNodes, onNodeUpdate])

  // Add new node to the graph
  const addNode = useCallback((nodeType: string) => {
    if (!reactFlowInstance) return

    const newNode: Node = {
      id: `${nodeType}-${Date.now()}`,
      type: nodeType,
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: getDefaultNodeData(nodeType)
    }

    setNodes((nds) => [...nds, newNode])
  }, [reactFlowInstance, setNodes])

  // Get default data for different node types
  const getDefaultNodeData = (nodeType: string) => {
    switch (nodeType) {
      case 'effect':
        return {
          effectType: 'reverb',
          enabled: true,
          parameters: { wetness: 0.3, roomSize: 0.5 }
        }
      case 'analyzer':
        return {
          analyzerType: 'spectrum',
          fftSize: 2048
        }
      default:
        return {}
    }
  }

  // Global transport controls
  const handlePlay = async () => {
    // Ensure audio context is running
    if (audioContext.state === 'suspended') {
      await audioContext.resume()
    }

    setIsPlaying(true)
    // Trigger play on all deck nodes
    setNodes((nds) =>
      nds.map((node) =>
        node.type === 'dj-deck'
          ? { ...node, data: { ...node.data, isPlaying: true } }
          : node
      )
    )

    // Notify audio processing callback
    onAudioProcess?.({ action: 'play', timestamp: audioContext.getCurrentTime() })
  }

  const handlePause = () => {
    setIsPlaying(false)
    setNodes((nds) =>
      nds.map((node) =>
        node.type === 'dj-deck'
          ? { ...node, data: { ...node.data, isPlaying: false } }
          : node
      )
    )

    // Notify audio processing callback
    onAudioProcess?.({ action: 'pause', timestamp: audioContext.getCurrentTime() })
  }

  const handleStop = () => {
    setIsPlaying(false)
    setNodes((nds) =>
      nds.map((node) =>
        node.type === 'dj-deck'
          ? { ...node, data: { ...node.data, isPlaying: false, currentTime: 0 } }
          : node
      )
    )

    // Notify audio processing callback
    onAudioProcess?.({ action: 'stop', timestamp: audioContext.getCurrentTime() })
  }

  return (
    <div className={cn("h-full w-full", className)} ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={onInit}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Background />
        <Controls />
        <MiniMap />
        
        {/* Top Panel - Transport Controls */}
        <Panel position="top-left">
          <div className="flex items-center gap-2 bg-card border rounded-lg p-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleStop}
              disabled={!audioContext.isReady}
            >
              <Square className="w-4 h-4" />
            </Button>
            <Button
              variant={isPlaying ? "default" : "outline"}
              size="sm"
              onClick={isPlaying ? handlePause : handlePlay}
              disabled={!audioContext.isReady}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Badge
              variant={audioContext.isReady ? "default" : "secondary"}
              className="text-xs"
            >
              {audioContext.isReady ? "Audio Ready" : `Audio ${audioContext.state}`}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {audioContext.getSampleRate()}Hz
            </Badge>
          </div>
        </Panel>

        {/* Top Panel - Add Nodes */}
        <Panel position="top-right">
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-1" />
                  Add Node
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => addNode('effect')}>
                  <Zap className="w-4 h-4 mr-2" />
                  Effect
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addNode('analyzer')}>
                  <Zap className="w-4 h-4 mr-2" />
                  Analyzer
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => addNode('dj-deck')}>
                  <Plus className="w-4 h-4 mr-2" />
                  DJ Deck
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addNode('channel-mixer')}>
                  <Plus className="w-4 h-4 mr-2" />
                  Channel Mixer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="outline" size="sm">
              <Save className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm">
              <FolderOpen className="w-4 h-4" />
            </Button>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  )
}

// Wrapper component with ReactFlowProvider
export function AudioGraphWrapper(props: AudioGraphContainerProps) {
  return (
    <ReactFlowProvider>
      <AudioGraphContainer {...props} />
    </ReactFlowProvider>
  )
}
