/**
 * ChannelMixerUI - Professional channel mixer interface
 * Provides 3-band EQ, gain control, volume fader, cue controls, and kill switches
 */

"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { 
  Volume2, 
  Headphones, 
  Zap,
  RotateCcw,
  Settings,
  Mic
} from 'lucide-react'

export interface ChannelMixerUIProps {
  channel: string
  volume: number
  gain: number
  highEQ: number
  midEQ: number
  lowEQ: number
  highKill: boolean
  midKill: boolean
  lowKill: boolean
  cue: boolean
  pfl: boolean
  onVolumeChange: (volume: number) => void
  onGainChange: (gain: number) => void
  onHighEQChange: (eq: number) => void
  onMidEQChange: (eq: number) => void
  onLowEQChange: (eq: number) => void
  onHighKillToggle: () => void
  onMidKillToggle: () => void
  onLowKillToggle: () => void
  onCueToggle: () => void
  onPFLToggle: () => void
  onReset?: () => void
  className?: string
  disabled?: boolean
}

export function ChannelMixerUI({
  channel,
  volume,
  gain,
  highEQ,
  midEQ,
  lowEQ,
  highKill,
  midKill,
  lowKill,
  cue,
  pfl,
  onVolumeChange,
  onGainChange,
  onHighEQChange,
  onMidEQChange,
  onLowEQChange,
  onHighKillToggle,
  onMidKillToggle,
  onLowKillToggle,
  onCueToggle,
  onPFLToggle,
  onReset,
  className,
  disabled = false
}: ChannelMixerUIProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Format dB values for display
  const formatDB = (value: number): string => {
    return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1)
  }

  // Handle EQ reset
  const handleEQReset = useCallback(() => {
    onHighEQChange(0)
    onMidEQChange(0)
    onLowEQChange(0)
  }, [onHighEQChange, onMidEQChange, onLowEQChange])

  return (
    <div className={cn(
      "bg-card border rounded-lg p-3 space-y-3 min-w-[120px] max-w-[140px]",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <Badge variant="outline" className="text-xs font-mono">
          CH {channel}
        </Badge>
        
        {onReset && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="h-5 w-5 p-0"
          >
            <Settings className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Gain Control */}
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium">Gain</span>
          <span className="text-xs text-muted-foreground font-mono">
            {formatDB(gain)}dB
          </span>
        </div>
        
        <div className="relative">
          <Slider
            value={[gain]}
            onValueChange={(values) => onGainChange(values[0])}
            min={-20}
            max={20}
            step={0.1}
            disabled={disabled}
            className="w-full"
            orientation="horizontal"
          />
          {/* Center marker */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-3 bg-muted-foreground/50" />
        </div>
      </div>

      {/* 3-Band EQ */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium">EQ</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEQReset}
            disabled={disabled || (highEQ === 0 && midEQ === 0 && lowEQ === 0)}
            className="h-4 w-4 p-0"
          >
            <RotateCcw className="h-2.5 w-2.5" />
          </Button>
        </div>

        {/* High EQ */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">High</span>
            <Button
              variant={highKill ? "destructive" : "ghost"}
              size="sm"
              onClick={onHighKillToggle}
              disabled={disabled}
              className="h-4 w-8 p-0 text-xs"
            >
              {highKill ? "KILL" : ""}
            </Button>
          </div>
          
          <div className="relative">
            <Slider
              value={[highEQ]}
              onValueChange={(values) => onHighEQChange(values[0])}
              min={-15}
              max={15}
              step={0.1}
              disabled={disabled || highKill}
              className="w-full"
              orientation="horizontal"
            />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-2 bg-muted-foreground/50" />
          </div>
          
          <div className="text-center text-xs text-muted-foreground font-mono">
            {formatDB(highEQ)}
          </div>
        </div>

        {/* Mid EQ */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Mid</span>
            <Button
              variant={midKill ? "destructive" : "ghost"}
              size="sm"
              onClick={onMidKillToggle}
              disabled={disabled}
              className="h-4 w-8 p-0 text-xs"
            >
              {midKill ? "KILL" : ""}
            </Button>
          </div>
          
          <div className="relative">
            <Slider
              value={[midEQ]}
              onValueChange={(values) => onMidEQChange(values[0])}
              min={-15}
              max={15}
              step={0.1}
              disabled={disabled || midKill}
              className="w-full"
              orientation="horizontal"
            />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-2 bg-muted-foreground/50" />
          </div>
          
          <div className="text-center text-xs text-muted-foreground font-mono">
            {formatDB(midEQ)}
          </div>
        </div>

        {/* Low EQ */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Low</span>
            <Button
              variant={lowKill ? "destructive" : "ghost"}
              size="sm"
              onClick={onLowKillToggle}
              disabled={disabled}
              className="h-4 w-8 p-0 text-xs"
            >
              {lowKill ? "KILL" : ""}
            </Button>
          </div>
          
          <div className="relative">
            <Slider
              value={[lowEQ]}
              onValueChange={(values) => onLowEQChange(values[0])}
              min={-15}
              max={15}
              step={0.1}
              disabled={disabled || lowKill}
              className="w-full"
              orientation="horizontal"
            />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-2 bg-muted-foreground/50" />
          </div>
          
          <div className="text-center text-xs text-muted-foreground font-mono">
            {formatDB(lowEQ)}
          </div>
        </div>
      </div>

      {/* Cue Controls */}
      <div className="space-y-1">
        <div className="flex gap-1">
          <Button
            variant={cue ? "default" : "outline"}
            size="sm"
            onClick={onCueToggle}
            disabled={disabled}
            className="flex-1 h-6 text-xs"
          >
            <Headphones className="h-3 w-3 mr-1" />
            CUE
          </Button>
          
          <Button
            variant={pfl ? "default" : "outline"}
            size="sm"
            onClick={onPFLToggle}
            disabled={disabled}
            className="h-6 px-2 text-xs"
          >
            PFL
          </Button>
        </div>
      </div>

      {/* Volume Fader */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium">Volume</span>
          <span className="text-xs text-muted-foreground font-mono">
            {Math.round(volume * 100)}%
          </span>
        </div>
        
        <div className="relative h-32 flex justify-center">
          <Slider
            value={[volume]}
            onValueChange={(values) => onVolumeChange(values[0])}
            min={0}
            max={1}
            step={0.01}
            disabled={disabled}
            className="h-full"
            orientation="vertical"
          />
          
          {/* Volume level indicator */}
          <div className="absolute left-8 top-0 h-full w-2 bg-muted rounded-sm overflow-hidden">
            <div 
              className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500 transition-all duration-100"
              style={{ height: `${volume * 100}%` }}
            />
          </div>
        </div>
        
        <div className="flex justify-center">
          <Volume2 className="h-4 w-4 text-muted-foreground" />
        </div>
      </div>

      {/* Advanced Controls */}
      {showAdvanced && (
        <div className="pt-2 border-t space-y-2">
          <div className="text-xs font-medium">Advanced</div>
          
          {onReset && (
            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              disabled={disabled}
              className="w-full h-6 text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset All
            </Button>
          )}
          
          <div className="grid grid-cols-2 gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={disabled}
              className="h-6 text-xs"
            >
              <Mic className="h-3 w-3" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              disabled={disabled}
              className="h-6 text-xs"
            >
              <Zap className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
