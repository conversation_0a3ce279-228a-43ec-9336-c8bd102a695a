/**
 * TempoPitchControl - Advanced tempo and pitch control component
 * Provides professional DJ-style tempo adjustment, pitch bend, and sync controls
 */

"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { 
  RotateCcw, 
  RotateCw, 
  Zap, 
  Lock, 
  Unlock,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

export interface TempoPitchControlProps {
  tempo: number // 0.5 - 2.0 (50% - 200%)
  pitch: number // -12 to +12 semitones
  bpm?: number
  targetBpm?: number
  isTempoLocked?: boolean
  isPitchLocked?: boolean
  isSyncing?: boolean
  onTempoChange: (tempo: number) => void
  onPitchChange: (pitch: number) => void
  onTempoReset: () => void
  onPitchReset: () => void
  onTempoLockToggle?: () => void
  onPitchLockToggle?: () => void
  onSync?: () => void
  onPitchBend?: (direction: 'up' | 'down', active: boolean) => void
  className?: string
  disabled?: boolean
}

export function TempoPitchControl({
  tempo,
  pitch,
  bpm,
  targetBpm,
  isTempoLocked = false,
  isPitchLocked = false,
  isSyncing = false,
  onTempoChange,
  onPitchChange,
  onTempoReset,
  onPitchReset,
  onTempoLockToggle,
  onPitchLockToggle,
  onSync,
  onPitchBend,
  className,
  disabled = false
}: TempoPitchControlProps) {
  const [isDraggingTempo, setIsDraggingTempo] = useState(false)
  const [isDraggingPitch, setIsDraggingPitch] = useState(false)
  const [pitchBendActive, setPitchBendActive] = useState<'up' | 'down' | null>(null)
  
  const tempoSliderRef = useRef<HTMLDivElement>(null)
  const pitchSliderRef = useRef<HTMLDivElement>(null)
  const pitchBendIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Calculate tempo percentage
  const tempoPercentage = Math.round((tempo - 1) * 100)
  const tempoRange = [-50, 50] // -50% to +50%
  
  // Calculate current BPM if available
  const currentBpm = bpm ? Math.round(bpm * tempo) : undefined
  
  // Calculate sync difference
  const syncDifference = targetBpm && currentBpm ? targetBpm - currentBpm : 0

  // Handle tempo slider change
  const handleTempoChange = useCallback((values: number[]) => {
    if (disabled || isTempoLocked) return
    
    const percentage = values[0]
    const newTempo = 1 + (percentage / 100)
    onTempoChange(Math.max(0.5, Math.min(2.0, newTempo)))
  }, [disabled, isTempoLocked, onTempoChange])

  // Handle pitch slider change
  const handlePitchChange = useCallback((values: number[]) => {
    if (disabled || isPitchLocked) return
    
    const newPitch = values[0]
    onPitchChange(Math.max(-12, Math.min(12, newPitch)))
  }, [disabled, isPitchLocked, onPitchChange])

  // Handle pitch bend
  const handlePitchBendStart = useCallback((direction: 'up' | 'down') => {
    if (disabled || isPitchLocked) return
    
    setPitchBendActive(direction)
    onPitchBend?.(direction, true)
    
    // Continuous pitch bend while held
    pitchBendIntervalRef.current = setInterval(() => {
      onPitchBend?.(direction, true)
    }, 50)
  }, [disabled, isPitchLocked, onPitchBend])

  const handlePitchBendEnd = useCallback(() => {
    if (pitchBendIntervalRef.current) {
      clearInterval(pitchBendIntervalRef.current)
      pitchBendIntervalRef.current = null
    }
    
    if (pitchBendActive) {
      onPitchBend?.(pitchBendActive, false)
      setPitchBendActive(null)
    }
  }, [pitchBendActive, onPitchBend])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pitchBendIntervalRef.current) {
        clearInterval(pitchBendIntervalRef.current)
      }
    }
  }, [])

  // Handle double-click to reset
  const handleTempoDoubleClick = useCallback(() => {
    if (!disabled && !isTempoLocked) {
      onTempoReset()
    }
  }, [disabled, isTempoLocked, onTempoReset])

  const handlePitchDoubleClick = useCallback(() => {
    if (!disabled && !isPitchLocked) {
      onPitchReset()
    }
  }, [disabled, isPitchLocked, onPitchReset])

  return (
    <div className={cn("space-y-4 p-4 bg-card rounded-lg border", className)}>
      {/* Tempo Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Tempo</span>
            {onTempoLockToggle && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onTempoLockToggle}
                disabled={disabled}
                className="h-6 w-6 p-0"
              >
                {isTempoLocked ? (
                  <Lock className="h-3 w-3" />
                ) : (
                  <Unlock className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {currentBpm && (
              <Badge variant="outline" className="text-xs">
                {currentBpm} BPM
              </Badge>
            )}
            
            <Badge 
              variant={tempoPercentage === 0 ? "default" : "secondary"}
              className="text-xs min-w-[60px]"
            >
              {tempoPercentage > 0 ? '+' : ''}{tempoPercentage}%
            </Badge>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onTempoReset}
              disabled={disabled || isTempoLocked || tempo === 1}
              className="h-6 w-6 p-0"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div 
          ref={tempoSliderRef}
          onDoubleClick={handleTempoDoubleClick}
          className="relative"
        >
          <Slider
            value={[tempoPercentage]}
            onValueChange={handleTempoChange}
            min={tempoRange[0]}
            max={tempoRange[1]}
            step={0.1}
            disabled={disabled || isTempoLocked}
            className={cn(
              "w-full",
              isTempoLocked && "opacity-50"
            )}
          />
          
          {/* Center marker */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-muted-foreground/50" />
        </div>
        
        {/* Sync controls */}
        {targetBpm && onSync && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">
              Target: {targetBpm} BPM
            </span>
            
            <div className="flex items-center gap-2">
              {syncDifference !== 0 && (
                <Badge 
                  variant={Math.abs(syncDifference) < 1 ? "default" : "destructive"}
                  className="text-xs"
                >
                  {syncDifference > 0 ? '+' : ''}{syncDifference.toFixed(1)}
                </Badge>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={onSync}
                disabled={disabled || isTempoLocked || isSyncing}
                className="h-6 px-2"
              >
                {isSyncing ? (
                  <Zap className="h-3 w-3 animate-pulse" />
                ) : (
                  <Zap className="h-3 w-3" />
                )}
                Sync
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Pitch Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Pitch</span>
            {onPitchLockToggle && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onPitchLockToggle}
                disabled={disabled}
                className="h-6 w-6 p-0"
              >
                {isPitchLocked ? (
                  <Lock className="h-3 w-3" />
                ) : (
                  <Unlock className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Badge 
              variant={pitch === 0 ? "default" : "secondary"}
              className="text-xs min-w-[60px]"
            >
              {pitch > 0 ? '+' : ''}{pitch.toFixed(1)}st
            </Badge>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onPitchReset}
              disabled={disabled || isPitchLocked || pitch === 0}
              className="h-6 w-6 p-0"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div 
          ref={pitchSliderRef}
          onDoubleClick={handlePitchDoubleClick}
          className="relative"
        >
          <Slider
            value={[pitch]}
            onValueChange={handlePitchChange}
            min={-12}
            max={12}
            step={0.1}
            disabled={disabled || isPitchLocked}
            className={cn(
              "w-full",
              isPitchLocked && "opacity-50"
            )}
          />
          
          {/* Center marker */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-muted-foreground/50" />
        </div>
        
        {/* Pitch bend buttons */}
        {onPitchBend && (
          <div className="flex items-center justify-center gap-2">
            <Button
              variant={pitchBendActive === 'down' ? "default" : "outline"}
              size="sm"
              disabled={disabled || isPitchLocked}
              onMouseDown={() => handlePitchBendStart('down')}
              onMouseUp={handlePitchBendEnd}
              onMouseLeave={handlePitchBendEnd}
              onTouchStart={() => handlePitchBendStart('down')}
              onTouchEnd={handlePitchBendEnd}
              className="h-8 px-3"
            >
              <TrendingDown className="h-4 w-4 mr-1" />
              Bend -
            </Button>
            
            <Button
              variant={pitchBendActive === 'up' ? "default" : "outline"}
              size="sm"
              disabled={disabled || isPitchLocked}
              onMouseDown={() => handlePitchBendStart('up')}
              onMouseUp={handlePitchBendEnd}
              onMouseLeave={handlePitchBendEnd}
              onTouchStart={() => handlePitchBendStart('up')}
              onTouchEnd={handlePitchBendEnd}
              className="h-8 px-3"
            >
              <TrendingUp className="h-4 w-4 mr-1" />
              Bend +
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
