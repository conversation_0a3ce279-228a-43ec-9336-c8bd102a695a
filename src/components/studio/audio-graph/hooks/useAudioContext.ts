/**
 * useAudioContext - React hook for managing Web Audio API context
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { getAudioContextManager, AudioContextState, AudioContextConfig } from '@/lib/audio/audio-context-manager'

export interface UseAudioContextReturn {
  audioContext: AudioContext | null
  state: AudioContextState
  isReady: boolean
  initialize: () => Promise<void>
  resume: () => Promise<void>
  suspend: () => Promise<void>
  close: () => Promise<void>
  setMasterVolume: (volume: number) => void
  getCurrentTime: () => number
  getSampleRate: () => number
  getInfo: () => object
}

export function useAudioContext(config?: AudioContextConfig): UseAudioContextReturn {
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null)
  const [state, setState] = useState<AudioContextState>('suspended')
  const [isReady, setIsReady] = useState(false)
  
  const audioContextManagerRef = useRef(getAudioContextManager(config))
  const listenerIdRef = useRef(`hook-${Date.now()}-${Math.random()}`)

  // Initialize audio context manager
  useEffect(() => {
    const manager = audioContextManagerRef.current
    const listenerId = listenerIdRef.current

    // Add state listener
    manager.addStateListener(listenerId, (newState) => {
      setState(newState)
      setIsReady(newState === 'running')
    })

    // Set initial state
    setState(manager.getState())
    setAudioContext(manager.getContext())
    setIsReady(manager.isReady())

    // Cleanup on unmount
    return () => {
      manager.removeStateListener(listenerId)
    }
  }, [])

  // Initialize audio context
  const initialize = useCallback(async () => {
    try {
      const manager = audioContextManagerRef.current
      await manager.initialize()
      setAudioContext(manager.getContext())
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
      throw error
    }
  }, [])

  // Resume audio context
  const resume = useCallback(async () => {
    try {
      const manager = audioContextManagerRef.current
      await manager.resume()
    } catch (error) {
      console.error('Failed to resume audio context:', error)
      throw error
    }
  }, [])

  // Suspend audio context
  const suspend = useCallback(async () => {
    try {
      const manager = audioContextManagerRef.current
      await manager.suspend()
    } catch (error) {
      console.error('Failed to suspend audio context:', error)
      throw error
    }
  }, [])

  // Close audio context
  const close = useCallback(async () => {
    try {
      const manager = audioContextManagerRef.current
      await manager.close()
      setAudioContext(null)
    } catch (error) {
      console.error('Failed to close audio context:', error)
      throw error
    }
  }, [])

  // Set master volume
  const setMasterVolume = useCallback((volume: number) => {
    const manager = audioContextManagerRef.current
    manager.setMasterVolume(volume)
  }, [])

  // Get current time
  const getCurrentTime = useCallback(() => {
    const manager = audioContextManagerRef.current
    return manager.getCurrentTime()
  }, [])

  // Get sample rate
  const getSampleRate = useCallback(() => {
    const manager = audioContextManagerRef.current
    return manager.getSampleRate()
  }, [])

  // Get info
  const getInfo = useCallback(() => {
    const manager = audioContextManagerRef.current
    return manager.getInfo()
  }, [])

  return {
    audioContext,
    state,
    isReady,
    initialize,
    resume,
    suspend,
    close,
    setMasterVolume,
    getCurrentTime,
    getSampleRate,
    getInfo
  }
}

/**
 * useAudioContextAutoResume - Automatically resume audio context on user interaction
 */
export function useAudioContextAutoResume(audioContextHook: UseAudioContextReturn) {
  const hasResumedRef = useRef(false)

  useEffect(() => {
    if (hasResumedRef.current || audioContextHook.state === 'running') {
      return
    }

    const handleUserInteraction = async () => {
      if (!hasResumedRef.current && audioContextHook.state === 'suspended') {
        try {
          await audioContextHook.resume()
          hasResumedRef.current = true
          console.log('Audio context auto-resumed on user interaction')
        } catch (error) {
          console.error('Failed to auto-resume audio context:', error)
        }
      }
    }

    // Add event listeners for user interaction
    const events = ['click', 'touchstart', 'keydown']
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction)
      })
    }
  }, [audioContextHook.state, audioContextHook.resume])
}

/**
 * useAudioContextVisibility - Suspend/resume audio context based on page visibility
 */
export function useAudioContextVisibility(
  audioContextHook: UseAudioContextReturn,
  enabled: boolean = true
) {
  useEffect(() => {
    if (!enabled) {
      return
    }

    const handleVisibilityChange = async () => {
      if (document.hidden) {
        // Page is hidden, suspend audio context to save resources
        if (audioContextHook.state === 'running') {
          try {
            await audioContextHook.suspend()
            console.log('Audio context suspended due to page visibility')
          } catch (error) {
            console.error('Failed to suspend audio context on visibility change:', error)
          }
        }
      } else {
        // Page is visible, resume audio context
        if (audioContextHook.state === 'suspended') {
          try {
            await audioContextHook.resume()
            console.log('Audio context resumed due to page visibility')
          } catch (error) {
            console.error('Failed to resume audio context on visibility change:', error)
          }
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [audioContextHook.state, audioContextHook.suspend, audioContextHook.resume, enabled])
}

/**
 * useAudioPerformanceMonitor - Monitor audio context performance
 */
export function useAudioPerformanceMonitor(audioContextHook: UseAudioContextReturn) {
  const [performanceInfo, setPerformanceInfo] = useState({
    currentTime: 0,
    baseLatency: 0,
    outputLatency: 0,
    sampleRate: 0,
    state: 'suspended' as AudioContextState
  })

  useEffect(() => {
    if (!audioContextHook.audioContext) {
      return
    }

    const updatePerformanceInfo = () => {
      const context = audioContextHook.audioContext
      if (context) {
        setPerformanceInfo({
          currentTime: context.currentTime,
          baseLatency: context.baseLatency,
          outputLatency: context.outputLatency,
          sampleRate: context.sampleRate,
          state: audioContextHook.state
        })
      }
    }

    // Update immediately
    updatePerformanceInfo()

    // Update periodically
    const interval = setInterval(updatePerformanceInfo, 1000)

    return () => {
      clearInterval(interval)
    }
  }, [audioContextHook.audioContext, audioContextHook.state])

  return performanceInfo
}
