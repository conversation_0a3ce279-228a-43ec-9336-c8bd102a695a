/**
 * useAudioNode - React hook for managing individual audio nodes
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { BaseAudioNode, AudioParameter } from '@/lib/audio/base-audio-node'

export interface UseAudioNodeReturn {
  audioNode: BaseAudioNode | null
  isInitialized: boolean
  parameters: Record<string, AudioParameter>
  setParameter: (name: string, value: number, rampTime?: number) => void
  getParameter: (name: string) => number | undefined
  setEnabled: (enabled: boolean) => void
  setBypassed: (bypassed: boolean) => void
  connect: (targetNode: BaseAudioNode, outputIndex?: number, inputIndex?: number) => void
  disconnect: (targetNode?: BaseAudioNode, outputIndex?: number, inputIndex?: number) => void
  getInfo: () => object
}

export function useAudioNode(audioNode: BaseAudioNode | null): UseAudioNodeReturn {
  const [isInitialized, setIsInitialized] = useState(false)
  const [parameters, setParameters] = useState<Record<string, AudioParameter>>({})
  
  const audioNodeRef = useRef<BaseAudioNode | null>(null)

  // Update ref when audioNode changes
  useEffect(() => {
    audioNodeRef.current = audioNode
    
    if (audioNode) {
      setIsInitialized(audioNode.getInfo().isInitialized as boolean)
      setParameters(audioNode.getParameters())
    } else {
      setIsInitialized(false)
      setParameters({})
    }
  }, [audioNode])

  // Set parameter value
  const setParameter = useCallback((name: string, value: number, rampTime = 0) => {
    const node = audioNodeRef.current
    if (node) {
      node.setParameter(name, value, rampTime)
      // Update local parameters state
      setParameters(prev => {
        const param = prev[name]
        if (param) {
          return {
            ...prev,
            [name]: { ...param, value }
          }
        }
        return prev
      })
    }
  }, [])

  // Get parameter value
  const getParameter = useCallback((name: string): number | undefined => {
    const node = audioNodeRef.current
    return node?.getParameter(name)
  }, [])

  // Set enabled state
  const setEnabled = useCallback((enabled: boolean) => {
    const node = audioNodeRef.current
    if (node) {
      node.setEnabled(enabled)
    }
  }, [])

  // Set bypassed state
  const setBypassed = useCallback((bypassed: boolean) => {
    const node = audioNodeRef.current
    if (node) {
      node.setBypassed(bypassed)
    }
  }, [])

  // Connect to another node
  const connect = useCallback((
    targetNode: BaseAudioNode,
    outputIndex = 0,
    inputIndex = 0
  ) => {
    const node = audioNodeRef.current
    if (node) {
      node.connect(targetNode, outputIndex, inputIndex)
    }
  }, [])

  // Disconnect from another node
  const disconnect = useCallback((
    targetNode?: BaseAudioNode,
    outputIndex = 0,
    inputIndex = 0
  ) => {
    const node = audioNodeRef.current
    if (node) {
      node.disconnect(targetNode, outputIndex, inputIndex)
    }
  }, [])

  // Get node info
  const getInfo = useCallback(() => {
    const node = audioNodeRef.current
    return node?.getInfo() || {}
  }, [])

  return {
    audioNode,
    isInitialized,
    parameters,
    setParameter,
    getParameter,
    setEnabled,
    setBypassed,
    connect,
    disconnect,
    getInfo
  }
}

/**
 * useAudioNodeParameter - Hook for managing a specific parameter
 */
export function useAudioNodeParameter(
  audioNodeHook: UseAudioNodeReturn,
  parameterName: string
) {
  const [value, setValue] = useState<number>(0)
  const [parameter, setParameter] = useState<AudioParameter | null>(null)

  // Update local state when parameter changes
  useEffect(() => {
    const param = audioNodeHook.parameters[parameterName]
    if (param) {
      setParameter(param)
      setValue(param.value)
    } else {
      setParameter(null)
      setValue(0)
    }
  }, [audioNodeHook.parameters, parameterName])

  // Update parameter value
  const updateValue = useCallback((newValue: number, rampTime = 0) => {
    audioNodeHook.setParameter(parameterName, newValue, rampTime)
    setValue(newValue)
  }, [audioNodeHook, parameterName])

  return {
    value,
    parameter,
    setValue: updateValue
  }
}

/**
 * useAudioNodeConnection - Hook for managing connections between nodes
 */
export function useAudioNodeConnection(
  sourceNode: BaseAudioNode | null,
  targetNode: BaseAudioNode | null,
  outputIndex = 0,
  inputIndex = 0
) {
  const [isConnected, setIsConnected] = useState(false)
  
  const sourceNodeRef = useRef<BaseAudioNode | null>(null)
  const targetNodeRef = useRef<BaseAudioNode | null>(null)

  // Update refs
  useEffect(() => {
    sourceNodeRef.current = sourceNode
    targetNodeRef.current = targetNode
  }, [sourceNode, targetNode])

  // Connect nodes
  const connect = useCallback(() => {
    const source = sourceNodeRef.current
    const target = targetNodeRef.current
    
    if (source && target && !isConnected) {
      try {
        source.connect(target, outputIndex, inputIndex)
        setIsConnected(true)
        console.log(`Connected ${source.getId()}[${outputIndex}] -> ${target.getId()}[${inputIndex}]`)
      } catch (error) {
        console.error('Failed to connect audio nodes:', error)
      }
    }
  }, [isConnected, outputIndex, inputIndex])

  // Disconnect nodes
  const disconnect = useCallback(() => {
    const source = sourceNodeRef.current
    const target = targetNodeRef.current
    
    if (source && target && isConnected) {
      try {
        source.disconnect(target, outputIndex, inputIndex)
        setIsConnected(false)
        console.log(`Disconnected ${source.getId()}[${outputIndex}] -> ${target.getId()}[${inputIndex}]`)
      } catch (error) {
        console.error('Failed to disconnect audio nodes:', error)
      }
    }
  }, [isConnected, outputIndex, inputIndex])

  // Auto-connect when both nodes are available
  useEffect(() => {
    if (sourceNode && targetNode && !isConnected) {
      connect()
    }
  }, [sourceNode, targetNode, isConnected, connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isConnected) {
        disconnect()
      }
    }
  }, [isConnected, disconnect])

  return {
    isConnected,
    connect,
    disconnect
  }
}

/**
 * useAudioNodeAnalyzer - Hook for real-time audio analysis
 */
export function useAudioNodeAnalyzer(
  audioNode: BaseAudioNode | null,
  fftSize: number = 2048
) {
  const [frequencyData, setFrequencyData] = useState<Uint8Array>(new Uint8Array(fftSize / 2))
  const [timeData, setTimeData] = useState<Uint8Array>(new Uint8Array(fftSize))
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  
  const analyzerRef = useRef<AnalyserNode | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  // Create analyzer node
  useEffect(() => {
    if (audioNode) {
      const audioContext = audioNode.getInfo().audioContext as AudioContext
      if (audioContext) {
        const analyzer = audioContext.createAnalyser()
        analyzer.fftSize = fftSize
        analyzer.smoothingTimeConstant = 0.8
        
        // Connect to audio node (this would need to be implemented in BaseAudioNode)
        // For now, we'll assume the audio node has a method to get its output
        
        analyzerRef.current = analyzer
        setFrequencyData(new Uint8Array(analyzer.frequencyBinCount))
        setTimeData(new Uint8Array(analyzer.fftSize))
      }
    }

    return () => {
      if (analyzerRef.current) {
        analyzerRef.current.disconnect()
        analyzerRef.current = null
      }
    }
  }, [audioNode, fftSize])

  // Start analysis
  const startAnalysis = useCallback(() => {
    if (analyzerRef.current && !isAnalyzing) {
      setIsAnalyzing(true)
      
      const analyze = () => {
        const analyzer = analyzerRef.current
        if (analyzer && isAnalyzing) {
          analyzer.getByteFrequencyData(frequencyData)
          analyzer.getByteTimeDomainData(timeData)
          
          setFrequencyData(new Uint8Array(frequencyData))
          setTimeData(new Uint8Array(timeData))
          
          animationFrameRef.current = requestAnimationFrame(analyze)
        }
      }
      
      analyze()
    }
  }, [isAnalyzing, frequencyData, timeData])

  // Stop analysis
  const stopAnalysis = useCallback(() => {
    setIsAnalyzing(false)
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAnalysis()
    }
  }, [stopAnalysis])

  return {
    frequencyData,
    timeData,
    isAnalyzing,
    startAnalysis,
    stopAnalysis
  }
}
