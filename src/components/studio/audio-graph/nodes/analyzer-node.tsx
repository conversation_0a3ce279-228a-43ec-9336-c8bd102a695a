"use client"

import React, { useC<PERSON>back, useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Activity, BarChart3, Waveform, Pause, Play } from 'lucide-react'

interface AnalyzerNodeData {
  analyzerType: 'spectrum' | 'waveform' | 'level' | 'phase'
  fftSize: 256 | 512 | 1024 | 2048 | 4096
  smoothing: number
  enabled: boolean
  freeze: boolean
}

export function AnalyzerNode({ data, selected }: NodeProps<AnalyzerNodeData>) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [mockData, setMockData] = useState<number[]>([])

  const handleAnalyzerTypeChange = useCallback((analyzerType: string) => {
    data.analyzerType = analyzerType as AnalyzerNodeData['analyzerType']
  }, [data])

  const handleFFTSizeChange = useCallback((fftSize: string) => {
    data.fftSize = parseInt(fftSize) as AnalyzerNodeData['fftSize']
  }, [data])

  const handleEnabledToggle = useCallback(() => {
    data.enabled = !data.enabled
  }, [data])

  const handleFreezeToggle = useCallback(() => {
    data.freeze = !data.freeze
  }, [data])

  // Generate mock data for visualization
  useEffect(() => {
    const generateMockData = () => {
      const size = data.fftSize / 4 // Reduce for display
      const newData = Array.from({ length: size }, (_, i) => {
        if (data.freeze) return mockData[i] || 0
        
        switch (data.analyzerType) {
          case 'spectrum':
            // Simulate frequency spectrum
            return Math.random() * 100 * Math.exp(-i / 20)
          case 'waveform':
            // Simulate waveform
            return Math.sin(i * 0.1) * 50 + Math.random() * 20
          case 'level':
            // Simulate level meter
            return Math.random() * 100
          case 'phase':
            // Simulate phase correlation
            return (Math.random() - 0.5) * 100
          default:
            return 0
        }
      })
      
      if (!data.freeze) {
        setMockData(newData)
      }
    }

    if (data.enabled) {
      const interval = setInterval(generateMockData, 50)
      return () => clearInterval(interval)
    }
  }, [data.enabled, data.freeze, data.analyzerType, data.fftSize, mockData])

  // Canvas drawing
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !data.enabled) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const draw = () => {
      const { width, height } = canvas
      ctx.clearRect(0, 0, width, height)

      if (mockData.length === 0) return

      ctx.strokeStyle = '#3b82f6'
      ctx.lineWidth = 2
      ctx.beginPath()

      switch (data.analyzerType) {
        case 'spectrum':
          // Draw frequency spectrum as bars
          const barWidth = width / mockData.length
          mockData.forEach((value, i) => {
            const barHeight = (value / 100) * height
            ctx.fillStyle = `hsl(${240 - (value / 100) * 60}, 70%, 50%)`
            ctx.fillRect(i * barWidth, height - barHeight, barWidth - 1, barHeight)
          })
          break

        case 'waveform':
          // Draw waveform
          mockData.forEach((value, i) => {
            const x = (i / mockData.length) * width
            const y = height / 2 + (value / 100) * (height / 2)
            if (i === 0) {
              ctx.moveTo(x, y)
            } else {
              ctx.lineTo(x, y)
            }
          })
          ctx.stroke()
          break

        case 'level':
          // Draw level meters
          const avgLevel = mockData.reduce((a, b) => a + b, 0) / mockData.length
          const levelHeight = (avgLevel / 100) * height
          
          // Left channel
          ctx.fillStyle = avgLevel > 80 ? '#ef4444' : avgLevel > 60 ? '#f59e0b' : '#10b981'
          ctx.fillRect(10, height - levelHeight, 20, levelHeight)
          
          // Right channel (slightly different)
          const rightLevel = avgLevel * (0.8 + Math.random() * 0.4)
          const rightHeight = (rightLevel / 100) * height
          ctx.fillStyle = rightLevel > 80 ? '#ef4444' : rightLevel > 60 ? '#f59e0b' : '#10b981'
          ctx.fillRect(40, height - rightHeight, 20, rightHeight)
          break

        case 'phase':
          // Draw phase correlation
          const centerX = width / 2
          const centerY = height / 2
          mockData.forEach((value, i) => {
            const angle = (i / mockData.length) * Math.PI * 2
            const radius = Math.abs(value / 100) * Math.min(width, height) / 3
            const x = centerX + Math.cos(angle) * radius
            const y = centerY + Math.sin(angle) * radius
            
            ctx.fillStyle = value > 0 ? '#10b981' : '#ef4444'
            ctx.fillRect(x - 1, y - 1, 2, 2)
          })
          break
      }

      if (!data.freeze) {
        animationRef.current = requestAnimationFrame(draw)
      }
    }

    draw()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [mockData, data.analyzerType, data.enabled, data.freeze])

  const getAnalyzerIcon = () => {
    switch (data.analyzerType) {
      case 'spectrum':
        return <BarChart3 className="w-5 h-5 text-primary" />
      case 'waveform':
        return <Waveform className="w-5 h-5 text-primary" />
      case 'level':
      case 'phase':
        return <Activity className="w-5 h-5 text-primary" />
      default:
        return <Activity className="w-5 h-5 text-primary" />
    }
  }

  return (
    <Card className={cn(
      "w-72 transition-all duration-200",
      selected && "ring-2 ring-primary",
      !data.enabled && "opacity-60"
    )}>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary"
      />

      {/* Pass-through Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          {getAnalyzerIcon()}
          Analyzer
        </CardTitle>
        <div className="flex items-center gap-1">
          <Badge 
            variant={data.enabled ? "default" : "secondary"} 
            className="text-xs"
          >
            {data.analyzerType.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {data.fftSize}
          </Badge>
          {data.freeze && (
            <Badge variant="outline" className="text-xs">
              FROZEN
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Analyzer Type Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Analyzer Type</label>
          <Select value={data.analyzerType} onValueChange={handleAnalyzerTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="spectrum">Frequency Spectrum</SelectItem>
              <SelectItem value="waveform">Waveform</SelectItem>
              <SelectItem value="level">Level Meter</SelectItem>
              <SelectItem value="phase">Phase Correlation</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* FFT Size Selection */}
        {(data.analyzerType === 'spectrum' || data.analyzerType === 'waveform') && (
          <div className="space-y-2">
            <label className="text-sm font-medium">FFT Size</label>
            <Select value={data.fftSize.toString()} onValueChange={handleFFTSizeChange}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="256">256</SelectItem>
                <SelectItem value="512">512</SelectItem>
                <SelectItem value="1024">1024</SelectItem>
                <SelectItem value="2048">2048</SelectItem>
                <SelectItem value="4096">4096</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Visualization Canvas */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">Visualization</label>
            <div className="flex gap-1">
              <Button
                variant={data.enabled ? "default" : "outline"}
                size="sm"
                onClick={handleEnabledToggle}
                className="w-8 h-8 p-0"
              >
                {data.enabled ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
              </Button>
              <Button
                variant={data.freeze ? "default" : "outline"}
                size="sm"
                onClick={handleFreezeToggle}
                className="text-xs px-2"
              >
                FREEZE
              </Button>
            </div>
          </div>
          
          <div className="border rounded-lg p-2 bg-muted/20">
            <canvas
              ref={canvasRef}
              width={240}
              height={120}
              className="w-full h-[120px] bg-background rounded"
            />
          </div>
        </div>

        {/* Info Display */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div className="flex justify-between">
            <span>Sample Rate:</span>
            <span>44.1 kHz</span>
          </div>
          <div className="flex justify-between">
            <span>Bit Depth:</span>
            <span>24-bit</span>
          </div>
          <div className="flex justify-between">
            <span>Latency:</span>
            <span>2.3 ms</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
