"use client"

import React, { useCallback } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Volume2,
  VolumeX,
  Headphones,
  Sliders,
  RotateCcw,
  Zap
} from 'lucide-react'

interface ChannelMixerNodeData {
  channel: 'A' | 'B'
  volume: number
  highEQ: number
  midEQ: number
  lowEQ: number
  highKill: boolean
  midKill: boolean
  lowKill: boolean
  cue: boolean
  crossfaderAssign: 'A' | 'B' | 'center'
  gain: number
}

export function ChannelMixerNode({ data, selected }: NodeProps<ChannelMixerNodeData>) {
  const handleVolumeChange = useCallback((value: number[]) => {
    data.volume = value[0] / 100
  }, [data])

  const handleGainChange = useCallback((value: number[]) => {
    data.gain = value[0] / 100
  }, [data])

  const handleEQChange = useCallback((band: 'high' | 'mid' | 'low', value: number[]) => {
    data[`${band}EQ` as keyof ChannelMixerNodeData] = value[0] as never
  }, [data])

  const handleKillToggle = useCallback((band: 'high' | 'mid' | 'low') => {
    data[`${band}Kill` as keyof ChannelMixerNodeData] = !data[`${band}Kill` as keyof ChannelMixerNodeData] as never
  }, [data])

  const handleCueToggle = useCallback(() => {
    data.cue = !data.cue
  }, [data])

  const handleCrossfaderAssign = useCallback((assign: 'A' | 'B' | 'center') => {
    data.crossfaderAssign = assign
  }, [data])

  const handleReset = useCallback(() => {
    data.highEQ = 0
    data.midEQ = 0
    data.lowEQ = 0
    data.highKill = false
    data.midKill = false
    data.lowKill = false
    data.volume = 0.8
    data.gain = 0.8
  }, [data])

  const getChannelColor = () => {
    return data.channel === 'A' ? 'blue' : 'red'
  }

  return (
    <Card className={cn(
      "w-48 transition-all duration-200",
      selected && "ring-2 ring-primary"
    )}>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className={cn(
          "w-3 h-3",
          data.channel === 'A' ? "bg-blue-500" : "bg-red-500"
        )}
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Sliders className="w-5 h-5 text-primary" />
          Channel {data.channel}
        </CardTitle>
        <div className="flex items-center gap-1">
          <Badge 
            variant="outline" 
            className={cn(
              "text-xs",
              data.channel === 'A' ? "border-blue-500 text-blue-500" : "border-red-500 text-red-500"
            )}
          >
            {data.crossfaderAssign.toUpperCase()}
          </Badge>
          {data.cue && (
            <Badge variant="secondary" className="text-xs">CUE</Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Gain Control */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">GAIN</span>
            <span>{Math.round(data.gain * 100)}</span>
          </div>
          <Slider
            value={[data.gain * 100]}
            onValueChange={handleGainChange}
            min={0}
            max={150}
            step={1}
            className="w-full"
          />
        </div>

        {/* EQ Section */}
        <div className="space-y-3">
          <div className="text-xs font-medium text-center">EQUALIZER</div>
          
          {/* High EQ */}
          <div className="space-y-1">
            <div className="flex justify-between items-center text-xs">
              <span>HI</span>
              <div className="flex items-center gap-1">
                <span>{data.highEQ > 0 ? '+' : ''}{data.highEQ.toFixed(1)}</span>
                <Button
                  variant={data.highKill ? "destructive" : "ghost"}
                  size="sm"
                  className="w-6 h-6 p-0 text-xs"
                  onClick={() => handleKillToggle('high')}
                >
                  K
                </Button>
              </div>
            </div>
            <Slider
              value={[data.highEQ]}
              onValueChange={(value) => handleEQChange('high', value)}
              min={-20}
              max={20}
              step={0.1}
              className="w-full"
              disabled={data.highKill}
            />
          </div>

          {/* Mid EQ */}
          <div className="space-y-1">
            <div className="flex justify-between items-center text-xs">
              <span>MID</span>
              <div className="flex items-center gap-1">
                <span>{data.midEQ > 0 ? '+' : ''}{data.midEQ.toFixed(1)}</span>
                <Button
                  variant={data.midKill ? "destructive" : "ghost"}
                  size="sm"
                  className="w-6 h-6 p-0 text-xs"
                  onClick={() => handleKillToggle('mid')}
                >
                  K
                </Button>
              </div>
            </div>
            <Slider
              value={[data.midEQ]}
              onValueChange={(value) => handleEQChange('mid', value)}
              min={-20}
              max={20}
              step={0.1}
              className="w-full"
              disabled={data.midKill}
            />
          </div>

          {/* Low EQ */}
          <div className="space-y-1">
            <div className="flex justify-between items-center text-xs">
              <span>LOW</span>
              <div className="flex items-center gap-1">
                <span>{data.lowEQ > 0 ? '+' : ''}{data.lowEQ.toFixed(1)}</span>
                <Button
                  variant={data.lowKill ? "destructive" : "ghost"}
                  size="sm"
                  className="w-6 h-6 p-0 text-xs"
                  onClick={() => handleKillToggle('low')}
                >
                  K
                </Button>
              </div>
            </div>
            <Slider
              value={[data.lowEQ]}
              onValueChange={(value) => handleEQChange('low', value)}
              min={-20}
              max={20}
              step={0.1}
              className="w-full"
              disabled={data.lowKill}
            />
          </div>
        </div>

        {/* Volume Fader */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">VOLUME</span>
            <span>{Math.round(data.volume * 100)}</span>
          </div>
          <div className="flex justify-center">
            <Slider
              value={[data.volume * 100]}
              onValueChange={handleVolumeChange}
              min={0}
              max={100}
              step={1}
              orientation="vertical"
              className="h-20"
            />
          </div>
        </div>

        {/* Controls */}
        <div className="space-y-2">
          {/* Cue Button */}
          <Button
            variant={data.cue ? "default" : "outline"}
            size="sm"
            onClick={handleCueToggle}
            className="w-full gap-1"
          >
            <Headphones className="w-3 h-3" />
            CUE
          </Button>

          {/* Crossfader Assignment */}
          <div className="flex gap-1">
            {(['A', 'center', 'B'] as const).map((assign) => (
              <Button
                key={assign}
                variant={data.crossfaderAssign === assign ? "default" : "outline"}
                size="sm"
                onClick={() => handleCrossfaderAssign(assign)}
                className="flex-1 text-xs"
              >
                {assign === 'center' ? 'C' : assign}
              </Button>
            ))}
          </div>

          {/* Reset Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="w-full gap-1"
          >
            <RotateCcw className="w-3 h-3" />
            Reset
          </Button>
        </div>

        {/* Level Meter */}
        <div className="space-y-1">
          <div className="text-xs text-center text-muted-foreground">LEVEL</div>
          <div className="flex justify-center">
            <div className="w-3 h-16 bg-muted rounded-full relative overflow-hidden">
              <div 
                className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500 transition-all"
                style={{ 
                  height: `${Math.random() * 80 + 20}%` // Mock level meter
                }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
