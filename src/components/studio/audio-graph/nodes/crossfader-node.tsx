"use client"

import React, { useCallback } from 'react'
import { Handle, Position, NodeProps, Node } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Shuffle, RotateCcw } from 'lucide-react'

interface CrossfaderNodeData {
  position: number // -1 to 1 (A to B)
  curve: 'linear' | 'logarithmic' | 'exponential'
  cutMode: 'fade' | 'cut' | 'scratch'
  hamsterSwitch: boolean
}

type CrossfaderNode = Node<CrossfaderNodeData>

export function CrossfaderNode({ data, selected }: NodeProps<CrossfaderNode>) {
  const handlePositionChange = useCallback((value: number[]) => {
    data.position = value[0]
  }, [data])

  const handleCurveChange = useCallback((curve: string) => {
    data.curve = curve as CrossfaderNodeData['curve']
  }, [data])

  const handleCutModeChange = useCallback((mode: string) => {
    data.cutMode = mode as CrossfaderNodeData['cutMode']
  }, [data])

  const handleHamsterToggle = useCallback(() => {
    data.hamsterSwitch = !data.hamsterSwitch
  }, [data])

  const handleReset = useCallback(() => {
    data.position = 0
  }, [data])

  const getPositionLabel = () => {
    if (data.position < -0.1) return 'A'
    if (data.position > 0.1) return 'B'
    return 'CENTER'
  }

  const getPositionPercentage = () => {
    return Math.round(((data.position + 1) / 2) * 100)
  }

  return (
    <Card className={cn(
      "w-64 transition-all duration-200",
      selected && "ring-2 ring-primary"
    )}>
      {/* Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="input-a"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-blue-500"
      />
      <Handle
        type="target"
        position={Position.Left}
        id="input-b"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-red-500"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Shuffle className="w-5 h-5 text-primary" />
          Crossfader
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {data.curve.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {data.cutMode.toUpperCase()}
          </Badge>
          {data.hamsterSwitch && (
            <Badge variant="secondary" className="text-xs">
              HAMSTER
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Position Display */}
        <div className="text-center space-y-2">
          <div className="text-2xl font-bold">
            {getPositionLabel()}
          </div>
          <div className="text-sm text-muted-foreground">
            {getPositionPercentage()}%
          </div>
        </div>

        {/* Crossfader Slider */}
        <div className="space-y-3">
          <div className="relative">
            <Slider
              value={[data.position]}
              onValueChange={handlePositionChange}
              min={-1}
              max={1}
              step={0.01}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span className="text-blue-500 font-medium">A</span>
              <span>CENTER</span>
              <span className="text-red-500 font-medium">B</span>
            </div>
          </div>

          {/* Reset Button */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="gap-1"
            >
              <RotateCcw className="w-3 h-3" />
              Reset
            </Button>
          </div>
        </div>

        {/* Curve Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Crossfader Curve</label>
          <Select value={data.curve} onValueChange={handleCurveChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="linear">Linear</SelectItem>
              <SelectItem value="logarithmic">Logarithmic</SelectItem>
              <SelectItem value="exponential">Exponential</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Cut Mode Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Cut Mode</label>
          <Select value={data.cutMode} onValueChange={handleCutModeChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fade">Smooth Fade</SelectItem>
              <SelectItem value="cut">Instant Cut</SelectItem>
              <SelectItem value="scratch">Scratch Mode</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Hamster Switch */}
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">Hamster Mode</label>
          <Button
            variant={data.hamsterSwitch ? "default" : "outline"}
            size="sm"
            onClick={handleHamsterToggle}
            className="text-xs"
          >
            {data.hamsterSwitch ? 'ON' : 'OFF'}
          </Button>
        </div>

        {/* Visual Indicator */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground text-center">Mix Balance</div>
          <div className="flex h-2 rounded-full overflow-hidden bg-muted">
            <div 
              className="bg-blue-500 transition-all duration-150"
              style={{ width: `${Math.max(0, (1 - data.position) * 50)}%` }}
            />
            <div 
              className="bg-red-500 transition-all duration-150"
              style={{ width: `${Math.max(0, (1 + data.position) * 50)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Deck A</span>
            <span>Deck B</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
