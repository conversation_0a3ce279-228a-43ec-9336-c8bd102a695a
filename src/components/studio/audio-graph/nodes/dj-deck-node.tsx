"use client"

import React, { useState, useCallback } from 'react'
import { Handle, Position, NodeProps, Node } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  RotateCw,
  Disc3,
  Zap,
  Volume2,
  VolumeX,
  Headphones
} from 'lucide-react'

interface DJDeckNodeData {
  deckId: 'A' | 'B'
  track: any | null
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  tempo: number
  pitch: number
  bpm: number
  key: string
  cuePoints: any[]
  waveform: Float32Array | null
  isSync: boolean
  isCue: boolean
}

type DJDeckNode = Node<DJDeckNodeData>

export function DJDeckNode({ data, selected }: NodeProps<DJDeckNode>) {
  const [isDragging, setIsDragging] = useState(false)

  const handlePlay = useCallback(() => {
    // Update node data through ReactFlow
    data.isPlaying = !data.isPlaying
  }, [data])

  const handleCue = useCallback(() => {
    data.currentTime = 0
    data.isPlaying = false
  }, [data])

  const handleSync = useCallback(() => {
    data.isSync = !data.isSync
  }, [data])

  const handleVolumeChange = useCallback((value: number[]) => {
    data.volume = value[0] / 100
  }, [data])

  const handleTempoChange = useCallback((value: number[]) => {
    data.tempo = value[0]
  }, [data])

  const handlePitchBend = useCallback((direction: 'up' | 'down') => {
    const bendAmount = direction === 'up' ? 0.02 : -0.02
    data.pitch = Math.max(-0.1, Math.min(0.1, data.pitch + bendAmount))
  }, [data])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Card className={cn(
      "w-80 transition-all duration-200",
      selected && "ring-2 ring-primary",
      isDragging && "rotate-1 scale-105"
    )}>
      {/* Audio Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="audio-output"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Disc3 className="w-5 h-5 text-primary" />
            Deck {data.deckId}
          </CardTitle>
          <div className="flex items-center gap-1">
            {data.isSync && (
              <Badge variant="secondary" className="text-xs">SYNC</Badge>
            )}
            {data.isCue && (
              <Badge variant="outline" className="text-xs">CUE</Badge>
            )}
          </div>
        </div>
        
        {data.track ? (
          <CardDescription className="space-y-1">
            <div className="font-medium truncate">{data.track.title}</div>
            <div className="text-xs text-muted-foreground truncate">{data.track.artist}</div>
          </CardDescription>
        ) : (
          <CardDescription>No track loaded</CardDescription>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Track Info */}
        {data.track && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>{data.bpm} BPM</span>
              <span>{data.key}</span>
            </div>
            
            {/* Waveform Display */}
            <div className="h-12 bg-muted rounded relative overflow-hidden">
              {data.waveform ? (
                <canvas className="w-full h-full" />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-xs text-muted-foreground">
                  Loading waveform...
                </div>
              )}
              
              {/* Playhead */}
              {data.duration > 0 && (
                <div 
                  className="absolute top-0 bottom-0 w-px bg-primary z-10"
                  style={{ 
                    left: `${(data.currentTime / data.duration) * 100}%` 
                  }}
                />
              )}
            </div>

            {/* Progress */}
            <div className="space-y-1">
              <Progress value={data.duration > 0 ? (data.currentTime / data.duration) * 100 : 0} />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formatTime(data.currentTime)}</span>
                <span>{formatTime(data.duration)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Transport Controls */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCue}
            className="w-10 h-10"
          >
            <Square className="w-4 h-4" />
          </Button>
          
          <Button
            variant={data.isPlaying ? "default" : "outline"}
            size="sm"
            onClick={handlePlay}
            className="w-12 h-12"
          >
            {data.isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </Button>

          <Button
            variant={data.isSync ? "default" : "outline"}
            size="sm"
            onClick={handleSync}
            className="w-10 h-10"
          >
            <Zap className="w-4 h-4" />
          </Button>
        </div>

        {/* Pitch Bend */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onMouseDown={() => handlePitchBend('down')}
            className="flex-1"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
          <div className="text-xs text-center min-w-[60px]">
            {data.pitch > 0 ? '+' : ''}{(data.pitch * 100).toFixed(1)}%
          </div>
          <Button
            variant="outline"
            size="sm"
            onMouseDown={() => handlePitchBend('up')}
            className="flex-1"
          >
            <RotateCw className="w-4 h-4" />
          </Button>
        </div>

        {/* Tempo Control */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Tempo</span>
            <span>{((data.tempo - 1) * 100).toFixed(1)}%</span>
          </div>
          <Slider
            value={[data.tempo]}
            onValueChange={handleTempoChange}
            min={0.8}
            max={1.2}
            step={0.001}
            className="w-full"
          />
        </div>

        {/* Volume Control */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Volume</span>
            <span>{Math.round(data.volume * 100)}%</span>
          </div>
          <Slider
            value={[data.volume * 100]}
            onValueChange={handleVolumeChange}
            min={0}
            max={100}
            step={1}
            className="w-full"
          />
        </div>

        {/* Hot Cues */}
        <div className="grid grid-cols-4 gap-1">
          {[1, 2, 3, 4].map((cueNum) => (
            <Button
              key={cueNum}
              variant="outline"
              size="sm"
              className="h-8 text-xs"
              onClick={() => {
                // Handle cue point logic
              }}
            >
              {cueNum}
            </Button>
          ))}
        </div>

        {/* Additional Controls */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" className="text-xs">
            <Headphones className="w-3 h-3 mr-1" />
            CUE
          </Button>
          <Button variant="ghost" size="sm" className="text-xs">
            {data.volume > 0 ? (
              <Volume2 className="w-3 h-3" />
            ) : (
              <VolumeX className="w-3 h-3" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
