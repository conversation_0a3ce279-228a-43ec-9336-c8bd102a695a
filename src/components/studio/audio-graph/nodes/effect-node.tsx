"use client"

import React, { useCallback } from 'react'
import { <PERSON>le, Position, NodeProps } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Zap, RotateCcw, Settings } from 'lucide-react'

interface EffectNodeData {
  effectType: 'reverb' | 'delay' | 'filter' | 'distortion' | 'chorus' | 'phaser' | 'flanger'
  enabled: boolean
  parameters: Record<string, number>
  preset: string
  wetDryMix: number
}

const effectPresets = {
  reverb: {
    'Hall': { roomSize: 0.8, damping: 0.3, wetness: 0.4 },
    'Plate': { roomSize: 0.5, damping: 0.7, wetness: 0.3 },
    'Spring': { roomSize: 0.3, damping: 0.1, wetness: 0.2 }
  },
  delay: {
    'Eighth Note': { time: 0.125, feedback: 0.3, wetness: 0.25 },
    'Quarter Note': { time: 0.25, feedback: 0.4, wetness: 0.3 },
    'Dotted Eighth': { time: 0.1875, feedback: 0.35, wetness: 0.25 }
  },
  filter: {
    'Low Pass': { frequency: 1000, resonance: 0.5, type: 'lowpass' },
    'High Pass': { frequency: 200, resonance: 0.3, type: 'highpass' },
    'Band Pass': { frequency: 500, resonance: 0.7, type: 'bandpass' }
  }
}

export function EffectNode({ data, selected }: NodeProps<EffectNodeData>) {
  const handleEffectTypeChange = useCallback((effectType: string) => {
    data.effectType = effectType as EffectNodeData['effectType']
    // Reset parameters when effect type changes
    data.parameters = getDefaultParameters(effectType)
    data.preset = Object.keys(effectPresets[effectType as keyof typeof effectPresets] || {})[0] || ''
  }, [data])

  const handleParameterChange = useCallback((param: string, value: number[]) => {
    data.parameters[param] = value[0]
  }, [data])

  const handleWetDryChange = useCallback((value: number[]) => {
    data.wetDryMix = value[0] / 100
  }, [data])

  const handleEnabledToggle = useCallback(() => {
    data.enabled = !data.enabled
  }, [data])

  const handlePresetChange = useCallback((preset: string) => {
    data.preset = preset
    const presetData = effectPresets[data.effectType as keyof typeof effectPresets]?.[preset as keyof typeof presetData]
    if (presetData) {
      data.parameters = { ...presetData }
    }
  }, [data])

  const handleReset = useCallback(() => {
    data.parameters = getDefaultParameters(data.effectType)
    data.wetDryMix = 0.5
    data.enabled = true
  }, [data])

  const getDefaultParameters = (effectType: string) => {
    switch (effectType) {
      case 'reverb':
        return { roomSize: 0.5, damping: 0.5, wetness: 0.3 }
      case 'delay':
        return { time: 0.25, feedback: 0.3, wetness: 0.25 }
      case 'filter':
        return { frequency: 1000, resonance: 0.5 }
      case 'distortion':
        return { drive: 0.5, tone: 0.5, level: 0.8 }
      case 'chorus':
        return { rate: 0.5, depth: 0.3, feedback: 0.2 }
      case 'phaser':
        return { rate: 0.4, depth: 0.5, feedback: 0.3 }
      case 'flanger':
        return { rate: 0.3, depth: 0.4, feedback: 0.6 }
      default:
        return {}
    }
  }

  const getEffectIcon = () => {
    return <Zap className="w-5 h-5 text-primary" />
  }

  const renderParameters = () => {
    const params = Object.entries(data.parameters)
    
    return params.map(([param, value]) => (
      <div key={param} className="space-y-1">
        <div className="flex justify-between text-xs">
          <span className="capitalize">{param}</span>
          <span>{typeof value === 'number' ? value.toFixed(2) : value}</span>
        </div>
        <Slider
          value={[typeof value === 'number' ? value : 0]}
          onValueChange={(newValue) => handleParameterChange(param, newValue)}
          min={0}
          max={param === 'frequency' ? 20000 : 1}
          step={param === 'frequency' ? 10 : 0.01}
          className="w-full"
          disabled={!data.enabled}
        />
      </div>
    ))
  }

  return (
    <Card className={cn(
      "w-56 transition-all duration-200",
      selected && "ring-2 ring-primary",
      !data.enabled && "opacity-60"
    )}>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          {getEffectIcon()}
          Effect
        </CardTitle>
        <div className="flex items-center gap-1">
          <Badge 
            variant={data.enabled ? "default" : "secondary"} 
            className="text-xs"
          >
            {data.effectType.toUpperCase()}
          </Badge>
          {data.preset && (
            <Badge variant="outline" className="text-xs">
              {data.preset}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Enabled</span>
          <Switch
            checked={data.enabled}
            onCheckedChange={handleEnabledToggle}
          />
        </div>

        {/* Effect Type Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Effect Type</label>
          <Select value={data.effectType} onValueChange={handleEffectTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="reverb">Reverb</SelectItem>
              <SelectItem value="delay">Delay</SelectItem>
              <SelectItem value="filter">Filter</SelectItem>
              <SelectItem value="distortion">Distortion</SelectItem>
              <SelectItem value="chorus">Chorus</SelectItem>
              <SelectItem value="phaser">Phaser</SelectItem>
              <SelectItem value="flanger">Flanger</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Preset Selection */}
        {effectPresets[data.effectType as keyof typeof effectPresets] && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Preset</label>
            <Select value={data.preset} onValueChange={handlePresetChange}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.keys(effectPresets[data.effectType as keyof typeof effectPresets] || {}).map((preset) => (
                  <SelectItem key={preset} value={preset}>
                    {preset}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Wet/Dry Mix */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">Wet/Dry Mix</span>
            <span>{Math.round(data.wetDryMix * 100)}%</span>
          </div>
          <Slider
            value={[data.wetDryMix * 100]}
            onValueChange={handleWetDryChange}
            min={0}
            max={100}
            step={1}
            className="w-full"
            disabled={!data.enabled}
          />
        </div>

        {/* Effect Parameters */}
        <div className="space-y-3">
          <div className="text-sm font-medium">Parameters</div>
          {renderParameters()}
        </div>

        {/* Reset Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleReset}
          className="w-full gap-1"
        >
          <RotateCcw className="w-3 h-3" />
          Reset
        </Button>
      </CardContent>
    </Card>
  )
}
