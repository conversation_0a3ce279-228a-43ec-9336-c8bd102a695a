"use client"

import React, { use<PERSON><PERSON>back, useState } from 'react'
import { <PERSON>le, Position, NodeProps } from '@xyflow/react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Volume2,
  VolumeX,
  Headphones,
  Radio,
  Mic,
  MicOff,
  Speaker,
  Activity,
  Settings
} from 'lucide-react'

interface MasterOutputNodeData {
  masterVolume: number
  cueVolume: number
  isRecording: boolean
  isLive: boolean
  limiterEnabled: boolean
  limiterThreshold: number
  outputGain: number
  monitorMode: 'master' | 'cue' | 'split'
}

export function MasterOutputNode({ data, selected }: NodeProps<MasterOutputNodeData>) {
  const [peakLevels, setPeakLevels] = useState({ left: 0, right: 0 })

  const handleMasterVolumeChange = useCallback((value: number[]) => {
    data.masterVolume = value[0] / 100
  }, [data])

  const handleCueVolumeChange = useCallback((value: number[]) => {
    data.cueVolume = value[0] / 100
  }, [data])

  const handleOutputGainChange = useCallback((value: number[]) => {
    data.outputGain = value[0] / 100
  }, [data])

  const handleLimiterThresholdChange = useCallback((value: number[]) => {
    data.limiterThreshold = value[0]
  }, [data])

  const handleRecordingToggle = useCallback(() => {
    data.isRecording = !data.isRecording
  }, [data])

  const handleLiveToggle = useCallback(() => {
    data.isLive = !data.isLive
  }, [data])

  const handleLimiterToggle = useCallback(() => {
    data.limiterEnabled = !data.limiterEnabled
  }, [data])

  const handleMonitorModeChange = useCallback((mode: 'master' | 'cue' | 'split') => {
    data.monitorMode = mode
  }, [data])

  // Simulate peak levels (in real implementation, this would come from audio analysis)
  React.useEffect(() => {
    const interval = setInterval(() => {
      setPeakLevels({
        left: Math.random() * 100,
        right: Math.random() * 100
      })
    }, 100)

    return () => clearInterval(interval)
  }, [])

  const getLevelColor = (level: number) => {
    if (level > 90) return 'bg-red-500'
    if (level > 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <Card className={cn(
      "w-64 transition-all duration-200",
      selected && "ring-2 ring-primary"
    )}>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-primary"
      />

      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Speaker className="w-5 h-5 text-primary" />
          Master Output
        </CardTitle>
        <div className="flex items-center gap-1">
          {data.isRecording && (
            <Badge variant="destructive" className="text-xs animate-pulse">
              REC
            </Badge>
          )}
          {data.isLive && (
            <Badge variant="default" className="text-xs">
              LIVE
            </Badge>
          )}
          {data.limiterEnabled && (
            <Badge variant="outline" className="text-xs">
              LIMIT
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Master Level Meters */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-center">MASTER LEVELS</div>
          <div className="flex justify-center gap-2">
            {/* Left Channel */}
            <div className="flex flex-col items-center gap-1">
              <div className="text-xs">L</div>
              <div className="w-4 h-24 bg-muted rounded-full relative overflow-hidden">
                <div 
                  className={cn(
                    "absolute bottom-0 w-full transition-all duration-75",
                    getLevelColor(peakLevels.left)
                  )}
                  style={{ height: `${peakLevels.left}%` }}
                />
                {/* Peak indicators */}
                <div className="absolute top-1 left-0 right-0 h-px bg-red-500" />
                <div className="absolute top-6 left-0 right-0 h-px bg-yellow-500" />
              </div>
              <div className="text-xs">{Math.round(peakLevels.left)}</div>
            </div>

            {/* Right Channel */}
            <div className="flex flex-col items-center gap-1">
              <div className="text-xs">R</div>
              <div className="w-4 h-24 bg-muted rounded-full relative overflow-hidden">
                <div 
                  className={cn(
                    "absolute bottom-0 w-full transition-all duration-75",
                    getLevelColor(peakLevels.right)
                  )}
                  style={{ height: `${peakLevels.right}%` }}
                />
                {/* Peak indicators */}
                <div className="absolute top-1 left-0 right-0 h-px bg-red-500" />
                <div className="absolute top-6 left-0 right-0 h-px bg-yellow-500" />
              </div>
              <div className="text-xs">{Math.round(peakLevels.right)}</div>
            </div>
          </div>
        </div>

        {/* Master Volume */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">MASTER</span>
            <span>{Math.round(data.masterVolume * 100)}</span>
          </div>
          <Slider
            value={[data.masterVolume * 100]}
            onValueChange={handleMasterVolumeChange}
            min={0}
            max={100}
            step={1}
            className="w-full"
          />
        </div>

        {/* Cue Volume */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">CUE</span>
            <span>{Math.round(data.cueVolume * 100)}</span>
          </div>
          <Slider
            value={[data.cueVolume * 100]}
            onValueChange={handleCueVolumeChange}
            min={0}
            max={100}
            step={1}
            className="w-full"
          />
        </div>

        {/* Output Gain */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="font-medium">OUTPUT GAIN</span>
            <span>{Math.round(data.outputGain * 100)}</span>
          </div>
          <Slider
            value={[data.outputGain * 100]}
            onValueChange={handleOutputGainChange}
            min={0}
            max={150}
            step={1}
            className="w-full"
          />
        </div>

        {/* Limiter Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium">LIMITER</span>
            <Switch
              checked={data.limiterEnabled}
              onCheckedChange={handleLimiterToggle}
            />
          </div>
          {data.limiterEnabled && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Threshold</span>
                <span>{data.limiterThreshold.toFixed(1)} dB</span>
              </div>
              <Slider
                value={[data.limiterThreshold]}
                onValueChange={handleLimiterThresholdChange}
                min={-20}
                max={0}
                step={0.1}
                className="w-full"
              />
            </div>
          )}
        </div>

        {/* Monitor Mode */}
        <div className="space-y-2">
          <div className="text-xs font-medium">MONITOR</div>
          <div className="flex gap-1">
            {(['master', 'cue', 'split'] as const).map((mode) => (
              <Button
                key={mode}
                variant={data.monitorMode === mode ? "default" : "outline"}
                size="sm"
                onClick={() => handleMonitorModeChange(mode)}
                className="flex-1 text-xs"
              >
                {mode.toUpperCase()}
              </Button>
            ))}
          </div>
        </div>

        {/* Recording & Live Controls */}
        <div className="space-y-2">
          <Button
            variant={data.isRecording ? "destructive" : "outline"}
            size="sm"
            onClick={handleRecordingToggle}
            className="w-full gap-1"
          >
            {data.isRecording ? <MicOff className="w-3 h-3" /> : <Mic className="w-3 h-3" />}
            {data.isRecording ? 'Stop Recording' : 'Start Recording'}
          </Button>

          <Button
            variant={data.isLive ? "default" : "outline"}
            size="sm"
            onClick={handleLiveToggle}
            className="w-full gap-1"
          >
            <Radio className="w-3 h-3" />
            {data.isLive ? 'Stop Stream' : 'Go Live'}
          </Button>
        </div>

        {/* Status Indicators */}
        <div className="flex justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Activity className="w-3 h-3" />
            <span>44.1kHz</span>
          </div>
          <div className="flex items-center gap-1">
            <Settings className="w-3 h-3" />
            <span>24-bit</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
