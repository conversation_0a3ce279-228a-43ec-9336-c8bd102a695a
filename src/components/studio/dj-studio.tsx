"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable'
import { StudioTabs } from './shared/studio-tabs'
import { StudioToolbar } from './shared/studio-toolbar'
import { DJSidebar } from './dj/dj-sidebar'
import { DualDeckInterface } from './dj/dual-deck-interface'
import { DJMixer } from './dj/dj-mixer'
import { LiveCollaboration } from './dj/live-collaboration'
import { MoodContentBridge } from './dj/mood-content-bridge'
import { useDJSession } from '@/hooks/studio/dj/useDJSession'
import { usePlayerStore } from '@/lib/stores/enhanced-player-store'
import type { Track } from './types'

interface DJStudioProps {
  className?: string
  initialMood?: string
  discoveredTrack?: Track
}

export function DJStudio({ className, initialMood, discoveredTrack }: DJStudioProps) {
  const [activeTab, setActiveTab] = useState('decks')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showCollaboration, setShowCollaboration] = useState(false)
  const [isLiveStreaming, setIsLiveStreaming] = useState(false)
  
  const {
    session,
    deckA,
    deckB,
    crossfader,
    mixer,
    isRecording,
    collaborators,
    startSession,
    endSession,
    loadTrackToDeck,
    updateCrossfader,
    updateMixer,
    startRecording,
    stopRecording,
    inviteCollaborator,
    startLiveStream,
    stopLiveStream
  } = useDJSession()

  const playerStore = usePlayerStore()

  // Auto-load discovered track when component mounts
  useEffect(() => {
    if (discoveredTrack && !session) {
      startSession()
      loadTrackToDeck('A', discoveredTrack)
    }
  }, [discoveredTrack, session, startSession, loadTrackToDeck])

  const tabs = [
    {
      id: 'decks',
      label: 'DJ Decks',
      icon: 'Sliders' as const,
      component: (
        <DualDeckInterface
          deckA={deckA}
          deckB={deckB}
          crossfader={crossfader}
          onDeckUpdate={(_deck: string, _updates: any) => {
            // Update deck state
          }}
          onCrossfaderUpdate={updateCrossfader}
          onLoadTrack={(deckId: 'A' | 'B', track: Track) => loadTrackToDeck(deckId, track)}
        />
      )
    },
    {
      id: 'mixer',
      label: 'Mixer',
      icon: 'Sliders' as const,
      component: (
        <DJMixer
          mixer={mixer}
          deckA={deckA}
          deckB={deckB}
          onMixerUpdate={updateMixer}
        />
      )
    },
    {
      id: 'effects',
      label: 'Effects',
      icon: 'Plus' as const,
      component: (
        <div className="p-4">
          <h3>DJ Effects Rack</h3>
          {/* Effects interface will be implemented */}
        </div>
      )
    },
    {
      id: 'remix',
      label: 'Remix Tools',
      icon: 'Sparkles' as const,
      component: (
        <div className="p-4">
          <h3>Remix & Edit Tools</h3>
          {/* Remix tools will be implemented */}
        </div>
      )
    }
  ]

  return (
    <div className={cn("h-screen bg-background flex flex-col", className)}>
      {/* DJ Studio Toolbar */}
      <StudioToolbar
        project={{
          name: session?.name || 'DJ Session',
          modified: session?.modified || false,
          lastSaved: session?.lastSaved
        }}
        isPlaying={deckA.isPlaying || deckB.isPlaying}
        onPlay={() => {
          // Start playback on active deck
        }}
        onPause={() => {
          // Pause active deck
        }}
        onStop={() => {
          // Stop both decks
        }}
        onSave={() => {
          // Save DJ session
        }}
        onLoad={() => {
          // Load DJ session
        }}
        onExport={() => {
          // Export DJ mix
        }}
        onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
        onToggleEffectsRack={() => {}}
        onToggleAITools={() => setShowCollaboration(!showCollaboration)}
        sidebarCollapsed={sidebarCollapsed}
        showEffectsRack={true}
        showAITools={showCollaboration}
        // DJ-specific controls
        isRecording={isRecording}
        isLiveStreaming={isLiveStreaming}
        onStartRecording={startRecording}
        onStopRecording={stopRecording}
        onStartLiveStream={() => {
          startLiveStream()
          setIsLiveStreaming(true)
        }}
        onStopLiveStream={() => {
          stopLiveStream()
          setIsLiveStreaming(false)
        }}
        collaboratorCount={collaborators.length}
        onInviteCollaborator={inviteCollaborator}
      />

      {/* Main DJ Layout */}
      <div className="flex-1 flex">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Left Sidebar - Content Discovery & Library */}
          {!sidebarCollapsed && (
            <>
              <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                <DJSidebar
                  currentMood={initialMood}
                  onTrackSelect={(track: Track) => {
                    // Auto-detect which deck to load to
                    const targetDeck = !deckA.track ? 'A' : !deckB.track ? 'B' : 'A'
                    loadTrackToDeck(targetDeck, track)
                  }}
                  onMoodChange={(mood: string) => {
                    // Update content discovery based on mood
                  }}
                />
              </ResizablePanel>
              <ResizableHandle withHandle />
            </>
          )}

          {/* Main DJ Interface */}
          <ResizablePanel defaultSize={sidebarCollapsed ? 70 : 60}>
            <div className="h-full flex flex-col">
              <StudioTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
              <div className="flex-1 overflow-hidden">
                {tabs.find(tab => tab.id === activeTab)?.component}
              </div>
            </div>
          </ResizablePanel>

          {/* Right Panel - Collaboration & Social */}
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={20} minSize={15} maxSize={35}>
            <ResizablePanelGroup direction="vertical">
              {/* Mood Content Bridge */}
              <ResizablePanel defaultSize={40}>
                <MoodContentBridge
                  currentMood={initialMood}
                  onTrackDiscover={(track: Track) => {
                    // Handle discovered track from mood-based feed
                    const targetDeck = !deckA.track ? 'A' : !deckB.track ? 'B' : 'A'
                    loadTrackToDeck(targetDeck, track)
                  }}
                />
              </ResizablePanel>

              {showCollaboration && (
                <>
                  <ResizableHandle withHandle />
                  <ResizablePanel defaultSize={60}>
                    <LiveCollaboration
                      session={session}
                      collaborators={collaborators}
                      isLiveStreaming={isLiveStreaming}
                      onInviteUser={inviteCollaborator}
                      onKickUser={(userId: string) => {
                        // Remove collaborator
                      }}
                      onToggleUserPermissions={(userId: string, permissions: any) => {
                        // Update user permissions
                      }}
                    />
                  </ResizablePanel>
                </>
              )}
            </ResizablePanelGroup>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  )
}
