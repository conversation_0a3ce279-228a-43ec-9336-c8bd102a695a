"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Music,
  Search,
  Heart,
  Clock,
  TrendingUp,
  Disc3,
  Play,
  Plus,
  Filter,
  Shuffle,
  Radio,
  Headphones
} from 'lucide-react'

import type { Track } from '../types'

interface DJSidebarProps {
  currentMood?: string
  onTrackSelect: (track: Track) => void
  onMoodChange: (mood: string) => void
  className?: string
}

const MOODS = [
  { id: 'energetic', name: 'Energetic', color: '#ff6b6b', emoji: '⚡' },
  { id: 'chill', name: 'Chill', color: '#4ecdc4', emoji: '😌' },
  { id: 'dark', name: 'Dark', color: '#45b7d1', emoji: '🌙' },
  { id: 'uplifting', name: 'Uplifting', color: '#96ceb4', emoji: '☀️' },
  { id: 'melancholic', name: 'Melancholic', color: '#feca57', emoji: '💭' },
  { id: 'aggressive', name: 'Aggressive', color: '#ff9ff3', emoji: '🔥' },
  { id: 'peaceful', name: 'Peaceful', color: '#54a0ff', emoji: '🕊️' },
  { id: 'mysterious', name: 'Mysterious', color: '#5f27cd', emoji: '🔮' }
]

const GENRES = [
  'House', 'Techno', 'Trance', 'Drum & Bass', 'Dubstep', 'Hip Hop',
  'Pop', 'Rock', 'Electronic', 'Ambient', 'Jazz', 'Funk'
]

export function DJSidebar({
  currentMood = 'energetic',
  onTrackSelect,
  onMoodChange,
  className
}: DJSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedMood, setSelectedMood] = useState(currentMood)
  const [selectedGenre, setSelectedGenre] = useState<string>('')
  const [bpmRange, setBpmRange] = useState<[number, number]>([120, 140])
  const [keyFilter, setKeyFilter] = useState<string>('')
  const [tracks, setTracks] = useState<Track[]>([])
  const [loading, setLoading] = useState(false)

  // Mock tracks data - in real implementation, this would come from HVPPY's content API
  const mockTracks: Track[] = [
    {
      id: '1',
      title: 'Midnight Drive',
      artist: 'Synthwave Artist',
      duration: 240,
      bpm: 128,
      key: 'Am',
      genre: 'Electronic',
      mood: 'dark',
      energy: 0.7,
      danceability: 0.8,
      audioUrl: '/audio/track1.mp3',
      isLiked: true,
      playCount: 1250
    },
    {
      id: '2',
      title: 'Summer Vibes',
      artist: 'Tropical House DJ',
      duration: 195,
      bpm: 124,
      key: 'C',
      genre: 'House',
      mood: 'uplifting',
      energy: 0.9,
      danceability: 0.9,
      audioUrl: '/audio/track2.mp3',
      isLiked: false,
      playCount: 890
    },
    // Add more mock tracks...
  ]

  useEffect(() => {
    // Simulate API call to fetch tracks based on mood and filters
    setLoading(true)
    setTimeout(() => {
      const filteredTracks = mockTracks.filter(track => {
        const matchesMood = !selectedMood || track.mood === selectedMood
        const matchesGenre = !selectedGenre || track.genre === selectedGenre
        const matchesBPM = track.bpm >= bpmRange[0] && track.bpm <= bpmRange[1]
        const matchesKey = !keyFilter || track.key === keyFilter
        const matchesSearch = !searchQuery || 
          track.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          track.artist.toLowerCase().includes(searchQuery.toLowerCase())
        
        return matchesMood && matchesGenre && matchesBPM && matchesKey && matchesSearch
      })
      setTracks(filteredTracks)
      setLoading(false)
    }, 500)
  }, [selectedMood, selectedGenre, bpmRange, keyFilter, searchQuery])

  const handleMoodSelect = (mood: string) => {
    setSelectedMood(mood)
    onMoodChange(mood)
  }

  const renderTrackItem = (track: Track) => (
    <div
      key={track.id}
      className="group p-3 border rounded-lg cursor-pointer hover:bg-accent/50 transition-colors"
      onClick={() => onTrackSelect(track)}
      draggable
      onDragStart={(e) => {
        e.dataTransfer.setData('application/json', JSON.stringify(track))
      }}
    >
      <div className="flex items-center gap-3">
        {/* Artwork */}
        <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
          {track.artwork ? (
            <img src={track.artwork} alt={track.title} className="w-full h-full object-cover rounded" />
          ) : (
            <Music className="w-6 h-6 text-muted-foreground" />
          )}
        </div>

        {/* Track Info */}
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">{track.title}</div>
          <div className="text-xs text-muted-foreground truncate">{track.artist}</div>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="text-xs px-1">
              {track.bpm} BPM
            </Badge>
            <Badge variant="outline" className="text-xs px-1">
              {track.key}
            </Badge>
            <Badge 
              variant="outline" 
              className="text-xs px-1"
              style={{ 
                backgroundColor: MOODS.find(m => m.id === track.mood)?.color + '20',
                borderColor: MOODS.find(m => m.id === track.mood)?.color
              }}
            >
              {MOODS.find(m => m.id === track.mood)?.emoji}
            </Badge>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="ghost" size="sm" className="w-6 h-6 p-0">
            <Play className="w-3 h-3" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className={cn("w-6 h-6 p-0", track.isLiked && "text-red-500")}
          >
            <Heart className="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>
  )

  return (
    <div className={cn("h-full bg-card border-r flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <h2 className="font-semibold text-sm mb-3 flex items-center gap-2">
          <Disc3 className="w-4 h-4" />
          DJ Library
        </h2>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search tracks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-8 text-sm"
          />
        </div>

        {/* Mood Selector */}
        <div className="space-y-2">
          <label className="text-xs font-medium">Mood</label>
          <div className="grid grid-cols-2 gap-1">
            {MOODS.slice(0, 6).map((mood) => (
              <Button
                key={mood.id}
                variant={selectedMood === mood.id ? "default" : "outline"}
                size="sm"
                onClick={() => handleMoodSelect(mood.id)}
                className="h-8 text-xs justify-start"
                style={{
                  backgroundColor: selectedMood === mood.id ? mood.color : undefined,
                  borderColor: mood.color
                }}
              >
                <span className="mr-1">{mood.emoji}</span>
                {mood.name}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <Tabs defaultValue="discover" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 mx-4 mt-2">
          <TabsTrigger value="discover" className="text-xs">Discover</TabsTrigger>
          <TabsTrigger value="library" className="text-xs">Library</TabsTrigger>
          <TabsTrigger value="playlists" className="text-xs">Sets</TabsTrigger>
        </TabsList>

        <TabsContent value="discover" className="flex-1 flex flex-col mt-2">
          {/* Filters */}
          <div className="px-4 pb-2 space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <Select value={selectedGenre} onValueChange={setSelectedGenre}>
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Genre" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Genres</SelectItem>
                  {GENRES.map(genre => (
                    <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={keyFilter} onValueChange={setKeyFilter}>
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Key" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Keys</SelectItem>
                  {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
                    <SelectItem key={key} value={key}>{key}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Track List */}
          <ScrollArea className="flex-1 px-4">
            <div className="space-y-2">
              {loading ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Radio className="w-8 h-8 mx-auto mb-2 animate-spin" />
                  <p className="text-sm">Discovering tracks...</p>
                </div>
              ) : tracks.length > 0 ? (
                tracks.map(renderTrackItem)
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Music className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No tracks found</p>
                  <p className="text-xs">Try adjusting your filters</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="library" className="flex-1 flex flex-col mt-2">
          <ScrollArea className="flex-1 px-4">
            <div className="text-center py-8 text-muted-foreground">
              <Headphones className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Your library is empty</p>
              <p className="text-xs">Liked tracks will appear here</p>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="playlists" className="flex-1 flex flex-col mt-2">
          <ScrollArea className="flex-1 px-4">
            <div className="text-center py-8 text-muted-foreground">
              <Plus className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No DJ sets yet</p>
              <p className="text-xs">Saved mixes will appear here</p>
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
