"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Users,
  UserPlus,
  Radio,
  Eye,
  MessageCircle,
  Settings,
  Crown,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  MoreVertical,
  Share,
  Copy,
  ExternalLink
} from 'lucide-react'

import type { DJSession, Collaborator } from '../types'

interface LiveCollaborationProps {
  session: DJSession | null
  collaborators: Collaborator[]
  isLiveStreaming: boolean
  onInviteUser: (userId: string) => void
  onKickUser: (userId: string) => void
  onToggleUserPermissions: (userId: string, permissions: Partial<Collaborator['permissions']>) => void
  className?: string
}

export function LiveCollaboration({
  session,
  collaborators,
  isLiveStreaming,
  onInviteUser,
  onKickUser,
  onToggleUserPermissions,
  className
}: LiveCollaborationProps) {
  const [showInviteDialog, setShowInviteDialog] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [chatMessage, setChatMessage] = useState('')
  const [chatMessages, setChatMessages] = useState<Array<{
    id: string
    user: string
    message: string
    timestamp: Date
  }>>([])
  const [viewerCount, setViewerCount] = useState(0)

  const handleSendMessage = () => {
    if (!chatMessage.trim()) return
    
    const newMessage = {
      id: Date.now().toString(),
      user: 'You',
      message: chatMessage,
      timestamp: new Date()
    }
    
    setChatMessages(prev => [...prev, newMessage])
    setChatMessage('')
  }

  const handleInviteUser = () => {
    if (!inviteEmail.trim()) return
    
    onInviteUser(inviteEmail)
    setInviteEmail('')
    setShowInviteDialog(false)
  }

  const copySessionLink = () => {
    const sessionUrl = `${window.location.origin}/dj-session/${session?.id}`
    navigator.clipboard.writeText(sessionUrl)
    // Show toast notification
  }

  const renderCollaborator = (collaborator: Collaborator) => (
    <div key={collaborator.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/50">
      <div className="relative">
        <Avatar className="w-8 h-8">
          <AvatarImage src={collaborator.avatar} />
          <AvatarFallback className="text-xs">
            {collaborator.name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className={cn(
          "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background",
          collaborator.isOnline ? "bg-green-500" : "bg-gray-400"
        )} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1">
          <span className="text-sm font-medium truncate">{collaborator.name}</span>
          {collaborator.isHost && (
            <Crown className="w-3 h-3 text-yellow-500" />
          )}
        </div>
        <div className="flex items-center gap-1">
          {collaborator.permissions.canControlDecks && (
            <Badge variant="secondary" className="text-xs px-1">Decks</Badge>
          )}
          {collaborator.permissions.canControlMixer && (
            <Badge variant="secondary" className="text-xs px-1">Mixer</Badge>
          )}
        </div>
      </div>
      
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="w-6 h-6 p-0"
          onClick={() => {
            // Toggle mute
          }}
        >
          {collaborator.isMuted ? (
            <MicOff className="w-3 h-3 text-muted-foreground" />
          ) : (
            <Mic className="w-3 h-3" />
          )}
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="w-6 h-6 p-0"
          onClick={() => {
            // Toggle listening
          }}
        >
          {collaborator.isListening ? (
            <Volume2 className="w-3 h-3" />
          ) : (
            <VolumeX className="w-3 h-3 text-muted-foreground" />
          )}
        </Button>
        
        {!collaborator.isHost && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="w-6 h-6 p-0">
                <MoreVertical className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => onToggleUserPermissions(collaborator.id, {
                  canControlDecks: !collaborator.permissions.canControlDecks
                })}
              >
                {collaborator.permissions.canControlDecks ? 'Remove' : 'Grant'} Deck Control
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onToggleUserPermissions(collaborator.id, {
                  canControlMixer: !collaborator.permissions.canControlMixer
                })}
              >
                {collaborator.permissions.canControlMixer ? 'Remove' : 'Grant'} Mixer Control
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onKickUser(collaborator.id)}
                className="text-destructive"
              >
                Remove from Session
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )

  return (
    <div className={cn("h-full bg-card flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm flex items-center gap-2">
            {isLiveStreaming ? (
              <>
                <Radio className="w-4 h-4 text-red-500" />
                Live Session
              </>
            ) : (
              <>
                <Users className="w-4 h-4" />
                Collaboration
              </>
            )}
          </h3>
          
          <div className="flex items-center gap-1">
            {isLiveStreaming && (
              <Badge variant="destructive" className="text-xs animate-pulse">
                LIVE
              </Badge>
            )}
            <Badge variant="secondary" className="text-xs">
              {collaborators.length} users
            </Badge>
          </div>
        </div>

        {/* Live Stream Info */}
        {isLiveStreaming && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-red-900">Broadcasting Live</div>
                <div className="text-xs text-red-700 flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  {viewerCount} viewers
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={copySessionLink}>
                <Share className="w-3 h-3 mr-1" />
                Share
              </Button>
            </div>
          </div>
        )}

        {/* Session Actions */}
        <div className="flex gap-2">
          <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="flex-1">
                <UserPlus className="w-3 h-3 mr-1" />
                Invite
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite Collaborator</DialogTitle>
                <DialogDescription>
                  Invite someone to join your DJ session
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Email or Username</label>
                  <Input
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="Enter email or username"
                    className="mt-1"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Session Link</label>
                  <div className="flex gap-2">
                    <Input
                      value={`${window.location.origin}/dj-session/${session?.id}`}
                      readOnly
                      className="text-xs"
                    />
                    <Button variant="outline" size="sm" onClick={copySessionLink}>
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleInviteUser}>
                  Send Invite
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Button variant="outline" size="sm" onClick={copySessionLink}>
            <Copy className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Collaborators List */}
      <div className="flex-1 flex flex-col">
        <div className="p-4 border-b">
          <h4 className="text-sm font-medium mb-2">Active Users</h4>
          <ScrollArea className="max-h-32">
            <div className="space-y-1">
              {collaborators.map(renderCollaborator)}
            </div>
          </ScrollArea>
        </div>

        {/* Chat */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 border-b">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <MessageCircle className="w-3 h-3" />
              Session Chat
            </h4>
          </div>
          
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-2">
              {chatMessages.map((message) => (
                <div key={message.id} className="text-sm">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-xs">{message.user}</span>
                    <span className="text-xs text-muted-foreground">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-xs mt-1">{message.message}</div>
                </div>
              ))}
              
              {chatMessages.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <MessageCircle className="w-6 h-6 mx-auto mb-2 opacity-50" />
                  <p className="text-xs">No messages yet</p>
                </div>
              )}
            </div>
          </ScrollArea>
          
          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Input
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                placeholder="Type a message..."
                className="text-sm"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSendMessage()
                  }
                }}
              />
              <Button size="sm" onClick={handleSendMessage}>
                Send
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
