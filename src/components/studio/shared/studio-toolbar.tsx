"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Play,
  Pause,
  Square,
  Save,
  FolderOpen,
  Download,
  PanelLeftClose,
  PanelLeftOpen,
  Settings,
  Sparkles,
  Sliders,
  Music,
  ChevronDown,
  Undo,
  Redo,
  Copy,
  Scissors,
  ClipboardPaste,
  Radio,
  Users,
  UserPlus,
  Mic,
  MicOff
} from 'lucide-react'

interface StudioProject {
  id?: string
  name: string
  modified: boolean
  lastSaved?: Date
}

interface StudioToolbarProps {
  project: StudioProject
  isPlaying: boolean
  onPlay: () => void
  onPause: () => void
  onStop: () => void
  onSave: () => void
  onLoad: () => void
  onExport: () => void
  onToggleSidebar: () => void
  onToggleEffectsRack: () => void
  onToggleAITools: () => void
  sidebarCollapsed: boolean
  showEffectsRack: boolean
  showAITools: boolean
  className?: string
  // DJ-specific props
  isRecording?: boolean
  isLiveStreaming?: boolean
  onStartRecording?: () => void
  onStopRecording?: () => void
  onStartLiveStream?: () => void
  onStopLiveStream?: () => void
  collaboratorCount?: number
  onInviteCollaborator?: (userId: string) => void
}

export function StudioToolbar({
  project,
  isPlaying,
  onPlay,
  onPause,
  onStop,
  onSave,
  onLoad,
  onExport,
  onToggleSidebar,
  onToggleEffectsRack,
  onToggleAITools,
  sidebarCollapsed,
  showEffectsRack,
  showAITools,
  className,
  // DJ-specific props
  isRecording = false,
  isLiveStreaming = false,
  onStartRecording,
  onStopRecording,
  onStartLiveStream,
  onStopLiveStream,
  collaboratorCount = 0,
  onInviteCollaborator
}: StudioToolbarProps) {
  return (
    <div className={cn("flex items-center justify-between px-4 py-2 bg-card border-b", className)}>
      {/* Left Section - Project Info & File Operations */}
      <div className="flex items-center gap-4">
        {/* Project Info */}
        <div className="flex items-center gap-2">
          <Music className="w-5 h-5 text-primary" />
          <div className="flex items-center gap-2">
            <span className="font-semibold text-sm">{project.name}</span>
            {project.modified && (
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                Modified
              </Badge>
            )}
          </div>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* File Operations */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-1">
                File
                <ChevronDown className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={onSave}>
                <Save className="w-4 h-4 mr-2" />
                Save Project
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onLoad}>
                <FolderOpen className="w-4 h-4 mr-2" />
                Open Project
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Audio
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-1">
                Edit
                <ChevronDown className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem>
                <Undo className="w-4 h-4 mr-2" />
                Undo
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Redo className="w-4 h-4 mr-2" />
                Redo
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Copy className="w-4 h-4 mr-2" />
                Copy
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Scissors className="w-4 h-4 mr-2" />
                Cut
              </DropdownMenuItem>
              <DropdownMenuItem>
                <ClipboardPaste className="w-4 h-4 mr-2" />
                Paste
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Center Section - Transport Controls */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onStop}
          className="w-8 h-8 p-0"
        >
          <Square className="w-4 h-4" />
        </Button>
        
        <Button
          variant={isPlaying ? "secondary" : "default"}
          size="sm"
          onClick={isPlaying ? onPause : onPlay}
          className="w-8 h-8 p-0"
        >
          {isPlaying ? (
            <Pause className="w-4 h-4" />
          ) : (
            <Play className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* Right Section - View Controls */}
      <div className="flex items-center gap-2">
        {/* Panel Toggles */}
        <div className="flex items-center gap-1">
          <Button
            variant={sidebarCollapsed ? "ghost" : "secondary"}
            size="sm"
            onClick={onToggleSidebar}
            className="w-8 h-8 p-0"
          >
            {sidebarCollapsed ? (
              <PanelLeftOpen className="w-4 h-4" />
            ) : (
              <PanelLeftClose className="w-4 h-4" />
            )}
          </Button>

          <Button
            variant={showEffectsRack ? "secondary" : "ghost"}
            size="sm"
            onClick={onToggleEffectsRack}
            className="w-8 h-8 p-0"
          >
            <Sliders className="w-4 h-4" />
          </Button>

          <Button
            variant={showAITools ? "secondary" : "ghost"}
            size="sm"
            onClick={onToggleAITools}
            className="w-8 h-8 p-0"
          >
            <Sparkles className="w-4 h-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* DJ Controls */}
        {(onStartRecording || onStopRecording) && (
          <>
            <Separator orientation="vertical" className="h-6" />
            <Button
              variant={isRecording ? "destructive" : "outline"}
              size="sm"
              className="w-8 h-8 p-0"
              onClick={isRecording ? onStopRecording : onStartRecording}
            >
              {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            </Button>
          </>
        )}

        {(onStartLiveStream || onStopLiveStream) && (
          <Button
            variant={isLiveStreaming ? "destructive" : "outline"}
            size="sm"
            className="w-8 h-8 p-0"
            onClick={isLiveStreaming ? onStopLiveStream : onStartLiveStream}
          >
            <Radio className="w-4 h-4" />
          </Button>
        )}

        {onInviteCollaborator && (
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={() => onInviteCollaborator('')}
          >
            <Users className="w-4 h-4" />
            {collaboratorCount > 0 && (
              <span className="text-xs">{collaboratorCount}</span>
            )}
          </Button>
        )}

        <Separator orientation="vertical" className="h-6" />

        {/* Settings */}
        <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}
