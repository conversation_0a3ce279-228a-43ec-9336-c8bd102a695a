// HVPPY Central Studio Types

export interface AudioTrack {
  id: string
  name: string
  type: 'audio' | 'midi' | 'instrument'
  volume: number
  pan: number
  muted: boolean
  solo: boolean
  visible: boolean
  locked: boolean
  color?: string
  effects: string[]
  clips?: AudioClip[]
}

export interface AudioClip {
  id: string
  name: string
  startTime: number
  duration: number
  offset: number
  volume: number
  fadeIn: number
  fadeOut: number
  color?: string
  audioUrl?: string
}

export interface StudioProject {
  id?: string
  name: string
  modified: boolean
  lastSaved?: Date
  tempo: number
  timeSignature: [number, number]
  sampleRate: number
  tracks?: AudioTrack[]
}

export interface AudioEffect {
  id: string
  name: string
  type: 'eq' | 'compressor' | 'reverb' | 'delay' | 'distortion' | 'filter' | 'chorus' | 'phaser'
  enabled: boolean
  parameters: Record<string, number>
  preset?: string
}

export interface AITask {
  id: string
  type: 'generate' | 'enhance' | 'separate' | 'master'
  status: 'idle' | 'processing' | 'completed' | 'error'
  progress: number
  result?: string
  config?: Record<string, any>
}

export interface StudioTab {
  id: string
  label: string
  icon: 'Clock' | 'Sliders' | 'Sparkles' | 'Plus' | 'MoreHorizontal' | 'Disc3' | 'Scissors' | 'Zap'
  component: React.ReactNode
  closable?: boolean
  modified?: boolean
}

export interface ExportSettings {
  format: 'wav' | 'mp3' | 'flac' | 'aac'
  quality: 'low' | 'medium' | 'high' | 'lossless'
  sampleRate: number
  bitDepth: number
  normalize: boolean
  fadeOut: number
}

// DJ Platform Types
export interface Track {
  id: string
  title: string
  artist: string
  duration: number
  bpm: number
  key: string
  genre: string
  mood: string
  energy: number
  danceability: number
  artwork?: string
  audioUrl: string
  isLiked: boolean
  playCount: number
  compatibility?: number
  tags?: string[]
  releaseDate?: string
  label?: string
}

export interface DJDeck {
  id: string
  track: Track | null
  isPlaying: boolean
  currentTime: number
  duration: number
  bpm: number
  detectedBpm: number
  key: string
  volume: number
  pitch: number
  tempo: number
  cuePoints: CuePoint[]
  loopStart: number | null
  loopEnd: number | null
  isLooping: boolean
  waveform: Float32Array | null
  beatGrid: number[]
  isSync: boolean
  isCue: boolean
  audioNode: AudioBufferSourceNode | null
  gainNode: GainNode | null
}

export interface CuePoint {
  id: string
  time: number
  label: string
  color: string
}

export interface Crossfader {
  position: number // -1 to 1 (A to B)
  curve: 'linear' | 'logarithmic' | 'exponential'
  audioNode: GainNode | null
}

export interface DJMixer {
  masterVolume: number
  masterGain: GainNode | null
  cueVolume: number
  cueGain: GainNode | null
  channels: {
    [key: string]: {
      volume: number
      highEQ: number
      midEQ: number
      lowEQ: number
      gain: GainNode | null
      eqNodes: {
        high: BiquadFilterNode | null
        mid: BiquadFilterNode | null
        low: BiquadFilterNode | null
      }
    }
  }
}

export interface DJSession {
  id: string
  name: string
  modified: boolean
  lastSaved?: Date
  isLive: boolean
  recordingUrl?: string
}

export interface Collaborator {
  id: string
  name: string
  avatar?: string
  permissions: {
    canControlDecks: boolean
    canControlMixer: boolean
    canInviteOthers: boolean
  }
  isOnline: boolean
  isHost?: boolean
  isMuted?: boolean
  isListening?: boolean
}
