"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Loader2, ImageOff } from 'lucide-react'

interface ProgressiveImageProps {
  src: string
  alt: string
  placeholder?: string // Low-res placeholder image
  blurDataURL?: string // Base64 blur placeholder
  className?: string
  containerClassName?: string
  width?: number
  height?: number
  priority?: boolean // Load immediately without intersection observer
  onLoad?: () => void
  onError?: (error: Error) => void
  fallback?: React.ReactNode
  aspectRatio?: 'square' | 'video' | 'auto' | string
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down'
  sizes?: string
  quality?: number
}

export function ProgressiveImage({
  src,
  alt,
  placeholder,
  blurDataURL,
  className,
  containerClassName,
  width,
  height,
  priority = false,
  onLoad,
  onError,
  fallback,
  aspectRatio = 'auto',
  objectFit = 'cover',
  sizes,
  quality = 75,
}: ProgressiveImageProps) {
  const [loadState, setLoadState] = useState<'loading' | 'loaded' | 'error'>('loading')
  const [isInView, setIsInView] = useState(priority)
  const [showPlaceholder, setShowPlaceholder] = useState(true)
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  // Generate optimized image URL with quality and size parameters
  const getOptimizedImageUrl = useCallback((url: string, w?: number, q?: number) => {
    if (!url) return url
    
    // If it's already a data URL or external URL, return as-is
    if (url.startsWith('data:') || url.startsWith('http')) {
      return url
    }
    
    // Add optimization parameters for internal images
    const params = new URLSearchParams()
    if (w) params.set('w', w.toString())
    if (q) params.set('q', q.toString())
    
    const separator = url.includes('?') ? '&' : '?'
    return params.toString() ? `${url}${separator}${params.toString()}` : url
  }, [])

  // Get aspect ratio class
  const getAspectRatioClass = useCallback(() => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square'
      case 'video':
        return 'aspect-video'
      case 'auto':
        return ''
      default:
        return aspectRatio.startsWith('aspect-') ? aspectRatio : `aspect-[${aspectRatio}]`
    }
  }, [aspectRatio])

  // Handle image load
  const handleImageLoad = useCallback(() => {
    setLoadState('loaded')
    setShowPlaceholder(false)
    onLoad?.()
  }, [onLoad])

  // Handle image error
  const handleImageError = useCallback(() => {
    setLoadState('error')
    setShowPlaceholder(false)
    onError?.(new Error(`Failed to load image: ${src}`))
  }, [onError, src])

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (priority || !containerRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observerRef.current?.disconnect()
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    )

    observerRef.current.observe(containerRef.current)

    return () => {
      observerRef.current?.disconnect()
    }
  }, [priority])

  // Load image when in view
  useEffect(() => {
    if (!isInView || !imgRef.current) return

    const img = imgRef.current
    const optimizedSrc = getOptimizedImageUrl(src, width, quality)

    // If image is already loaded with the same src, mark as loaded
    if (img.complete && img.src === optimizedSrc) {
      handleImageLoad()
      return
    }

    // Set up image loading
    img.onload = handleImageLoad
    img.onerror = handleImageError
    img.src = optimizedSrc

    return () => {
      img.onload = null
      img.onerror = null
    }
  }, [isInView, src, width, quality, getOptimizedImageUrl, handleImageLoad, handleImageError])

  // Generate blur placeholder style
  const getBlurPlaceholderStyle = useCallback(() => {
    if (!blurDataURL) return {}
    
    return {
      backgroundImage: `url(${blurDataURL})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      filter: 'blur(20px)',
      transform: 'scale(1.1)', // Slightly scale to hide blur edges
    }
  }, [blurDataURL])

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative overflow-hidden bg-muted',
        getAspectRatioClass(),
        containerClassName
      )}
    >
      {/* Blur placeholder */}
      {showPlaceholder && blurDataURL && (
        <div
          className="absolute inset-0 transition-opacity duration-300"
          style={getBlurPlaceholderStyle()}
        />
      )}

      {/* Low-res placeholder */}
      {showPlaceholder && placeholder && !blurDataURL && (
        <img
          src={placeholder}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full transition-opacity duration-300',
            `object-${objectFit}`,
            'filter blur-sm scale-110'
          )}
        />
      )}

      {/* Loading state */}
      {loadState === 'loading' && isInView && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          <Loader2 className="w-6 h-6 text-muted-foreground animate-spin" />
        </div>
      )}

      {/* Main image */}
      {isInView && (
        <img
          ref={imgRef}
          alt={alt}
          width={width}
          height={height}
          sizes={sizes}
          className={cn(
            'w-full h-full transition-opacity duration-300',
            `object-${objectFit}`,
            loadState === 'loaded' ? 'opacity-100' : 'opacity-0',
            className
          )}
        />
      )}

      {/* Error state */}
      {loadState === 'error' && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-muted text-muted-foreground">
          {fallback || (
            <>
              <ImageOff className="w-8 h-8 mb-2" />
              <span className="text-sm">Failed to load image</span>
            </>
          )}
        </div>
      )}

      {/* Overlay for additional content */}
      {loadState === 'loaded' && (
        <div className="absolute inset-0 pointer-events-none">
          {/* This can be used for overlays like play buttons, etc. */}
        </div>
      )}
    </div>
  )
}

// Hook for generating blur data URLs from images
export function useBlurDataURL(src: string) {
  const [blurDataURL, setBlurDataURL] = useState<string | null>(null)
  const canvasRef = useRef<HTMLCanvasElement | null>(null)

  useEffect(() => {
    if (!src || src.startsWith('data:')) return

    const generateBlurDataURL = async () => {
      try {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        
        img.onload = () => {
          if (!canvasRef.current) {
            canvasRef.current = document.createElement('canvas')
          }
          
          const canvas = canvasRef.current
          const ctx = canvas.getContext('2d')
          if (!ctx) return

          // Use small canvas for blur effect
          canvas.width = 40
          canvas.height = 40

          // Draw scaled down image
          ctx.drawImage(img, 0, 0, 40, 40)

          // Get data URL
          const dataURL = canvas.toDataURL('image/jpeg', 0.1)
          setBlurDataURL(dataURL)
        }

        img.src = src
      } catch (error) {
        console.warn('Failed to generate blur data URL:', error)
      }
    }

    generateBlurDataURL()
  }, [src])

  return blurDataURL
}

// Utility function to generate responsive image sizes
export function generateImageSizes(breakpoints: Record<string, number>) {
  return Object.entries(breakpoints)
    .map(([breakpoint, width]) => `(max-width: ${breakpoint}) ${width}px`)
    .join(', ')
}

// Common size presets
export const imageSizes = {
  thumbnail: '(max-width: 640px) 150px, 200px',
  card: '(max-width: 640px) 300px, (max-width: 1024px) 400px, 500px',
  hero: '(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 1200px',
  fullWidth: '100vw',
}
