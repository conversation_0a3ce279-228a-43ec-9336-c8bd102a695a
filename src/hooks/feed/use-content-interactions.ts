"use client"

import { useCallback } from 'react'
import { ReactionType } from '@/types'
import { toast } from 'sonner'

interface UseContentInteractionsReturn {
  react: (postId: string, type: ReactionType, mood?: string) => Promise<void>
  unreact: (postId: string) => Promise<void>
  toggleMemory: (postId: string) => Promise<void>
  share: (postId: string) => Promise<void>
  comment: (postId: string, content: string) => Promise<void>
}

export function useContentInteractions(): UseContentInteractionsReturn {
  const react = useCallback(async (postId: string, type: ReactionType, mood?: string) => {
    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, mood }),
      })

      if (!response.ok) {
        throw new Error('Failed to react to post')
      }

      toast.success('Reaction added!')
    } catch (error) {
      console.error('Error reacting to post:', error)
      toast.error('Failed to react to post')
      throw error
    }
  }, [])

  const unreact = useCallback(async (postId: string) => {
    try {
      const response = await fetch(`/api/post/${postId}/like`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to remove reaction')
      }

      toast.success('Reaction removed!')
    } catch (error) {
      console.error('Error removing reaction:', error)
      toast.error('Failed to remove reaction')
      throw error
    }
  }, [])

  const toggleMemory = useCallback(async (postId: string) => {
    try {
      const response = await fetch(`/api/post/${postId}/memory`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to toggle memory')
      }

      const data = await response.json()
      toast.success(data.saved ? 'Saved to memories!' : 'Removed from memories!')
    } catch (error) {
      console.error('Error toggling memory:', error)
      toast.error('Failed to save memory')
      throw error
    }
  }, [])

  const share = useCallback(async (postId: string) => {
    try {
      // Try native sharing first
      if (navigator.share) {
        await navigator.share({
          title: 'Check out this content on HVPPY',
          url: `${window.location.origin}/post/${postId}`,
        })
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(`${window.location.origin}/post/${postId}`)
        toast.success('Link copied to clipboard!')
      }

      // Track share
      await fetch(`/api/post/${postId}/share`, {
        method: 'POST',
      })
    } catch (error) {
      console.error('Error sharing post:', error)
      toast.error('Failed to share post')
      throw error
    }
  }, [])

  const comment = useCallback(async (postId: string, content: string) => {
    try {
      const response = await fetch(`/api/post/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      })

      if (!response.ok) {
        throw new Error('Failed to add comment')
      }

      toast.success('Comment added!')
    } catch (error) {
      console.error('Error adding comment:', error)
      toast.error('Failed to add comment')
      throw error
    }
  }, [])

  return {
    react,
    unreact,
    toggleMemory,
    share,
    comment,
  }
}