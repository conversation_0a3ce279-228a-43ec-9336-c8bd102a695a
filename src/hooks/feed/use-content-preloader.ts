"use client"

import { useState, useEffect, useCallback } from 'react'
import { FeedItem } from '@/types/feed'

interface PreloadOptions {
  preloadDistance: number
  offloadDistance: number
  enableImagePreload: boolean
  enableVideoPreload: boolean
  enableAudioPreload: boolean
}

interface UseContentPreloaderReturn {
  preloadedContent: Map<string, any>
  preloadedCount: number
  preloadItem: (item: FeedItem) => Promise<void>
  offloadItem: (itemId: string) => void
}

export function useContentPreloader(
  items: FeedItem[],
  currentIndex: number,
  options: PreloadOptions
): UseContentPreloaderReturn {
  const [preloadedContent, setPreloadedContent] = useState<Map<string, any>>(new Map())

  const preloadItem = useCallback(async (item: FeedItem) => {
    if (preloadedContent.has(item.id)) return

    try {
      const contentUrl = item.post.contentUrl || item.post.mediaUrls[0]
      if (!contentUrl) return

      let preloadedData: any = null

      if (item.post.contentType.startsWith('image/') && options.enableImagePreload) {
        const img = new Image()
        img.src = contentUrl
        await new Promise((resolve, reject) => {
          img.onload = resolve
          img.onerror = reject
        })
        preloadedData = { type: 'image', element: img }
      } else if (item.post.contentType.startsWith('video/') && options.enableVideoPreload) {
        const video = document.createElement('video')
        video.src = contentUrl
        video.preload = 'metadata'
        preloadedData = { type: 'video', element: video }
      } else if (item.post.contentType.startsWith('audio/') && options.enableAudioPreload) {
        const audio = document.createElement('audio')
        audio.src = contentUrl
        audio.preload = 'metadata'
        preloadedData = { type: 'audio', element: audio }
      }

      if (preloadedData) {
        setPreloadedContent(prev => new Map(prev).set(item.id, preloadedData))
      }
    } catch (error) {
      console.warn('Failed to preload content:', error)
    }
  }, [preloadedContent, options])

  const offloadItem = useCallback((itemId: string) => {
    setPreloadedContent(prev => {
      const newMap = new Map(prev)
      newMap.delete(itemId)
      return newMap
    })
  }, [])

  // Preload items around current index
  useEffect(() => {
    const preloadPromises: Promise<void>[] = []

    for (let i = Math.max(0, currentIndex - options.preloadDistance); 
         i <= Math.min(items.length - 1, currentIndex + options.preloadDistance); 
         i++) {
      if (items[i]) {
        preloadPromises.push(preloadItem(items[i]))
      }
    }

    Promise.all(preloadPromises).catch(console.warn)

    // Offload items that are too far away
    const itemsToOffload: string[] = []
    preloadedContent.forEach((_, itemId) => {
      const itemIndex = items.findIndex(item => item.id === itemId)
      if (itemIndex !== -1 && Math.abs(itemIndex - currentIndex) > options.offloadDistance) {
        itemsToOffload.push(itemId)
      }
    })

    itemsToOffload.forEach(offloadItem)
  }, [currentIndex, items, options, preloadItem, offloadItem, preloadedContent])

  return {
    preloadedContent,
    preloadedCount: preloadedContent.size,
    preloadItem,
    offloadItem,
  }
}