"use client"

import { useState, useEffect, useCallback } from 'react'
import { FeedType, FeedItem, FeedFilters } from '@/types/feed'
import { ContentType } from '@/types'

interface UseFeedDataReturn {
  items: FeedItem[]
  loading: boolean
  error: string | null
  hasMore: boolean
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
  updateFilters: (filters: FeedFilters) => void
}

export function useFeedData(
  feedType: FeedType,
  initialFilters?: FeedFilters
): UseFeedDataReturn {
  const [items, setItems] = useState<FeedItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [filters, setFilters] = useState<FeedFilters>(initialFilters || {})
  const [page, setPage] = useState(0)

  const fetchFeedData = useCallback(async (pageNum: number = 0, reset: boolean = false) => {
    try {
      setLoading(true)
      setError(null)

      let endpoint = '/api/feed/discover'
      
      switch (feedType) {
        case FeedType.DISCOVER:
          endpoint = '/api/feed/discover'
          break
        case FeedType.FOLLOWING:
          endpoint = '/api/feed/following'
          break
        case FeedType.TRENDING:
          endpoint = '/api/feed/trending'
          break
        default:
          endpoint = '/api/feed/discover'
      }

      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '10',
        ...Object.entries(filters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null) {
            acc[key] = Array.isArray(value) ? value.join(',') : String(value)
          }
          return acc
        }, {} as Record<string, string>)
      })

      const response = await fetch(`${endpoint}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch feed: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Transform API response to FeedItem format
      const feedItems: FeedItem[] = data.posts.map((post: any) => ({
        id: post.id,
        post: {
          ...post,
          user: {
            ...post.user,
            displayName: post.user.name,
            avatarUrl: post.user.image
          },
          description: post.content,
          reactions: [],
          memories: [],
          comments: []
        }
      }))

      if (reset || pageNum === 0) {
        setItems(feedItems)
      } else {
        setItems(prev => [...prev, ...feedItems])
      }

      setHasMore(feedItems.length === 10) // Assume no more if less than limit
      setPage(pageNum)
    } catch (err) {
      console.error('Error fetching feed data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }, [feedType, filters])

  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await fetchFeedData(page + 1, false)
    }
  }, [fetchFeedData, loading, hasMore, page])

  const refresh = useCallback(async () => {
    await fetchFeedData(0, true)
  }, [fetchFeedData])

  const updateFilters = useCallback((newFilters: FeedFilters) => {
    setFilters(newFilters)
    setPage(0)
  }, [])

  // Initial load
  useEffect(() => {
    fetchFeedData(0, true)
  }, [fetchFeedData])

  // Reload when filters change
  useEffect(() => {
    if (page === 0) {
      fetchFeedData(0, true)
    }
  }, [filters, feedType])

  return {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters
  }
}