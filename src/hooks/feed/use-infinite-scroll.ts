"use client"

import { useCallback, useEffect, useRef } from 'react'

interface UseInfiniteScrollOptions {
  threshold?: number
  rootMargin?: string
  enabled?: boolean
}

interface UseInfiniteScrollReturn {
  observeElement: (element: HTMLElement | null) => void
  unobserveElement: (element: HTMLElement) => void
}

export function useInfiniteScroll(
  onLoadMore: () => void,
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn {
  const {
    threshold = 0.1,
    rootMargin = '100px',
    enabled = true,
  } = options

  const observer = useRef<IntersectionObserver | null>(null)

  const observeElement = useCallback((element: HTMLElement | null) => {
    if (!element || !enabled) return

    if (observer.current) {
      observer.current.observe(element)
    }
  }, [enabled])

  const unobserveElement = useCallback((element: HTMLElement) => {
    if (observer.current) {
      observer.current.unobserve(element)
    }
  }, [])

  useEffect(() => {
    if (!enabled) return

    observer.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            onLoadMore()
          }
        })
      },
      {
        threshold,
        rootMargin,
      }
    )

    return () => {
      if (observer.current) {
        observer.current.disconnect()
      }
    }
  }, [onLoadMore, threshold, rootMargin, enabled])

  return {
    observeElement,
    unobserveElement,
  }
}