"use client"

import { useCallback, useRef, useEffect } from 'react'
import { FeedItem } from '@/types/feed'

interface AutoplayOptions {
  threshold: number
  autoplayDelay: number
  pauseDelay: number
  enableVideoAutoplay: boolean
  enableAudioAutoplay: boolean
  muteByDefault: boolean
}

interface UseMediaAutoplayReturn {
  registerMediaElement: (element: HTMLVideoElement | HTMLAudioElement, itemId: string, type: 'video' | 'audio') => void
  unregisterMediaElement: (itemId: string) => void
  observeElement: (element: HTMLElement) => void
}

export function useMediaAutoplay(
  items: FeedItem[],
  currentIndex: number,
  options: AutoplayOptions
): UseMediaAutoplayReturn {
  const mediaElements = useRef<Map<string, HTMLVideoElement | HTMLAudioElement>>(new Map())
  const observer = useRef<IntersectionObserver | null>(null)
  const playTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const pauseTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())

  const registerMediaElement = useCallback((
    element: HTMLVideoElement | HTMLAudioElement, 
    itemId: string, 
    type: 'video' | 'audio'
  ) => {
    mediaElements.current.set(itemId, element)

    // Set initial mute state
    if (options.muteByDefault) {
      element.muted = true
    }

    // Add event listeners
    element.addEventListener('loadedmetadata', () => {
      console.log(`Media loaded for ${itemId}`)
    })

    element.addEventListener('error', (e) => {
      console.error(`Media error for ${itemId}:`, e)
    })
  }, [options.muteByDefault])

  const unregisterMediaElement = useCallback((itemId: string) => {
    const element = mediaElements.current.get(itemId)
    if (element) {
      element.pause()
      mediaElements.current.delete(itemId)
    }

    // Clear timeouts
    const playTimeout = playTimeouts.current.get(itemId)
    if (playTimeout) {
      clearTimeout(playTimeout)
      playTimeouts.current.delete(itemId)
    }

    const pauseTimeout = pauseTimeouts.current.get(itemId)
    if (pauseTimeout) {
      clearTimeout(pauseTimeout)
      pauseTimeouts.current.delete(itemId)
    }
  }, [])

  const playMedia = useCallback((itemId: string) => {
    const element = mediaElements.current.get(itemId)
    if (!element) return

    const item = items.find(i => i.id === itemId)
    if (!item) return

    const isVideo = item.post.contentType.startsWith('video/')
    const isAudio = item.post.contentType.startsWith('audio/')

    if ((isVideo && options.enableVideoAutoplay) || (isAudio && options.enableAudioAutoplay)) {
      const playTimeout = setTimeout(() => {
        element.play().catch(error => {
          console.warn(`Autoplay failed for ${itemId}:`, error)
        })
      }, options.autoplayDelay)

      playTimeouts.current.set(itemId, playTimeout)
    }
  }, [items, options])

  const pauseMedia = useCallback((itemId: string) => {
    const element = mediaElements.current.get(itemId)
    if (!element) return

    const pauseTimeout = setTimeout(() => {
      element.pause()
    }, options.pauseDelay)

    pauseTimeouts.current.set(itemId, pauseTimeout)
  }, [options.pauseDelay])

  const observeElement = useCallback((element: HTMLElement) => {
    if (!observer.current) return

    observer.current.observe(element)
  }, [])

  // Set up intersection observer
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const itemId = entry.target.getAttribute('data-post-id')
          if (!itemId) return

          if (entry.isIntersecting && entry.intersectionRatio >= options.threshold) {
            // Clear any pending pause timeout
            const pauseTimeout = pauseTimeouts.current.get(itemId)
            if (pauseTimeout) {
              clearTimeout(pauseTimeout)
              pauseTimeouts.current.delete(itemId)
            }

            playMedia(itemId)
          } else {
            // Clear any pending play timeout
            const playTimeout = playTimeouts.current.get(itemId)
            if (playTimeout) {
              clearTimeout(playTimeout)
              playTimeouts.current.delete(itemId)
            }

            pauseMedia(itemId)
          }
        })
      },
      {
        threshold: options.threshold,
        rootMargin: '0px',
      }
    )

    return () => {
      if (observer.current) {
        observer.current.disconnect()
      }
    }
  }, [options.threshold, playMedia, pauseMedia])

  // Pause all media when current index changes
  useEffect(() => {
    mediaElements.current.forEach((element, itemId) => {
      const itemIndex = items.findIndex(item => item.id === itemId)
      if (itemIndex !== currentIndex) {
        element.pause()
      }
    })
  }, [currentIndex, items])

  return {
    registerMediaElement,
    unregisterMediaElement,
    observeElement,
  }
}