"use client"

import { useState, useCallback } from 'react'

interface UseMoodFilterReturn {
  selectedMoods: string[]
  setSelectedMoods: (moods: string[]) => void
  moodCounts: Record<string, number>
  clearMoods: () => void
  toggleMood: (mood: string) => void
}

export function useMoodFilter(): UseMoodFilterReturn {
  const [selectedMoods, setSelectedMoods] = useState<string[]>([])
  const [moodCounts] = useState<Record<string, number>>({
    'happy': 1250,
    'energetic': 890,
    'chill': 2100,
    'romantic': 650,
    'melancholic': 420,
    'uplifting': 980,
    'dreamy': 340,
    'intense': 560,
  })

  const clearMoods = useCallback(() => {
    setSelectedMoods([])
  }, [])

  const toggleMood = useCallback((mood: string) => {
    setSelectedMoods(prev => 
      prev.includes(mood) 
        ? prev.filter(m => m !== mood)
        : [...prev, mood]
    )
  }, [])

  return {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
    toggleMood,
  }
}