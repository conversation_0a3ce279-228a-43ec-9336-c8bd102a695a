"use client"

import { useState, useEffect, useCallback } from 'react'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  scrollPerformance?: {
    smoothness: number
    jankCount: number
  }
  averageLoadTime: number
}

interface PerformanceOptions {
  enabled: boolean
  onPerformanceIssue?: (issue: string, severity: 'low' | 'medium' | 'high') => void
}

interface UsePerformanceMonitorReturn {
  metrics: PerformanceMetrics
  recordLoadTime: (duration: number) => void
  recordScrollEvent: (timestamp: number) => void
}

export function usePerformanceMonitor(
  options: PerformanceOptions
): UsePerformanceMonitorReturn {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    averageLoadTime: 0,
  })

  const [loadTimes, setLoadTimes] = useState<number[]>([])
  const [scrollEvents, setScrollEvents] = useState<number[]>([])

  const recordLoadTime = useCallback((duration: number) => {
    if (!options.enabled) return

    setLoadTimes(prev => {
      const newTimes = [...prev, duration].slice(-10) // Keep last 10
      const average = newTimes.reduce((sum, time) => sum + time, 0) / newTimes.length

      setMetrics(current => ({
        ...current,
        averageLoadTime: Math.round(average),
      }))

      // Check for performance issues
      if (duration > 3000 && options.onPerformanceIssue) {
        options.onPerformanceIssue(`Slow load time: ${duration}ms`, 'high')
      } else if (duration > 1500 && options.onPerformanceIssue) {
        options.onPerformanceIssue(`Moderate load time: ${duration}ms`, 'medium')
      }

      return newTimes
    })
  }, [options])

  const recordScrollEvent = useCallback((timestamp: number) => {
    if (!options.enabled) return

    setScrollEvents(prev => {
      const newEvents = [...prev, timestamp].slice(-60) // Keep last 60 frames
      
      if (newEvents.length >= 2) {
        const frameTimes = newEvents.slice(1).map((time, i) => time - newEvents[i])
        const avgFrameTime = frameTimes.reduce((sum, time) => sum + time, 0) / frameTimes.length
        const fps = Math.round(1000 / avgFrameTime)
        
        // Calculate jank (frames > 16.67ms)
        const jankFrames = frameTimes.filter(time => time > 16.67)
        const smoothness = Math.round(((frameTimes.length - jankFrames.length) / frameTimes.length) * 100)

        setMetrics(current => ({
          ...current,
          fps: Math.min(fps, 60),
          scrollPerformance: {
            smoothness,
            jankCount: jankFrames.length,
          },
        }))

        // Check for performance issues
        if (fps < 30 && options.onPerformanceIssue) {
          options.onPerformanceIssue(`Low FPS: ${fps}`, 'high')
        } else if (smoothness < 80 && options.onPerformanceIssue) {
          options.onPerformanceIssue(`Poor scroll smoothness: ${smoothness}%`, 'medium')
        }
      }

      return newEvents
    })
  }, [options])

  // Monitor memory usage
  useEffect(() => {
    if (!options.enabled) return

    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
        
        setMetrics(current => ({
          ...current,
          memoryUsage: usedMB,
        }))

        // Check for memory issues
        if (usedMB > 100 && options.onPerformanceIssue) {
          options.onPerformanceIssue(`High memory usage: ${usedMB}MB`, 'medium')
        } else if (usedMB > 200 && options.onPerformanceIssue) {
          options.onPerformanceIssue(`Very high memory usage: ${usedMB}MB`, 'high')
        }
      }
    }

    const interval = setInterval(updateMemoryUsage, 5000)
    updateMemoryUsage() // Initial check

    return () => clearInterval(interval)
  }, [options])

  return {
    metrics,
    recordLoadTime,
    recordScrollEvent,
  }
}