"use client"

import { useCallback, useEffect, useRef } from 'react'

interface SwipeGestureOptions {
  threshold?: number
  preventDefaultTouchmove?: boolean
  deltaThreshold?: number
}

interface SwipeGestureHandlers {
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
}

interface UseSwipeGesturesReturn {
  attachToElement: (element: HTMLElement | null) => void
  detachFromElement: (element: HTMLElement) => void
}

export function useSwipeGestures(
  handlers: SwipeGestureHandlers,
  options: SwipeGestureOptions = {}
): UseSwipeGesturesReturn {
  const {
    threshold = 50,
    preventDefaultTouchmove = true,
    deltaThreshold = 10,
  } = options

  const touchStart = useRef<{ x: number; y: number } | null>(null)
  const touchEnd = useRef<{ x: number; y: number } | null>(null)

  const handleTouchStart = useCallback((e: TouchEvent) => {
    touchEnd.current = null
    touchStart.current = {
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
    }
  }, [])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (preventDefaultTouchmove) {
      e.preventDefault()
    }
  }, [preventDefaultTouchmove])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!touchStart.current) return

    touchEnd.current = {
      x: e.changedTouches[0].clientX,
      y: e.changedTouches[0].clientY,
    }

    const deltaX = touchStart.current.x - touchEnd.current.x
    const deltaY = touchStart.current.y - touchEnd.current.y

    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    // Determine if this is a valid swipe
    if (Math.max(absDeltaX, absDeltaY) < threshold) return

    // Determine swipe direction
    if (absDeltaX > absDeltaY) {
      // Horizontal swipe
      if (absDeltaX > deltaThreshold) {
        if (deltaX > 0) {
          handlers.onSwipeLeft?.()
        } else {
          handlers.onSwipeRight?.()
        }
      }
    } else {
      // Vertical swipe
      if (absDeltaY > deltaThreshold) {
        if (deltaY > 0) {
          handlers.onSwipeUp?.()
        } else {
          handlers.onSwipeDown?.()
        }
      }
    }

    touchStart.current = null
    touchEnd.current = null
  }, [handlers, threshold, deltaThreshold])

  const attachToElement = useCallback((element: HTMLElement | null) => {
    if (!element) return

    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  const detachFromElement = useCallback((element: HTMLElement) => {
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  return {
    attachToElement,
    detachFromElement,
  }
}