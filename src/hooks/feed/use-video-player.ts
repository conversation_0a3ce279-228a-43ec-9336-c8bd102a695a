"use client"

import { useState, useCallback, useRef, useEffect } from 'react'
import { VideoPlayerState } from '@/types/feed'

interface UseVideoPlayerOptions {
  autoPlay?: boolean
  muted?: boolean
  loop?: boolean
  preload?: 'none' | 'metadata' | 'auto'
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onTimeUpdate?: (currentTime: number) => void
  onDurationChange?: (duration: number) => void
  onVolumeChange?: (volume: number, muted: boolean) => void
  onError?: (error: string) => void
}

interface UseVideoPlayerReturn {
  state: VideoPlayerState
  actions: {
    play: () => Promise<void>
    pause: () => void
    seek: (time: number) => void
    setVolume: (volume: number) => void
    toggleMute: () => void
    toggleFullscreen: () => void
    setPlaybackRate: (rate: number) => void
  }
  ref: React.RefObject<HTMLVideoElement>
  registerElement: (element: HTMLVideoElement | null) => void
}

export function useVideoPlayer(
  src?: string,
  options: UseVideoPlayerOptions = {}
): UseVideoPlayerReturn {
  const videoRef = useRef<HTMLVideoElement>(null)
  
  const [state, setState] = useState<VideoPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    buffered: null,
    volume: 1,
    isMuted: options.muted || false,
    playbackRate: 1,
    quality: 'auto',
    error: null,
    isFullscreen: false,
  })

  const registerElement = useCallback((element: HTMLVideoElement | null) => {
    if (element) {
      videoRef.current = element
      
      // Apply initial settings
      element.muted = options.muted || false
      element.loop = options.loop || false
      element.preload = options.preload || 'metadata'
      
      if (src) {
        element.src = src
      }
    }
  }, [src, options])

  const play = useCallback(async () => {
    if (!videoRef.current) return

    try {
      await videoRef.current.play()
      setState(prev => ({ ...prev, isPlaying: true, error: null }))
      options.onPlay?.()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Playback failed'
      setState(prev => ({ ...prev, error: errorMessage }))
      options.onError?.(errorMessage)
    }
  }, [options])

  const pause = useCallback(() => {
    if (!videoRef.current) return

    videoRef.current.pause()
    setState(prev => ({ ...prev, isPlaying: false }))
    options.onPause?.()
  }, [options])

  const seek = useCallback((time: number) => {
    if (!videoRef.current) return

    videoRef.current.currentTime = time
    setState(prev => ({ ...prev, currentTime: time }))
  }, [])

  const setVolume = useCallback((volume: number) => {
    if (!videoRef.current) return

    const clampedVolume = Math.max(0, Math.min(1, volume))
    videoRef.current.volume = clampedVolume
    
    setState(prev => ({ 
      ...prev, 
      volume: clampedVolume,
      isMuted: clampedVolume === 0
    }))
    
    options.onVolumeChange?.(clampedVolume, clampedVolume === 0)
  }, [options])

  const toggleMute = useCallback(() => {
    if (!videoRef.current) return

    const newMuted = !state.isMuted
    videoRef.current.muted = newMuted
    
    setState(prev => ({ ...prev, isMuted: newMuted }))
    options.onVolumeChange?.(state.volume, newMuted)
  }, [state.isMuted, state.volume, options])

  const toggleFullscreen = useCallback(async () => {
    if (!videoRef.current) return

    try {
      if (!document.fullscreenElement) {
        await videoRef.current.requestFullscreen()
        setState(prev => ({ ...prev, isFullscreen: true }))
      } else {
        await document.exitFullscreen()
        setState(prev => ({ ...prev, isFullscreen: false }))
      }
    } catch (error) {
      console.warn('Fullscreen toggle failed:', error)
    }
  }, [])

  const setPlaybackRate = useCallback((rate: number) => {
    if (!videoRef.current) return

    videoRef.current.playbackRate = rate
    setState(prev => ({ ...prev, playbackRate: rate }))
  }, [])

  // Event listeners
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setState(prev => ({ ...prev, currentTime: video.currentTime }))
      options.onTimeUpdate?.(video.currentTime)
    }

    const handleDurationChange = () => {
      setState(prev => ({ ...prev, duration: video.duration }))
      options.onDurationChange?.(video.duration)
    }

    const handleProgress = () => {
      setState(prev => ({ ...prev, buffered: video.buffered }))
    }

    const handlePlay = () => {
      setState(prev => ({ ...prev, isPlaying: true }))
    }

    const handlePause = () => {
      setState(prev => ({ ...prev, isPlaying: false }))
    }

    const handleEnded = () => {
      setState(prev => ({ ...prev, isPlaying: false }))
      options.onEnded?.()
    }

    const handleError = () => {
      const errorMessage = video.error?.message || 'Video error occurred'
      setState(prev => ({ ...prev, error: errorMessage }))
      options.onError?.(errorMessage)
    }

    const handleVolumeChange = () => {
      setState(prev => ({ 
        ...prev, 
        volume: video.volume,
        isMuted: video.muted
      }))
    }

    const handleFullscreenChange = () => {
      setState(prev => ({ 
        ...prev, 
        isFullscreen: document.fullscreenElement === video
      }))
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('progress', handleProgress)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)
    video.addEventListener('volumechange', handleVolumeChange)
    document.addEventListener('fullscreenchange', handleFullscreenChange)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('progress', handleProgress)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
      video.removeEventListener('volumechange', handleVolumeChange)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [options])

  // Auto-play effect
  useEffect(() => {
    if (options.autoPlay && videoRef.current && src) {
      play()
    }
  }, [src, options.autoPlay, play])

  return {
    state,
    actions: {
      play,
      pause,
      seek,
      setVolume,
      toggleMute,
      toggleFullscreen,
      setPlaybackRate,
    },
    ref: videoRef,
    registerElement,
  }
}