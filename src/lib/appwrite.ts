import { Client, Storage, Databases, Account } from "appwrite";

const client = new Client();

client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT as string) // Your API Endpoint
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID as string);

// Set API key for server-side operations if available
if (process.env.APPWRITE_API_KEY) {
  client.setKey(process.env.APPWRITE_API_KEY);
}

const account = new Account(client);
const storage = new Storage(client);
const databases = new Databases(client);

export { client, account, storage, databases };