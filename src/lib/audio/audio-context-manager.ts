/**
 * AudioContextManager - Centralized Web Audio API context management
 * Handles audio context lifecycle, state management, and global audio settings
 */

export type AudioContextState = 'suspended' | 'running' | 'closed' | 'interrupted'

export interface AudioContextConfig {
  sampleRate?: number
  latencyHint?: AudioContextLatencyCategory
  maxChannelCount?: number
}

export class AudioContextManager {
  private static instance: AudioContextManager | null = null
  private audioContext: AudioContext | null = null
  private state: AudioContextState = 'suspended'
  private listeners: Map<string, (state: AudioContextState) => void> = new Map()
  private config: AudioContextConfig
  private masterGainNode: GainNode | null = null
  private analyserNode: AnalyserNode | null = null

  private constructor(config: AudioContextConfig = {}) {
    this.config = {
      sampleRate: 44100,
      latencyHint: 'interactive',
      maxChannelCount: 2,
      ...config
    }
  }

  /**
   * Get singleton instance of AudioContextManager
   */
  public static getInstance(config?: AudioContextConfig): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager(config)
    }
    return AudioContextManager.instance
  }

  /**
   * Initialize the audio context
   */
  public async initialize(): Promise<void> {
    if (this.audioContext) {
      return
    }

    try {
      this.audioContext = new AudioContext({
        sampleRate: this.config.sampleRate,
        latencyHint: this.config.latencyHint
      })

      // Create master gain node
      this.masterGainNode = this.audioContext.createGain()
      this.masterGainNode.connect(this.audioContext.destination)

      // Create global analyser node
      this.analyserNode = this.audioContext.createAnalyser()
      this.analyserNode.fftSize = 2048
      this.analyserNode.connect(this.masterGainNode)

      // Handle state changes
      this.audioContext.addEventListener('statechange', this.handleStateChange.bind(this))
      
      this.updateState(this.audioContext.state as AudioContextState)

      console.log('AudioContext initialized:', {
        sampleRate: this.audioContext.sampleRate,
        state: this.audioContext.state,
        baseLatency: this.audioContext.baseLatency,
        outputLatency: this.audioContext.outputLatency
      })
    } catch (error) {
      console.error('Failed to initialize AudioContext:', error)
      throw new Error('AudioContext initialization failed')
    }
  }

  /**
   * Resume audio context (required for user interaction)
   */
  public async resume(): Promise<void> {
    if (!this.audioContext) {
      await this.initialize()
    }

    if (this.audioContext?.state === 'suspended') {
      try {
        await this.audioContext.resume()
        console.log('AudioContext resumed')
      } catch (error) {
        console.error('Failed to resume AudioContext:', error)
        throw error
      }
    }
  }

  /**
   * Suspend audio context
   */
  public async suspend(): Promise<void> {
    if (this.audioContext?.state === 'running') {
      try {
        await this.audioContext.suspend()
        console.log('AudioContext suspended')
      } catch (error) {
        console.error('Failed to suspend AudioContext:', error)
        throw error
      }
    }
  }

  /**
   * Close audio context and cleanup
   */
  public async close(): Promise<void> {
    if (this.audioContext) {
      try {
        await this.audioContext.close()
        this.audioContext = null
        this.masterGainNode = null
        this.analyserNode = null
        this.updateState('closed')
        console.log('AudioContext closed')
      } catch (error) {
        console.error('Failed to close AudioContext:', error)
        throw error
      }
    }
  }

  /**
   * Get the current audio context
   */
  public getContext(): AudioContext | null {
    return this.audioContext
  }

  /**
   * Get the master gain node
   */
  public getMasterGainNode(): GainNode | null {
    return this.masterGainNode
  }

  /**
   * Get the global analyser node
   */
  public getAnalyserNode(): AnalyserNode | null {
    return this.analyserNode
  }

  /**
   * Get current state
   */
  public getState(): AudioContextState {
    return this.state
  }

  /**
   * Set master volume
   */
  public setMasterVolume(volume: number): void {
    if (this.masterGainNode) {
      // Clamp volume between 0 and 1
      const clampedVolume = Math.max(0, Math.min(1, volume))
      this.masterGainNode.gain.setValueAtTime(clampedVolume, this.getCurrentTime())
    }
  }

  /**
   * Get current audio time
   */
  public getCurrentTime(): number {
    return this.audioContext?.currentTime || 0
  }

  /**
   * Get sample rate
   */
  public getSampleRate(): number {
    return this.audioContext?.sampleRate || this.config.sampleRate || 44100
  }

  /**
   * Add state change listener
   */
  public addStateListener(id: string, callback: (state: AudioContextState) => void): void {
    this.listeners.set(id, callback)
  }

  /**
   * Remove state change listener
   */
  public removeStateListener(id: string): void {
    this.listeners.delete(id)
  }

  /**
   * Handle audio context state changes
   */
  private handleStateChange(): void {
    if (this.audioContext) {
      this.updateState(this.audioContext.state as AudioContextState)
    }
  }

  /**
   * Update state and notify listeners
   */
  private updateState(newState: AudioContextState): void {
    if (this.state !== newState) {
      this.state = newState
      this.listeners.forEach(callback => callback(newState))
    }
  }

  /**
   * Check if audio context is ready for processing
   */
  public isReady(): boolean {
    return this.audioContext !== null && this.state === 'running'
  }

  /**
   * Get audio context info for debugging
   */
  public getInfo(): object {
    if (!this.audioContext) {
      return { status: 'not_initialized' }
    }

    return {
      state: this.state,
      sampleRate: this.audioContext.sampleRate,
      currentTime: this.audioContext.currentTime,
      baseLatency: this.audioContext.baseLatency,
      outputLatency: this.audioContext.outputLatency,
      maxChannelCount: this.audioContext.destination.maxChannelCount
    }
  }
}

// Export singleton instance getter
export const getAudioContextManager = (config?: AudioContextConfig) => 
  AudioContextManager.getInstance(config)
