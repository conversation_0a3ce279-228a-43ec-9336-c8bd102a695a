/**
 * AudioRouter - Manages audio routing between ReactFlow nodes and Web Audio API nodes
 * Handles the mapping between visual connections and actual audio connections
 */

import { Edge, Node } from '@xyflow/react'
import { BaseAudioNode } from './base-audio-node'
import { getAudioContextManager } from './audio-context-manager'

export interface AudioRouteConnection {
  sourceNodeId: string
  targetNodeId: string
  sourceHandle?: string
  targetHandle?: string
  outputIndex: number
  inputIndex: number
}

export interface AudioNodeFactory {
  createAudioNode(nodeId: string, nodeType: string, nodeData: any): Promise<BaseAudioNode>
}

export class AudioRouter {
  private audioNodes: Map<string, BaseAudioNode> = new Map()
  private connections: Map<string, AudioRouteConnection> = new Map()
  private nodeFactory: AudioNodeFactory | null = null
  private isInitialized = false

  constructor(nodeFactory?: AudioNodeFactory) {
    this.nodeFactory = nodeFactory || null
  }

  /**
   * Initialize the audio router
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    const audioContextManager = getAudioContextManager()
    await audioContextManager.initialize()
    
    this.isInitialized = true
    console.log('AudioRouter initialized')
  }

  /**
   * Set the audio node factory
   */
  public setNodeFactory(factory: AudioNodeFactory): void {
    this.nodeFactory = factory
  }

  /**
   * Create or update audio nodes from ReactFlow nodes
   */
  public async updateNodes(nodes: Node[]): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    // Get current node IDs
    const currentNodeIds = new Set(this.audioNodes.keys())
    const newNodeIds = new Set(nodes.map(node => node.id))

    // Remove deleted nodes
    for (const nodeId of currentNodeIds) {
      if (!newNodeIds.has(nodeId)) {
        await this.removeAudioNode(nodeId)
      }
    }

    // Create or update nodes
    for (const node of nodes) {
      const existingAudioNode = this.audioNodes.get(node.id)
      
      if (!existingAudioNode) {
        // Create new audio node
        await this.createAudioNode(node.id, node.type || 'unknown', node.data)
      } else {
        // Update existing node data
        await this.updateAudioNodeData(node.id, node.data)
      }
    }
  }

  /**
   * Update audio connections from ReactFlow edges
   */
  public async updateConnections(edges: Edge[]): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    // Get current connection IDs
    const currentConnectionIds = new Set(this.connections.keys())
    const newConnectionIds = new Set(edges.map(edge => edge.id))

    // Remove deleted connections
    for (const connectionId of currentConnectionIds) {
      if (!newConnectionIds.has(connectionId)) {
        this.removeConnection(connectionId)
      }
    }

    // Create new connections
    for (const edge of edges) {
      if (!this.connections.has(edge.id)) {
        this.createConnection(edge)
      }
    }
  }

  /**
   * Create a new audio node
   */
  private async createAudioNode(nodeId: string, nodeType: string, nodeData: any): Promise<void> {
    if (!this.nodeFactory) {
      console.warn(`No node factory available for creating ${nodeType} node`)
      return
    }

    try {
      const audioNode = await this.nodeFactory.createAudioNode(nodeId, nodeType, nodeData)
      await audioNode.initialize()
      
      this.audioNodes.set(nodeId, audioNode)
      console.log(`Created audio node: ${nodeId} (${nodeType})`)
    } catch (error) {
      console.error(`Failed to create audio node ${nodeId}:`, error)
    }
  }

  /**
   * Remove an audio node
   */
  private async removeAudioNode(nodeId: string): Promise<void> {
    const audioNode = this.audioNodes.get(nodeId)
    if (audioNode) {
      // Remove all connections involving this node
      const connectionsToRemove: string[] = []
      this.connections.forEach((connection, connectionId) => {
        if (connection.sourceNodeId === nodeId || connection.targetNodeId === nodeId) {
          connectionsToRemove.push(connectionId)
        }
      })
      
      connectionsToRemove.forEach(connectionId => {
        this.removeConnection(connectionId)
      })

      // Dispose the audio node
      audioNode.dispose()
      this.audioNodes.delete(nodeId)
      
      console.log(`Removed audio node: ${nodeId}`)
    }
  }

  /**
   * Update audio node data
   */
  private async updateAudioNodeData(nodeId: string, nodeData: any): Promise<void> {
    const audioNode = this.audioNodes.get(nodeId)
    if (audioNode) {
      // Update parameters based on node data
      this.updateNodeParameters(audioNode, nodeData)
    }
  }

  /**
   * Create a new audio connection
   */
  private createConnection(edge: Edge): void {
    const sourceNode = this.audioNodes.get(edge.source)
    const targetNode = this.audioNodes.get(edge.target)

    if (!sourceNode || !targetNode) {
      console.warn(`Cannot create connection: missing audio nodes for ${edge.source} -> ${edge.target}`)
      return
    }

    // Parse handle information to determine input/output indices
    const outputIndex = this.parseHandleIndex(edge.sourceHandle, 'output')
    const inputIndex = this.parseHandleIndex(edge.targetHandle, 'input')

    try {
      sourceNode.connect(targetNode, outputIndex, inputIndex)
      
      const connection: AudioRouteConnection = {
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        outputIndex,
        inputIndex
      }
      
      this.connections.set(edge.id, connection)
      console.log(`Created audio connection: ${edge.source}[${outputIndex}] -> ${edge.target}[${inputIndex}]`)
    } catch (error) {
      console.error(`Failed to create audio connection ${edge.id}:`, error)
    }
  }

  /**
   * Remove an audio connection
   */
  private removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      return
    }

    const sourceNode = this.audioNodes.get(connection.sourceNodeId)
    const targetNode = this.audioNodes.get(connection.targetNodeId)

    if (sourceNode && targetNode) {
      try {
        sourceNode.disconnect(targetNode, connection.outputIndex, connection.inputIndex)
        console.log(`Removed audio connection: ${connection.sourceNodeId}[${connection.outputIndex}] -> ${connection.targetNodeId}[${connection.inputIndex}]`)
      } catch (error) {
        console.error(`Failed to remove audio connection ${connectionId}:`, error)
      }
    }

    this.connections.delete(connectionId)
  }

  /**
   * Parse handle string to get index
   */
  private parseHandleIndex(handle: string | null | undefined, type: 'input' | 'output'): number {
    if (!handle) {
      return 0
    }

    // Handle formats: "input-0", "output-1", "input-a", "output-b"
    const match = handle.match(new RegExp(`${type}-(\\d+|[a-z])`))
    if (match) {
      const indexStr = match[1]
      if (/^\d+$/.test(indexStr)) {
        return parseInt(indexStr, 10)
      } else {
        // Convert letter to index (a=0, b=1, etc.)
        return indexStr.charCodeAt(0) - 97
      }
    }

    return 0
  }

  /**
   * Update node parameters from data
   */
  private updateNodeParameters(audioNode: BaseAudioNode, nodeData: any): void {
    const parameters = audioNode.getParameters()
    
    Object.keys(parameters).forEach(paramName => {
      if (nodeData.hasOwnProperty(paramName)) {
        const value = nodeData[paramName]
        if (typeof value === 'number') {
          audioNode.setParameter(paramName, value)
        }
      }
    })

    // Handle common properties
    if (typeof nodeData.volume === 'number') {
      audioNode.setParameter('volume', nodeData.volume)
    }
    
    if (typeof nodeData.enabled === 'boolean') {
      audioNode.setEnabled(nodeData.enabled)
    }
    
    if (typeof nodeData.bypassed === 'boolean') {
      audioNode.setBypassed(nodeData.bypassed)
    }
  }

  /**
   * Get audio node by ID
   */
  public getAudioNode(nodeId: string): BaseAudioNode | undefined {
    return this.audioNodes.get(nodeId)
  }

  /**
   * Get all audio nodes
   */
  public getAudioNodes(): Map<string, BaseAudioNode> {
    return new Map(this.audioNodes)
  }

  /**
   * Get all connections
   */
  public getConnections(): Map<string, AudioRouteConnection> {
    return new Map(this.connections)
  }

  /**
   * Cleanup all resources
   */
  public dispose(): void {
    // Remove all connections
    this.connections.clear()
    
    // Dispose all audio nodes
    this.audioNodes.forEach(audioNode => {
      audioNode.dispose()
    })
    this.audioNodes.clear()
    
    this.isInitialized = false
    console.log('AudioRouter disposed')
  }

  /**
   * Get router info for debugging
   */
  public getInfo(): object {
    return {
      isInitialized: this.isInitialized,
      nodeCount: this.audioNodes.size,
      connectionCount: this.connections.size,
      nodes: Array.from(this.audioNodes.keys()),
      connections: Array.from(this.connections.values())
    }
  }
}
