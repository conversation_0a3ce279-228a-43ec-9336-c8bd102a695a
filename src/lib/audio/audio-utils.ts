/**
 * Audio utilities for common audio processing tasks
 */

/**
 * Convert linear gain to decibels
 */
export function gainToDb(gain: number): number {
  return gain > 0 ? 20 * Math.log10(gain) : -Infinity
}

/**
 * Convert decibels to linear gain
 */
export function dbToGain(db: number): number {
  return db === -Infinity ? 0 : Math.pow(10, db / 20)
}

/**
 * Clamp a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value))
}

/**
 * Linear interpolation between two values
 */
export function lerp(a: number, b: number, t: number): number {
  return a + (b - a) * t
}

/**
 * Map a value from one range to another
 */
export function mapRange(
  value: number,
  inMin: number,
  inMax: number,
  outMin: number,
  outMax: number
): number {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin
}

/**
 * Convert frequency to MIDI note number
 */
export function frequencyToMidi(frequency: number): number {
  return 69 + 12 * Math.log2(frequency / 440)
}

/**
 * Convert MIDI note number to frequency
 */
export function midiToFrequency(midi: number): number {
  return 440 * Math.pow(2, (midi - 69) / 12)
}

/**
 * Calculate crossfade gain for two channels
 */
export function calculateCrossfadeGains(
  position: number,
  curve: 'linear' | 'logarithmic' | 'exponential' = 'logarithmic'
): { gainA: number; gainB: number } {
  // Normalize position to 0-1 range
  const normalizedPos = clamp((position + 1) / 2, 0, 1)
  
  let gainA: number
  let gainB: number
  
  switch (curve) {
    case 'linear':
      gainA = 1 - normalizedPos
      gainB = normalizedPos
      break
      
    case 'exponential':
      gainA = Math.pow(1 - normalizedPos, 2)
      gainB = Math.pow(normalizedPos, 2)
      break
      
    case 'logarithmic':
    default:
      // Equal power crossfade
      gainA = Math.cos(normalizedPos * Math.PI / 2)
      gainB = Math.sin(normalizedPos * Math.PI / 2)
      break
  }
  
  return { gainA, gainB }
}

/**
 * Calculate EQ gain for a given frequency band
 */
export function calculateEQGain(
  frequency: number,
  centerFreq: number,
  gain: number,
  q: number = 1
): number {
  if (gain === 0) return 1
  
  const w = 2 * Math.PI * frequency
  const w0 = 2 * Math.PI * centerFreq
  const A = Math.pow(10, gain / 40)
  const alpha = Math.sin(w0) / (2 * q)
  
  // Peaking EQ filter calculation
  const b0 = 1 + alpha * A
  const b1 = -2 * Math.cos(w0)
  const b2 = 1 - alpha * A
  const a0 = 1 + alpha / A
  const a1 = -2 * Math.cos(w0)
  const a2 = 1 - alpha / A
  
  // Calculate magnitude response
  const numerator = Math.sqrt(
    Math.pow(b0 + b2 * Math.cos(2 * w), 2) + Math.pow(b1 * Math.sin(w), 2)
  )
  const denominator = Math.sqrt(
    Math.pow(a0 + a2 * Math.cos(2 * w), 2) + Math.pow(a1 * Math.sin(w), 2)
  )
  
  return numerator / denominator
}

/**
 * Generate waveform data from audio buffer
 */
export function generateWaveformData(
  audioBuffer: AudioBuffer,
  samples: number = 1000
): Float32Array {
  const channelData = audioBuffer.getChannelData(0)
  const blockSize = Math.floor(channelData.length / samples)
  const waveformData = new Float32Array(samples)
  
  for (let i = 0; i < samples; i++) {
    const start = i * blockSize
    const end = Math.min(start + blockSize, channelData.length)
    
    let max = 0
    for (let j = start; j < end; j++) {
      max = Math.max(max, Math.abs(channelData[j]))
    }
    
    waveformData[i] = max
  }
  
  return waveformData
}

/**
 * Detect peaks in audio data
 */
export function detectPeaks(
  audioData: Float32Array,
  threshold: number = 0.5,
  minDistance: number = 10
): number[] {
  const peaks: number[] = []
  
  for (let i = minDistance; i < audioData.length - minDistance; i++) {
    if (audioData[i] > threshold) {
      let isPeak = true
      
      // Check if this is a local maximum
      for (let j = i - minDistance; j <= i + minDistance; j++) {
        if (j !== i && audioData[j] >= audioData[i]) {
          isPeak = false
          break
        }
      }
      
      if (isPeak) {
        peaks.push(i)
        i += minDistance // Skip ahead to avoid duplicate peaks
      }
    }
  }
  
  return peaks
}

/**
 * Calculate RMS (Root Mean Square) of audio data
 */
export function calculateRMS(audioData: Float32Array): number {
  let sum = 0
  for (let i = 0; i < audioData.length; i++) {
    sum += audioData[i] * audioData[i]
  }
  return Math.sqrt(sum / audioData.length)
}

/**
 * Apply fade in/out to audio buffer
 */
export function applyFade(
  audioBuffer: AudioBuffer,
  fadeInDuration: number = 0,
  fadeOutDuration: number = 0
): void {
  const sampleRate = audioBuffer.sampleRate
  const fadeInSamples = Math.floor(fadeInDuration * sampleRate)
  const fadeOutSamples = Math.floor(fadeOutDuration * sampleRate)
  
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    const channelData = audioBuffer.getChannelData(channel)
    
    // Apply fade in
    for (let i = 0; i < fadeInSamples && i < channelData.length; i++) {
      const gain = i / fadeInSamples
      channelData[i] *= gain
    }
    
    // Apply fade out
    const fadeOutStart = channelData.length - fadeOutSamples
    for (let i = fadeOutStart; i < channelData.length; i++) {
      const gain = (channelData.length - i) / fadeOutSamples
      channelData[i] *= gain
    }
  }
}

/**
 * Load audio file and decode to AudioBuffer
 */
export async function loadAudioFile(
  audioContext: AudioContext,
  file: File | ArrayBuffer
): Promise<AudioBuffer> {
  let arrayBuffer: ArrayBuffer
  
  if (file instanceof File) {
    arrayBuffer = await file.arrayBuffer()
  } else {
    arrayBuffer = file
  }
  
  try {
    return await audioContext.decodeAudioData(arrayBuffer)
  } catch (error) {
    console.error('Failed to decode audio data:', error)
    throw new Error('Invalid audio file format')
  }
}

/**
 * Create a simple noise generator
 */
export function createNoiseBuffer(
  audioContext: AudioContext,
  duration: number,
  type: 'white' | 'pink' | 'brown' = 'white'
): AudioBuffer {
  const sampleRate = audioContext.sampleRate
  const length = Math.floor(duration * sampleRate)
  const buffer = audioContext.createBuffer(1, length, sampleRate)
  const channelData = buffer.getChannelData(0)
  
  switch (type) {
    case 'white':
      for (let i = 0; i < length; i++) {
        channelData[i] = Math.random() * 2 - 1
      }
      break
      
    case 'pink':
      let b0 = 0, b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0
      for (let i = 0; i < length; i++) {
        const white = Math.random() * 2 - 1
        b0 = 0.99886 * b0 + white * 0.0555179
        b1 = 0.99332 * b1 + white * 0.0750759
        b2 = 0.96900 * b2 + white * 0.1538520
        b3 = 0.86650 * b3 + white * 0.3104856
        b4 = 0.55000 * b4 + white * 0.5329522
        b5 = -0.7616 * b5 - white * 0.0168980
        channelData[i] = b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362
        b6 = white * 0.115926
      }
      break
      
    case 'brown':
      let lastOut = 0
      for (let i = 0; i < length; i++) {
        const white = Math.random() * 2 - 1
        channelData[i] = (lastOut + (0.02 * white)) / 1.02
        lastOut = channelData[i]
        channelData[i] *= 3.5 // Compensate for volume
      }
      break
  }
  
  return buffer
}

/**
 * Time formatting utilities
 */
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

export function parseTime(timeString: string): number {
  const parts = timeString.split(':')
  if (parts.length === 2) {
    const minutes = parseInt(parts[0], 10)
    const seconds = parseInt(parts[1], 10)
    return minutes * 60 + seconds
  }
  return 0
}
