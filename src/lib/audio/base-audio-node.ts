/**
 * BaseAudioNode - Abstract base class for all audio processing nodes
 * Provides common functionality for audio input/output connections and parameter management
 */

import { getAudioContextManager, AudioContextManager } from './audio-context-manager'

export interface AudioNodeConnection {
  nodeId: string
  outputIndex?: number
  inputIndex?: number
}

export interface AudioParameter {
  name: string
  value: number
  min: number
  max: number
  defaultValue: number
  unit?: string
  automatable?: boolean
}

export interface AudioNodeConfig {
  id: string
  type: string
  inputs?: number
  outputs?: number
  parameters?: Record<string, AudioParameter>
}

export abstract class BaseAudioNode {
  protected id: string
  protected type: string
  protected audioContextManager: AudioContextManager
  protected audioContext: AudioContext | null = null
  
  // Audio nodes
  protected inputNodes: (AudioNode | null)[] = []
  protected outputNodes: (AudioNode | null)[] = []
  protected gainNode: GainNode | null = null
  
  // Connections
  protected inputConnections: Map<number, AudioNodeConnection[]> = new Map()
  protected outputConnections: Map<number, AudioNodeConnection[]> = new Map()
  
  // Parameters
  protected parameters: Map<string, AudioParameter> = new Map()
  protected parameterNodes: Map<string, AudioParam> = new Map()
  
  // State
  protected isInitialized = false
  protected isEnabled = true
  protected isBypassed = false

  constructor(config: AudioNodeConfig) {
    this.id = config.id
    this.type = config.type
    this.audioContextManager = getAudioContextManager()
    
    // Initialize input/output arrays
    const inputCount = config.inputs || 1
    const outputCount = config.outputs || 1
    
    this.inputNodes = new Array(inputCount).fill(null)
    this.outputNodes = new Array(outputCount).fill(null)
    
    // Initialize parameters
    if (config.parameters) {
      Object.entries(config.parameters).forEach(([name, param]) => {
        this.parameters.set(name, { ...param })
      })
    }
  }

  /**
   * Initialize the audio node
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    this.audioContext = this.audioContextManager.getContext()
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // Create gain node for volume control
    this.gainNode = this.audioContext.createGain()
    
    // Create audio processing nodes
    await this.createAudioNodes()
    
    // Initialize parameters
    this.initializeParameters()
    
    this.isInitialized = true
    console.log(`AudioNode ${this.id} (${this.type}) initialized`)
  }

  /**
   * Abstract method to create specific audio nodes
   */
  protected abstract createAudioNodes(): Promise<void>

  /**
   * Connect this node's output to another node's input
   */
  public connect(targetNode: BaseAudioNode, outputIndex = 0, inputIndex = 0): void {
    if (!this.isInitialized || !targetNode.isInitialized) {
      throw new Error('Nodes must be initialized before connecting')
    }

    const outputNode = this.getOutputNode(outputIndex)
    const inputNode = targetNode.getInputNode(inputIndex)

    if (outputNode && inputNode) {
      outputNode.connect(inputNode)
      
      // Track connections
      const connection: AudioNodeConnection = {
        nodeId: targetNode.getId(),
        outputIndex,
        inputIndex
      }
      
      if (!this.outputConnections.has(outputIndex)) {
        this.outputConnections.set(outputIndex, [])
      }
      this.outputConnections.get(outputIndex)!.push(connection)
      
      if (!targetNode.inputConnections.has(inputIndex)) {
        targetNode.inputConnections.set(inputIndex, [])
      }
      targetNode.inputConnections.get(inputIndex)!.push({
        nodeId: this.id,
        outputIndex,
        inputIndex
      })
      
      console.log(`Connected ${this.id}[${outputIndex}] -> ${targetNode.getId()}[${inputIndex}]`)
    }
  }

  /**
   * Disconnect this node's output from another node's input
   */
  public disconnect(targetNode?: BaseAudioNode, outputIndex = 0, inputIndex = 0): void {
    const outputNode = this.getOutputNode(outputIndex)
    
    if (outputNode) {
      if (targetNode) {
        const inputNode = targetNode.getInputNode(inputIndex)
        if (inputNode) {
          outputNode.disconnect(inputNode)
          
          // Remove connection tracking
          const connections = this.outputConnections.get(outputIndex)
          if (connections) {
            const index = connections.findIndex(c => 
              c.nodeId === targetNode.getId() && c.inputIndex === inputIndex
            )
            if (index >= 0) {
              connections.splice(index, 1)
            }
          }
          
          const targetConnections = targetNode.inputConnections.get(inputIndex)
          if (targetConnections) {
            const index = targetConnections.findIndex(c => 
              c.nodeId === this.id && c.outputIndex === outputIndex
            )
            if (index >= 0) {
              targetConnections.splice(index, 1)
            }
          }
        }
      } else {
        // Disconnect all
        outputNode.disconnect()
        this.outputConnections.set(outputIndex, [])
      }
    }
  }

  /**
   * Set parameter value
   */
  public setParameter(name: string, value: number, rampTime = 0): void {
    const parameter = this.parameters.get(name)
    const audioParam = this.parameterNodes.get(name)
    
    if (parameter && audioParam) {
      // Clamp value to parameter range
      const clampedValue = Math.max(parameter.min, Math.min(parameter.max, value))
      
      if (rampTime > 0) {
        const currentTime = this.audioContextManager.getCurrentTime()
        audioParam.linearRampToValueAtTime(clampedValue, currentTime + rampTime)
      } else {
        audioParam.setValueAtTime(clampedValue, this.audioContextManager.getCurrentTime())
      }
      
      // Update parameter value
      parameter.value = clampedValue
      this.parameters.set(name, parameter)
    }
  }

  /**
   * Get parameter value
   */
  public getParameter(name: string): number | undefined {
    return this.parameters.get(name)?.value
  }

  /**
   * Get all parameters
   */
  public getParameters(): Record<string, AudioParameter> {
    const params: Record<string, AudioParameter> = {}
    this.parameters.forEach((param, name) => {
      params[name] = { ...param }
    })
    return params
  }

  /**
   * Enable/disable the node
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    if (this.gainNode) {
      const gain = enabled && !this.isBypassed ? 1 : 0
      this.gainNode.gain.setValueAtTime(gain, this.audioContextManager.getCurrentTime())
    }
  }

  /**
   * Bypass the node (pass audio through without processing)
   */
  public setBypassed(bypassed: boolean): void {
    this.isBypassed = bypassed
    if (this.gainNode) {
      const gain = this.isEnabled && !bypassed ? 1 : 0
      this.gainNode.gain.setValueAtTime(gain, this.audioContextManager.getCurrentTime())
    }
  }

  /**
   * Get node ID
   */
  public getId(): string {
    return this.id
  }

  /**
   * Get node type
   */
  public getType(): string {
    return this.type
  }

  /**
   * Get input node at index
   */
  protected getInputNode(index: number): AudioNode | null {
    return this.inputNodes[index] || null
  }

  /**
   * Get output node at index
   */
  protected getOutputNode(index: number): AudioNode | null {
    return this.outputNodes[index] || null
  }

  /**
   * Initialize parameter audio params
   */
  protected initializeParameters(): void {
    // Override in subclasses to map parameters to AudioParam objects
  }

  /**
   * Cleanup resources
   */
  public dispose(): void {
    // Disconnect all connections
    this.outputConnections.forEach((_, outputIndex) => {
      this.disconnect(undefined, outputIndex)
    })
    
    // Clear references
    this.inputNodes = []
    this.outputNodes = []
    this.gainNode = null
    this.parameterNodes.clear()
    this.inputConnections.clear()
    this.outputConnections.clear()
    
    this.isInitialized = false
    console.log(`AudioNode ${this.id} disposed`)
  }

  /**
   * Get node info for debugging
   */
  public getInfo(): object {
    return {
      id: this.id,
      type: this.type,
      isInitialized: this.isInitialized,
      isEnabled: this.isEnabled,
      isBypassed: this.isBypassed,
      inputCount: this.inputNodes.length,
      outputCount: this.outputNodes.length,
      parameters: Object.fromEntries(this.parameters),
      connections: {
        inputs: Object.fromEntries(this.inputConnections),
        outputs: Object.fromEntries(this.outputConnections)
      }
    }
  }
}
