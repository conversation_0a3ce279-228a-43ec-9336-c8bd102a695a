/**
 * BeatDetector - Real-time beat detection and BPM analysis
 * Implements onset detection and tempo estimation for DJ applications
 */

export interface BeatInfo {
  bpm: number
  confidence: number
  beats: number[] // Beat positions in seconds
  beatGrid: number[] // Quantized beat grid
  timeSignature: [number, number] // e.g., [4, 4]
  firstBeatTime: number
}

export interface OnsetDetectionConfig {
  fftSize: number
  hopSize: number
  sampleRate: number
  threshold: number
  minBpm: number
  maxBpm: number
}

export class BeatDetector {
  private config: OnsetDetectionConfig
  private audioContext: AudioContext
  private analyserNode: AnalyserNode | null = null
  private frequencyData: Float32Array
  private previousSpectrum: Float32Array
  private onsetTimes: number[] = []
  private isAnalyzing = false

  constructor(audioContext: AudioContext, config?: Partial<OnsetDetectionConfig>) {
    this.audioContext = audioContext
    this.config = {
      fftSize: 2048,
      hopSize: 512,
      sampleRate: audioContext.sampleRate,
      threshold: 0.3,
      minBpm: 60,
      maxBpm: 200,
      ...config
    }

    this.frequencyData = new Float32Array(this.config.fftSize / 2)
    this.previousSpectrum = new Float32Array(this.config.fftSize / 2)
  }

  /**
   * Analyze audio buffer for beat information
   */
  public async analyzeAudioBuffer(audioBuffer: AudioBuffer): Promise<BeatInfo> {
    const channelData = audioBuffer.getChannelData(0)
    const sampleRate = audioBuffer.sampleRate
    
    // Detect onsets using spectral flux
    const onsets = this.detectOnsets(channelData, sampleRate)
    
    // Estimate BPM from onset intervals
    const bpmAnalysis = this.estimateBPM(onsets, audioBuffer.duration)
    
    // Generate beat grid
    const beatGrid = this.generateBeatGrid(bpmAnalysis.bpm, audioBuffer.duration, bpmAnalysis.firstBeat)
    
    return {
      bpm: bpmAnalysis.bpm,
      confidence: bpmAnalysis.confidence,
      beats: onsets,
      beatGrid,
      timeSignature: [4, 4], // Default to 4/4
      firstBeatTime: bpmAnalysis.firstBeat
    }
  }

  /**
   * Start real-time beat detection
   */
  public startRealTimeDetection(sourceNode: AudioNode): void {
    if (this.isAnalyzing) return

    this.analyserNode = this.audioContext.createAnalyser()
    this.analyserNode.fftSize = this.config.fftSize
    this.analyserNode.smoothingTimeConstant = 0.3

    sourceNode.connect(this.analyserNode)
    
    this.isAnalyzing = true
    this.onsetTimes = []
    this.analyzeRealTime()
  }

  /**
   * Stop real-time beat detection
   */
  public stopRealTimeDetection(): void {
    this.isAnalyzing = false
    if (this.analyserNode) {
      this.analyserNode.disconnect()
      this.analyserNode = null
    }
  }

  /**
   * Get current BPM estimate from real-time analysis
   */
  public getCurrentBPM(): number {
    if (this.onsetTimes.length < 4) return 0

    const recentOnsets = this.onsetTimes.slice(-8) // Use last 8 onsets
    const intervals = []
    
    for (let i = 1; i < recentOnsets.length; i++) {
      intervals.push(recentOnsets[i] - recentOnsets[i - 1])
    }
    
    if (intervals.length === 0) return 0
    
    // Calculate median interval to avoid outliers
    intervals.sort((a, b) => a - b)
    const medianInterval = intervals[Math.floor(intervals.length / 2)]
    
    return 60 / medianInterval
  }

  /**
   * Detect onsets in audio data using spectral flux
   */
  private detectOnsets(audioData: Float32Array, sampleRate: number): number[] {
    const onsets: number[] = []
    const windowSize = 2048
    const hopSize = 512
    const threshold = this.config.threshold

    for (let i = 0; i < audioData.length - windowSize; i += hopSize) {
      const window = audioData.slice(i, i + windowSize)
      const spectrum = this.computeSpectrum(window)
      
      if (i > 0) {
        const flux = this.computeSpectralFlux(spectrum, this.previousSpectrum)
        
        if (flux > threshold) {
          const onsetTime = i / sampleRate
          
          // Avoid duplicate onsets too close together
          if (onsets.length === 0 || onsetTime - onsets[onsets.length - 1] > 0.1) {
            onsets.push(onsetTime)
          }
        }
      }
      
      this.previousSpectrum.set(spectrum)
    }

    return onsets
  }

  /**
   * Compute magnitude spectrum using FFT
   */
  private computeSpectrum(window: Float32Array): Float32Array {
    // Simple magnitude spectrum calculation
    // In a real implementation, you'd use a proper FFT library
    const spectrum = new Float32Array(window.length / 2)
    
    for (let i = 0; i < spectrum.length; i++) {
      const real = window[i * 2] || 0
      const imag = window[i * 2 + 1] || 0
      spectrum[i] = Math.sqrt(real * real + imag * imag)
    }
    
    return spectrum
  }

  /**
   * Compute spectral flux between two spectra
   */
  private computeSpectralFlux(current: Float32Array, previous: Float32Array): number {
    let flux = 0
    
    for (let i = 0; i < current.length; i++) {
      const diff = current[i] - previous[i]
      if (diff > 0) {
        flux += diff
      }
    }
    
    return flux / current.length
  }

  /**
   * Estimate BPM from onset times
   */
  private estimateBPM(onsets: number[], duration: number): { bpm: number; confidence: number; firstBeat: number } {
    if (onsets.length < 4) {
      return { bpm: 120, confidence: 0, firstBeat: 0 }
    }

    // Calculate intervals between onsets
    const intervals: number[] = []
    for (let i = 1; i < onsets.length; i++) {
      intervals.push(onsets[i] - onsets[i - 1])
    }

    // Group intervals into tempo candidates
    const tempoCandidates = new Map<number, number>()
    
    for (const interval of intervals) {
      const bpm = 60 / interval
      
      if (bpm >= this.config.minBpm && bpm <= this.config.maxBpm) {
        // Round to nearest 0.1 BPM for grouping
        const roundedBpm = Math.round(bpm * 10) / 10
        tempoCandidates.set(roundedBpm, (tempoCandidates.get(roundedBpm) || 0) + 1)
      }
      
      // Also check double and half tempo
      const doubleBpm = bpm * 2
      const halfBpm = bpm / 2
      
      if (doubleBpm >= this.config.minBpm && doubleBpm <= this.config.maxBpm) {
        const roundedDouble = Math.round(doubleBpm * 10) / 10
        tempoCandidates.set(roundedDouble, (tempoCandidates.get(roundedDouble) || 0) + 0.5)
      }
      
      if (halfBpm >= this.config.minBpm && halfBpm <= this.config.maxBpm) {
        const roundedHalf = Math.round(halfBpm * 10) / 10
        tempoCandidates.set(roundedHalf, (tempoCandidates.get(roundedHalf) || 0) + 0.5)
      }
    }

    // Find the most likely BPM
    let bestBpm = 120
    let bestScore = 0
    
    for (const [bpm, score] of tempoCandidates) {
      if (score > bestScore) {
        bestScore = score
        bestBpm = bpm
      }
    }

    // Calculate confidence based on consistency
    const confidence = Math.min(bestScore / intervals.length, 1.0)
    
    // Estimate first beat time
    const beatInterval = 60 / bestBpm
    const firstBeat = this.findFirstBeat(onsets, beatInterval)

    return { bpm: bestBpm, confidence, firstBeat }
  }

  /**
   * Find the most likely first beat time
   */
  private findFirstBeat(onsets: number[], beatInterval: number): number {
    if (onsets.length === 0) return 0

    // Try different phase offsets and find the one that aligns best with onsets
    const candidates: { offset: number; score: number }[] = []
    
    for (let i = 0; i < Math.min(4, onsets.length); i++) {
      const offset = onsets[i] % beatInterval
      let score = 0
      
      // Count how many onsets align with this beat grid
      for (const onset of onsets) {
        const beatPosition = (onset - offset) / beatInterval
        const nearestBeat = Math.round(beatPosition)
        const error = Math.abs(beatPosition - nearestBeat)
        
        if (error < 0.1) { // Within 10% of beat interval
          score += 1 - error
        }
      }
      
      candidates.push({ offset, score })
    }
    
    // Return the offset with the highest score
    candidates.sort((a, b) => b.score - a.score)
    return candidates[0]?.offset || 0
  }

  /**
   * Generate a quantized beat grid
   */
  private generateBeatGrid(bpm: number, duration: number, firstBeat: number): number[] {
    const beatInterval = 60 / bpm
    const beats: number[] = []
    
    // Start from the first beat and generate beats throughout the track
    for (let time = firstBeat; time < duration; time += beatInterval) {
      beats.push(time)
    }
    
    // Also add beats before the first detected beat if needed
    for (let time = firstBeat - beatInterval; time >= 0; time -= beatInterval) {
      beats.unshift(time)
    }
    
    return beats.sort((a, b) => a - b)
  }

  /**
   * Real-time analysis loop
   */
  private analyzeRealTime(): void {
    if (!this.isAnalyzing || !this.analyserNode) return

    this.analyserNode.getFloatFrequencyData(this.frequencyData)
    
    // Compute spectral flux
    const flux = this.computeSpectralFlux(this.frequencyData, this.previousSpectrum)
    
    if (flux > this.config.threshold) {
      const currentTime = this.audioContext.currentTime
      
      // Avoid duplicate onsets
      if (this.onsetTimes.length === 0 || currentTime - this.onsetTimes[this.onsetTimes.length - 1] > 0.1) {
        this.onsetTimes.push(currentTime)
        
        // Keep only recent onsets (last 10 seconds)
        this.onsetTimes = this.onsetTimes.filter(time => currentTime - time < 10)
      }
    }
    
    this.previousSpectrum.set(this.frequencyData)
    
    // Continue analysis
    requestAnimationFrame(() => this.analyzeRealTime())
  }

  /**
   * Get detected onsets from real-time analysis
   */
  public getRecentOnsets(): number[] {
    return [...this.onsetTimes]
  }

  /**
   * Clear onset history
   */
  public clearOnsets(): void {
    this.onsetTimes = []
  }
}
