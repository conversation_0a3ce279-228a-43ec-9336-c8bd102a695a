/**
 * ChannelMixerAudioNode - Professional channel mixer with 3-band EQ
 * Provides gain control, 3-band EQ, cue system, and kill switches
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'

export interface ChannelMixerAudioNodeData {
  channel: string
  volume: number
  gain: number
  highEQ: number
  midEQ: number
  lowEQ: number
  highKill: boolean
  midKill: boolean
  lowKill: boolean
  cue: boolean
  pfl: boolean // Pre-Fader Listen
}

export class ChannelMixerAudioNode extends BaseAudioNode {
  private inputGainNode: GainNode | null = null
  private channelGainNode: GainNode | null = null
  private cueGainNode: GainNode | null = null
  
  // EQ nodes
  private highShelfFilter: BiquadFilterNode | null = null
  private midPeakingFilter: BiquadFilterNode | null = null
  private lowShelfFilter: BiquadFilterNode | null = null
  
  // Kill switch nodes
  private highKillGain: GainNode | null = null
  private midKillGain: GainNode | null = null
  private lowKillGain: GainNode | null = null
  
  // Output routing
  private mainOutputGain: GainNode | null = null
  private cueOutputGain: GainNode | null = null
  
  private channelData: ChannelMixerAudioNodeData

  constructor(config: AudioNodeConfig & { data?: ChannelMixerAudioNodeData }) {
    const mixerConfig: AudioNodeConfig = {
      ...config,
      inputs: 1,
      outputs: 2, // Main output and cue output
      parameters: {
        volume: {
          name: 'volume',
          value: config.data?.volume || 0.8,
          min: 0,
          max: 1,
          defaultValue: 0.8,
          unit: 'linear',
          automatable: true
        },
        gain: {
          name: 'gain',
          value: config.data?.gain || 0,
          min: -20,
          max: 20,
          defaultValue: 0,
          unit: 'dB',
          automatable: true
        },
        highEQ: {
          name: 'highEQ',
          value: config.data?.highEQ || 0,
          min: -15,
          max: 15,
          defaultValue: 0,
          unit: 'dB',
          automatable: true
        },
        midEQ: {
          name: 'midEQ',
          value: config.data?.midEQ || 0,
          min: -15,
          max: 15,
          defaultValue: 0,
          unit: 'dB',
          automatable: true
        },
        lowEQ: {
          name: 'lowEQ',
          value: config.data?.lowEQ || 0,
          min: -15,
          max: 15,
          defaultValue: 0,
          unit: 'dB',
          automatable: true
        }
      }
    }

    super(mixerConfig)
    
    this.channelData = {
      channel: config.data?.channel || 'A',
      volume: config.data?.volume || 0.8,
      gain: config.data?.gain || 0,
      highEQ: config.data?.highEQ || 0,
      midEQ: config.data?.midEQ || 0,
      lowEQ: config.data?.lowEQ || 0,
      highKill: config.data?.highKill || false,
      midKill: config.data?.midKill || false,
      lowKill: config.data?.lowKill || false,
      cue: config.data?.cue || false,
      pfl: config.data?.pfl || false
    }
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // Input gain (trim/gain control)
    this.inputGainNode = this.audioContext.createGain()
    
    // 3-band EQ chain
    this.createEQChain()
    
    // Kill switch gains
    this.highKillGain = this.audioContext.createGain()
    this.midKillGain = this.audioContext.createGain()
    this.lowKillGain = this.audioContext.createGain()
    
    // Channel volume
    this.channelGainNode = this.audioContext.createGain()
    
    // Output routing
    this.mainOutputGain = this.audioContext.createGain()
    this.cueOutputGain = this.audioContext.createGain()
    
    // Connect the audio chain
    this.connectAudioChain()
    
    // Set up input/output nodes
    this.inputNodes[0] = this.inputGainNode
    this.outputNodes[0] = this.mainOutputGain // Main output
    this.outputNodes[1] = this.cueOutputGain // Cue output
    
    console.log(`Channel Mixer ${this.id} (Channel ${this.channelData.channel}) created`)
  }

  /**
   * Create 3-band EQ chain
   */
  private createEQChain(): void {
    if (!this.audioContext) return

    // High shelf filter (10kHz)
    this.highShelfFilter = this.audioContext.createBiquadFilter()
    this.highShelfFilter.type = 'highshelf'
    this.highShelfFilter.frequency.value = 10000
    this.highShelfFilter.Q.value = 0.7

    // Mid peaking filter (1kHz)
    this.midPeakingFilter = this.audioContext.createBiquadFilter()
    this.midPeakingFilter.type = 'peaking'
    this.midPeakingFilter.frequency.value = 1000
    this.midPeakingFilter.Q.value = 1.0

    // Low shelf filter (100Hz)
    this.lowShelfFilter = this.audioContext.createBiquadFilter()
    this.lowShelfFilter.type = 'lowshelf'
    this.lowShelfFilter.frequency.value = 100
    this.lowShelfFilter.Q.value = 0.7
  }

  /**
   * Connect the complete audio processing chain
   */
  private connectAudioChain(): void {
    if (!this.inputGainNode || !this.channelGainNode || !this.mainOutputGain || !this.cueOutputGain) return
    if (!this.highShelfFilter || !this.midPeakingFilter || !this.lowShelfFilter) return
    if (!this.highKillGain || !this.midKillGain || !this.lowKillGain) return

    // Main signal chain: Input -> Gain -> EQ -> Kill Switches -> Channel Volume -> Outputs
    this.inputGainNode.connect(this.highShelfFilter)
    this.highShelfFilter.connect(this.highKillGain)
    this.highKillGain.connect(this.midPeakingFilter)
    this.midPeakingFilter.connect(this.midKillGain)
    this.midKillGain.connect(this.lowShelfFilter)
    this.lowShelfFilter.connect(this.lowKillGain)
    this.lowKillGain.connect(this.channelGainNode)
    
    // Route to outputs
    this.channelGainNode.connect(this.mainOutputGain)
    this.channelGainNode.connect(this.cueOutputGain)
  }

  /**
   * Initialize parameter mappings
   */
  protected initializeParameters(): void {
    if (!this.inputGainNode || !this.channelGainNode) return
    if (!this.highShelfFilter || !this.midPeakingFilter || !this.lowShelfFilter) return

    // Map parameters to audio params
    this.parameterNodes.set('gain', this.inputGainNode.gain)
    this.parameterNodes.set('volume', this.channelGainNode.gain)
    this.parameterNodes.set('highEQ', this.highShelfFilter.gain)
    this.parameterNodes.set('midEQ', this.midPeakingFilter.gain)
    this.parameterNodes.set('lowEQ', this.lowShelfFilter.gain)

    // Set initial values
    this.updateParameterValues()
  }

  /**
   * Update all parameter values
   */
  private updateParameterValues(): void {
    const currentTime = this.getCurrentTime()
    
    // Convert gain from dB to linear
    const gainLinear = Math.pow(10, this.channelData.gain / 20)
    this.inputGainNode?.gain.setValueAtTime(gainLinear, currentTime)
    
    // Set volume
    this.channelGainNode?.gain.setValueAtTime(this.channelData.volume, currentTime)
    
    // Set EQ values
    this.highShelfFilter?.gain.setValueAtTime(this.channelData.highEQ, currentTime)
    this.midPeakingFilter?.gain.setValueAtTime(this.channelData.midEQ, currentTime)
    this.lowShelfFilter?.gain.setValueAtTime(this.channelData.lowEQ, currentTime)
    
    // Set kill switches
    this.updateKillSwitches()
    
    // Set cue routing
    this.updateCueRouting()
  }

  /**
   * Set channel volume
   */
  public setVolume(volume: number): void {
    this.channelData.volume = Math.max(0, Math.min(1, volume))
    this.setParameter('volume', this.channelData.volume)
  }

  /**
   * Set input gain in dB
   */
  public setGain(gainDb: number): void {
    this.channelData.gain = Math.max(-20, Math.min(20, gainDb))
    this.setParameter('gain', this.channelData.gain)
  }

  /**
   * Set high EQ in dB
   */
  public setHighEQ(gainDb: number): void {
    this.channelData.highEQ = Math.max(-15, Math.min(15, gainDb))
    this.setParameter('highEQ', this.channelData.highEQ)
  }

  /**
   * Set mid EQ in dB
   */
  public setMidEQ(gainDb: number): void {
    this.channelData.midEQ = Math.max(-15, Math.min(15, gainDb))
    this.setParameter('midEQ', this.channelData.midEQ)
  }

  /**
   * Set low EQ in dB
   */
  public setLowEQ(gainDb: number): void {
    this.channelData.lowEQ = Math.max(-15, Math.min(15, gainDb))
    this.setParameter('lowEQ', this.channelData.lowEQ)
  }

  /**
   * Toggle high frequency kill switch
   */
  public setHighKill(kill: boolean): void {
    this.channelData.highKill = kill
    this.updateKillSwitches()
  }

  /**
   * Toggle mid frequency kill switch
   */
  public setMidKill(kill: boolean): void {
    this.channelData.midKill = kill
    this.updateKillSwitches()
  }

  /**
   * Toggle low frequency kill switch
   */
  public setLowKill(kill: boolean): void {
    this.channelData.lowKill = kill
    this.updateKillSwitches()
  }

  /**
   * Update kill switch states
   */
  private updateKillSwitches(): void {
    const currentTime = this.getCurrentTime()
    
    this.highKillGain?.gain.setValueAtTime(
      this.channelData.highKill ? 0 : 1,
      currentTime
    )
    
    this.midKillGain?.gain.setValueAtTime(
      this.channelData.midKill ? 0 : 1,
      currentTime
    )
    
    this.lowKillGain?.gain.setValueAtTime(
      this.channelData.lowKill ? 0 : 1,
      currentTime
    )
  }

  /**
   * Set cue (headphone) monitoring
   */
  public setCue(enabled: boolean): void {
    this.channelData.cue = enabled
    this.updateCueRouting()
  }

  /**
   * Set pre-fader listen
   */
  public setPFL(enabled: boolean): void {
    this.channelData.pfl = enabled
    this.updateCueRouting()
  }

  /**
   * Update cue output routing
   */
  private updateCueRouting(): void {
    const currentTime = this.getCurrentTime()
    const cueLevel = this.channelData.cue ? 1 : 0
    
    this.cueOutputGain?.gain.setValueAtTime(cueLevel, currentTime)
  }

  /**
   * Get current channel data
   */
  public getChannelData(): ChannelMixerAudioNodeData {
    return { ...this.channelData }
  }

  /**
   * Get extended info
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()
    
    return {
      ...baseInfo,
      channelData: this.channelData,
      eqFrequencies: {
        high: this.highShelfFilter?.frequency.value,
        mid: this.midPeakingFilter?.frequency.value,
        low: this.lowShelfFilter?.frequency.value
      }
    }
  }
}
