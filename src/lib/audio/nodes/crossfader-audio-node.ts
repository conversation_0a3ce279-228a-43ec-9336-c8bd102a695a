/**
 * CrossfaderAudioNode - Professional crossfader with multiple curve types
 * Provides smooth crossfading between two audio sources with cut modes
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'
import { calculateCrossfadeGains } from '../audio-utils'

export type CrossfaderCurve = 'linear' | 'logarithmic' | 'exponential' | 'cut'
export type CrossfaderMode = 'normal' | 'hamster' | 'scratch'

export interface CrossfaderAudioNodeData {
  position: number // -1 (full A) to +1 (full B)
  curve: CrossfaderCurve
  mode: CrossfaderMode
  cutPoint: number // 0-1, where the cut happens in cut mode
  reverse: boolean
}

export class CrossfaderAudioNode extends BaseAudioNode {
  private inputAGain: GainNode | null = null
  private inputBGain: GainNode | null = null
  private outputMixGain: GainNode | null = null
  
  private crossfaderData: CrossfaderAudioNodeData

  constructor(config: AudioNodeConfig & { data?: CrossfaderAudioNodeData }) {
    const crossfaderConfig: AudioNodeConfig = {
      ...config,
      inputs: 2, // Input A and Input B
      outputs: 1, // Mixed output
      parameters: {
        position: {
          name: 'position',
          value: config.data?.position || 0,
          min: -1,
          max: 1,
          defaultValue: 0,
          unit: 'position',
          automatable: true
        },
        cutPoint: {
          name: 'cutPoint',
          value: config.data?.cutPoint || 0.5,
          min: 0,
          max: 1,
          defaultValue: 0.5,
          unit: 'position',
          automatable: true
        }
      }
    }

    super(crossfaderConfig)
    
    this.crossfaderData = {
      position: config.data?.position || 0,
      curve: config.data?.curve || 'logarithmic',
      mode: config.data?.mode || 'normal',
      cutPoint: config.data?.cutPoint || 0.5,
      reverse: config.data?.reverse || false
    }
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // Create gain nodes for each input
    this.inputAGain = this.audioContext.createGain()
    this.inputBGain = this.audioContext.createGain()
    
    // Create output mix node
    this.outputMixGain = this.audioContext.createGain()
    
    // Connect inputs to their respective gain nodes
    this.inputNodes[0] = this.inputAGain // Input A
    this.inputNodes[1] = this.inputBGain // Input B
    
    // Connect both inputs to the output mix
    this.inputAGain.connect(this.outputMixGain)
    this.inputBGain.connect(this.outputMixGain)
    
    // Set output node
    this.outputNodes[0] = this.outputMixGain
    
    // Initialize crossfader position
    this.updateCrossfaderGains()
    
    console.log(`Crossfader ${this.id} created with ${this.crossfaderData.curve} curve`)
  }

  /**
   * Initialize parameter mappings
   */
  protected initializeParameters(): void {
    // Position parameter is handled manually for complex crossfade calculations
    // Cut point parameter for cut mode
  }

  /**
   * Set crossfader position (-1 to +1)
   */
  public setPosition(position: number): void {
    this.crossfaderData.position = Math.max(-1, Math.min(1, position))
    this.setParameter('position', this.crossfaderData.position)
    this.updateCrossfaderGains()
  }

  /**
   * Set crossfader curve type
   */
  public setCurve(curve: CrossfaderCurve): void {
    this.crossfaderData.curve = curve
    this.updateCrossfaderGains()
    console.log(`Crossfader ${this.id} curve set to ${curve}`)
  }

  /**
   * Set crossfader mode
   */
  public setMode(mode: CrossfaderMode): void {
    this.crossfaderData.mode = mode
    this.updateCrossfaderGains()
    console.log(`Crossfader ${this.id} mode set to ${mode}`)
  }

  /**
   * Set cut point for cut mode
   */
  public setCutPoint(cutPoint: number): void {
    this.crossfaderData.cutPoint = Math.max(0, Math.min(1, cutPoint))
    this.setParameter('cutPoint', this.crossfaderData.cutPoint)
    this.updateCrossfaderGains()
  }

  /**
   * Toggle reverse mode
   */
  public setReverse(reverse: boolean): void {
    this.crossfaderData.reverse = reverse
    this.updateCrossfaderGains()
  }

  /**
   * Update crossfader gain values based on position and curve
   */
  private updateCrossfaderGains(): void {
    if (!this.inputAGain || !this.inputBGain) return

    const currentTime = this.getCurrentTime()
    let position = this.crossfaderData.position
    
    // Apply reverse if enabled
    if (this.crossfaderData.reverse) {
      position = -position
    }
    
    let gainA: number
    let gainB: number

    switch (this.crossfaderData.curve) {
      case 'cut':
        ({ gainA, gainB } = this.calculateCutGains(position))
        break
        
      case 'linear':
      case 'logarithmic':
      case 'exponential':
        ({ gainA, gainB } = calculateCrossfadeGains(position, this.crossfaderData.curve))
        break
        
      default:
        ({ gainA, gainB } = calculateCrossfadeGains(position, 'logarithmic'))
    }

    // Apply mode-specific modifications
    switch (this.crossfaderData.mode) {
      case 'hamster':
        // Hamster mode: reverse the gains when moving in opposite direction
        if (this.isHamsterDirection(position)) {
          [gainA, gainB] = [gainB, gainA]
        }
        break
        
      case 'scratch':
        // Scratch mode: more aggressive cuts for scratching
        gainA = gainA > 0.1 ? gainA : 0
        gainB = gainB > 0.1 ? gainB : 0
        break
    }

    // Apply gains with smooth transitions
    this.inputAGain.gain.linearRampToValueAtTime(gainA, currentTime + 0.01)
    this.inputBGain.gain.linearRampToValueAtTime(gainB, currentTime + 0.01)
  }

  /**
   * Calculate gains for cut mode
   */
  private calculateCutGains(position: number): { gainA: number; gainB: number } {
    const normalizedPos = (position + 1) / 2 // Convert -1,1 to 0,1
    const cutPoint = this.crossfaderData.cutPoint
    
    let gainA: number
    let gainB: number
    
    if (normalizedPos < cutPoint) {
      // A side active
      gainA = 1
      gainB = 0
    } else {
      // B side active
      gainA = 0
      gainB = 1
    }
    
    return { gainA, gainB }
  }

  /**
   * Check if we're in hamster direction (for hamster mode)
   */
  private isHamsterDirection(position: number): boolean {
    // Simple hamster mode implementation
    // In a real implementation, this would track direction changes
    return false
  }

  /**
   * Get crossfader position
   */
  public getPosition(): number {
    return this.crossfaderData.position
  }

  /**
   * Get crossfader curve
   */
  public getCurve(): CrossfaderCurve {
    return this.crossfaderData.curve
  }

  /**
   * Get crossfader mode
   */
  public getMode(): CrossfaderMode {
    return this.crossfaderData.mode
  }

  /**
   * Get current gain values for visualization
   */
  public getCurrentGains(): { gainA: number; gainB: number } {
    const position = this.crossfaderData.position
    
    switch (this.crossfaderData.curve) {
      case 'cut':
        return this.calculateCutGains(position)
      default:
        return calculateCrossfadeGains(position, this.crossfaderData.curve)
    }
  }

  /**
   * Snap to center position
   */
  public snapToCenter(): void {
    this.setPosition(0)
  }

  /**
   * Snap to full A position
   */
  public snapToA(): void {
    this.setPosition(-1)
  }

  /**
   * Snap to full B position
   */
  public snapToB(): void {
    this.setPosition(1)
  }

  /**
   * Get crossfader data
   */
  public getCrossfaderData(): CrossfaderAudioNodeData {
    return { ...this.crossfaderData }
  }

  /**
   * Perform quick cut to opposite side
   */
  public quickCut(): void {
    const currentPos = this.crossfaderData.position
    const targetPos = currentPos < 0 ? 1 : -1
    this.setPosition(targetPos)
  }

  /**
   * Start crossfade transition over time
   */
  public crossfadeToPosition(targetPosition: number, duration: number): void {
    if (!this.inputAGain || !this.inputBGain) return

    const startPosition = this.crossfaderData.position
    const startTime = this.getCurrentTime()
    const endTime = startTime + duration
    
    // Create smooth transition
    const steps = Math.max(10, Math.floor(duration * 60)) // 60 steps per second
    const stepDuration = duration / steps
    
    for (let i = 0; i <= steps; i++) {
      const progress = i / steps
      const currentPosition = startPosition + (targetPosition - startPosition) * progress
      const time = startTime + (i * stepDuration)
      
      const { gainA, gainB } = calculateCrossfadeGains(currentPosition, this.crossfaderData.curve)
      
      this.inputAGain.gain.linearRampToValueAtTime(gainA, time)
      this.inputBGain.gain.linearRampToValueAtTime(gainB, time)
    }
    
    // Update final position
    setTimeout(() => {
      this.crossfaderData.position = targetPosition
    }, duration * 1000)
  }

  /**
   * Get extended info
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()
    const currentGains = this.getCurrentGains()
    
    return {
      ...baseInfo,
      crossfaderData: this.crossfaderData,
      currentGains,
      inputAGain: this.inputAGain?.gain.value,
      inputBGain: this.inputBGain?.gain.value
    }
  }
}
