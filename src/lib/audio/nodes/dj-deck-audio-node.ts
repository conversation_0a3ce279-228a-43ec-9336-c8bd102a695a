/**
 * DJDeckAudioNode - Audio processing node for DJ deck functionality
 * Handles audio playback, tempo control, and basic DJ features
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'

export interface DJDeckAudioNodeData {
  track?: {
    url: string
    name: string
    duration: number
  }
  isPlaying: boolean
  volume: number
  tempo: number
  pitch: number
  cuePoints: number[]
  currentTime: number
}

export class DJDeckAudioNode extends BaseAudioNode {
  private audioBuffer: AudioBuffer | null = null
  private sourceNode: AudioBufferSourceNode | null = null
  private tempoNode: AudioWorkletNode | null = null
  private isLoaded = false
  private startTime = 0
  private pauseTime = 0
  private playbackRate = 1.0

  constructor(config: AudioNodeConfig & { data?: DJDeckAudioNodeData }) {
    const djDeckConfig: AudioNodeConfig = {
      ...config,
      inputs: 0, // DJ deck is a source
      outputs: 1,
      parameters: {
        volume: {
          name: 'volume',
          value: config.data?.volume || 0.8,
          min: 0,
          max: 1,
          defaultValue: 0.8,
          unit: 'linear',
          automatable: true
        },
        tempo: {
          name: 'tempo',
          value: config.data?.tempo || 1.0,
          min: 0.5,
          max: 2.0,
          defaultValue: 1.0,
          unit: 'ratio',
          automatable: true
        },
        pitch: {
          name: 'pitch',
          value: config.data?.pitch || 0,
          min: -12,
          max: 12,
          defaultValue: 0,
          unit: 'semitones',
          automatable: true
        }
      }
    }

    super(djDeckConfig)
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('AudioContext not available')
    }

    // Set up the audio chain: source -> gain -> output
    this.outputNodes[0] = this.gainNode
    
    console.log(`DJ Deck audio node ${this.id} created`)
  }

  /**
   * Initialize parameters
   */
  protected initializeParameters(): void {
    if (!this.gainNode) return

    // Map volume parameter to gain node
    this.parameterNodes.set('volume', this.gainNode.gain)
    
    // Set initial values
    const volumeParam = this.parameters.get('volume')
    if (volumeParam) {
      this.gainNode.gain.setValueAtTime(volumeParam.value, this.getCurrentTime())
    }
  }

  /**
   * Load audio track
   */
  public async loadTrack(audioBuffer: AudioBuffer): Promise<void> {
    this.audioBuffer = audioBuffer
    this.isLoaded = true
    
    console.log(`Track loaded in DJ Deck ${this.id}:`, {
      duration: audioBuffer.duration,
      sampleRate: audioBuffer.sampleRate,
      channels: audioBuffer.numberOfChannels
    })
  }

  /**
   * Load track from URL
   */
  public async loadTrackFromUrl(url: string): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    try {
      const response = await fetch(url)
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
      
      await this.loadTrack(audioBuffer)
    } catch (error) {
      console.error(`Failed to load track from URL ${url}:`, error)
      throw error
    }
  }

  /**
   * Start playback
   */
  public play(): void {
    if (!this.isLoaded || !this.audioBuffer || !this.audioContext || !this.gainNode) {
      console.warn(`Cannot play: DJ Deck ${this.id} not ready`)
      return
    }

    // Stop current playback if any
    this.stop()

    // Create new source node
    this.sourceNode = this.audioContext.createBufferSource()
    this.sourceNode.buffer = this.audioBuffer
    this.sourceNode.playbackRate.value = this.playbackRate
    
    // Connect to gain node
    this.sourceNode.connect(this.gainNode)
    
    // Calculate start offset
    const offset = this.pauseTime || 0
    
    // Start playback
    this.sourceNode.start(0, offset)
    this.startTime = this.audioContext.currentTime - offset
    
    console.log(`DJ Deck ${this.id} started playing from ${offset}s`)
  }

  /**
   * Pause playback
   */
  public pause(): void {
    if (this.sourceNode && this.audioContext) {
      this.pauseTime = this.audioContext.currentTime - this.startTime
      this.sourceNode.stop()
      this.sourceNode = null
      
      console.log(`DJ Deck ${this.id} paused at ${this.pauseTime}s`)
    }
  }

  /**
   * Stop playback
   */
  public stop(): void {
    if (this.sourceNode) {
      this.sourceNode.stop()
      this.sourceNode = null
    }
    
    this.startTime = 0
    this.pauseTime = 0
    
    console.log(`DJ Deck ${this.id} stopped`)
  }

  /**
   * Seek to position
   */
  public seek(time: number): void {
    const wasPlaying = this.sourceNode !== null
    
    this.stop()
    this.pauseTime = Math.max(0, Math.min(time, this.getDuration()))
    
    if (wasPlaying) {
      this.play()
    }
    
    console.log(`DJ Deck ${this.id} seeked to ${this.pauseTime}s`)
  }

  /**
   * Set tempo (playback rate)
   */
  public setTempo(tempo: number): void {
    this.playbackRate = Math.max(0.5, Math.min(2.0, tempo))
    
    if (this.sourceNode) {
      this.sourceNode.playbackRate.setValueAtTime(
        this.playbackRate,
        this.getCurrentTime()
      )
    }
    
    // Update parameter
    this.setParameter('tempo', this.playbackRate)
    
    console.log(`DJ Deck ${this.id} tempo set to ${this.playbackRate}`)
  }

  /**
   * Get current playback position
   */
  public getCurrentTime(): number {
    if (!this.audioContext) return 0
    
    if (this.sourceNode) {
      return (this.audioContext.currentTime - this.startTime) * this.playbackRate
    }
    
    return this.pauseTime
  }

  /**
   * Get track duration
   */
  public getDuration(): number {
    return this.audioBuffer?.duration || 0
  }

  /**
   * Check if track is playing
   */
  public isPlaying(): boolean {
    return this.sourceNode !== null
  }

  /**
   * Check if track is loaded
   */
  public isTrackLoaded(): boolean {
    return this.isLoaded
  }

  /**
   * Get waveform data for visualization
   */
  public getWaveformData(samples: number = 1000): Float32Array | null {
    if (!this.audioBuffer) return null
    
    const channelData = this.audioBuffer.getChannelData(0)
    const blockSize = Math.floor(channelData.length / samples)
    const waveformData = new Float32Array(samples)
    
    for (let i = 0; i < samples; i++) {
      const start = i * blockSize
      const end = Math.min(start + blockSize, channelData.length)
      
      let max = 0
      for (let j = start; j < end; j++) {
        max = Math.max(max, Math.abs(channelData[j]))
      }
      
      waveformData[i] = max
    }
    
    return waveformData
  }

  /**
   * Add cue point
   */
  public addCuePoint(time: number): void {
    // This would be implemented with more sophisticated cue point management
    console.log(`Cue point added at ${time}s in DJ Deck ${this.id}`)
  }

  /**
   * Jump to cue point
   */
  public jumpToCue(cueIndex: number): void {
    // This would be implemented with cue point management
    console.log(`Jumping to cue ${cueIndex} in DJ Deck ${this.id}`)
  }

  /**
   * Override dispose to clean up audio resources
   */
  public dispose(): void {
    this.stop()
    this.audioBuffer = null
    this.isLoaded = false
    super.dispose()
  }

  /**
   * Get extended info including track details
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()
    
    return {
      ...baseInfo,
      isLoaded: this.isLoaded,
      isPlaying: this.isPlaying(),
      currentTime: this.getCurrentTime(),
      duration: this.getDuration(),
      playbackRate: this.playbackRate,
      track: this.audioBuffer ? {
        duration: this.audioBuffer.duration,
        sampleRate: this.audioBuffer.sampleRate,
        channels: this.audioBuffer.numberOfChannels
      } : null
    }
  }
}
