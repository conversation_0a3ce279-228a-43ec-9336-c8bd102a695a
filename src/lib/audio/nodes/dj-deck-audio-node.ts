/**
 * DJDeckAudioNode - Audio processing node for DJ deck functionality
 * Handles audio playback, tempo control, and basic DJ features
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'
import { TrackLoader, LoadedTrack, LoadProgressCallback } from '../track-loader'
import { BeatDetector, BeatInfo } from '../beat-detector'

export interface CuePoint {
  id: string
  time: number
  label?: string
  color?: string
}

export interface LoopRegion {
  id: string
  start: number
  end: number
  enabled: boolean
}

export interface DJDeckAudioNodeData {
  track?: LoadedTrack
  isPlaying: boolean
  volume: number
  tempo: number
  pitch: number
  cuePoints: CuePoint[]
  currentTime: number
  loopRegion?: LoopRegion
  beatInfo?: BeatInfo
  isLooping: boolean
  isSyncing: boolean
}

export class DJDeckAudioNode extends BaseAudioNode {
  private loadedTrack: LoadedTrack | null = null
  private sourceNode: AudioBufferSourceNode | null = null
  private tempoNode: AudioWorkletNode | null = null
  private trackLoader: TrackLoader
  private beatDetector: BeatDetector
  private isLoaded = false
  private startTime = 0
  private pauseTime = 0
  private playbackRate = 1.0
  private pitchShift = 0
  private cuePoints: CuePoint[] = []
  private loopRegion: LoopRegion | null = null
  private isLooping = false
  private beatInfo: BeatInfo | null = null

  constructor(config: AudioNodeConfig & { data?: DJDeckAudioNodeData }) {
    const djDeckConfig: AudioNodeConfig = {
      ...config,
      inputs: 0, // DJ deck is a source
      outputs: 1,
      parameters: {
        volume: {
          name: 'volume',
          value: config.data?.volume || 0.8,
          min: 0,
          max: 1,
          defaultValue: 0.8,
          unit: 'linear',
          automatable: true
        },
        tempo: {
          name: 'tempo',
          value: config.data?.tempo || 1.0,
          min: 0.5,
          max: 2.0,
          defaultValue: 1.0,
          unit: 'ratio',
          automatable: true
        },
        pitch: {
          name: 'pitch',
          value: config.data?.pitch || 0,
          min: -12,
          max: 12,
          defaultValue: 0,
          unit: 'semitones',
          automatable: true
        }
      }
    }

    super(djDeckConfig)

    // Initialize track loader and beat detector
    this.trackLoader = new TrackLoader(this.audioContext!)
    this.beatDetector = new BeatDetector(this.audioContext!)
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('AudioContext not available')
    }

    // Set up the audio chain: source -> gain -> output
    this.outputNodes[0] = this.gainNode
    
    console.log(`DJ Deck audio node ${this.id} created`)
  }

  /**
   * Initialize parameters
   */
  protected initializeParameters(): void {
    if (!this.gainNode) return

    // Map volume parameter to gain node
    this.parameterNodes.set('volume', this.gainNode.gain)
    
    // Set initial values
    const volumeParam = this.parameters.get('volume')
    if (volumeParam) {
      this.gainNode.gain.setValueAtTime(volumeParam.value, this.getCurrentTime())
    }
  }

  /**
   * Load track from file with progress tracking
   */
  public async loadTrackFromFile(
    file: File,
    onProgress?: LoadProgressCallback
  ): Promise<LoadedTrack> {
    try {
      const track = await this.trackLoader.loadFromFile(file, onProgress)
      await this.setLoadedTrack(track)
      return track
    } catch (error) {
      console.error(`Failed to load track from file:`, error)
      throw error
    }
  }

  /**
   * Load track from URL with progress tracking
   */
  public async loadTrackFromUrl(
    url: string,
    name?: string,
    onProgress?: LoadProgressCallback
  ): Promise<LoadedTrack> {
    try {
      const track = await this.trackLoader.loadFromUrl(url, name, onProgress)
      await this.setLoadedTrack(track)
      return track
    } catch (error) {
      console.error(`Failed to load track from URL:`, error)
      throw error
    }
  }

  /**
   * Set loaded track and analyze beats
   */
  private async setLoadedTrack(track: LoadedTrack): Promise<void> {
    this.loadedTrack = track
    this.isLoaded = true

    // Analyze beats
    try {
      this.beatInfo = await this.beatDetector.analyzeAudioBuffer(track.audioBuffer)
      console.log(`Beat analysis complete for ${track.name}:`, {
        bpm: this.beatInfo.bpm,
        confidence: this.beatInfo.confidence,
        beats: this.beatInfo.beats.length
      })
    } catch (error) {
      console.warn(`Beat analysis failed for ${track.name}:`, error)
      this.beatInfo = null
    }

    console.log(`Track loaded in DJ Deck ${this.id}:`, {
      name: track.name,
      duration: track.audioBuffer.duration,
      sampleRate: track.audioBuffer.sampleRate,
      channels: track.audioBuffer.numberOfChannels,
      bpm: this.beatInfo?.bpm
    })
  }

  /**
   * Start playback
   */
  public play(): void {
    if (!this.isLoaded || !this.loadedTrack || !this.audioContext || !this.gainNode) {
      console.warn(`Cannot play: DJ Deck ${this.id} not ready`)
      return
    }

    // Stop current playback if any
    this.stop()

    // Create new source node
    this.sourceNode = this.audioContext.createBufferSource()
    this.sourceNode.buffer = this.loadedTrack.audioBuffer
    this.sourceNode.playbackRate.value = this.playbackRate

    // Set up loop if enabled
    if (this.isLooping && this.loopRegion) {
      this.sourceNode.loop = true
      this.sourceNode.loopStart = this.loopRegion.start
      this.sourceNode.loopEnd = this.loopRegion.end
    }

    // Connect to gain node
    this.sourceNode.connect(this.gainNode)

    // Calculate start offset
    const offset = this.pauseTime || 0

    // Start playback
    this.sourceNode.start(0, offset)
    this.startTime = this.audioContext.currentTime - offset

    // Start real-time beat detection if available
    if (this.beatDetector && this.sourceNode) {
      this.beatDetector.startRealTimeDetection(this.sourceNode)
    }

    console.log(`DJ Deck ${this.id} started playing from ${offset}s`)
  }

  /**
   * Pause playback
   */
  public pause(): void {
    if (this.sourceNode && this.audioContext) {
      this.pauseTime = this.audioContext.currentTime - this.startTime
      this.sourceNode.stop()
      this.sourceNode = null
      
      console.log(`DJ Deck ${this.id} paused at ${this.pauseTime}s`)
    }
  }

  /**
   * Stop playback
   */
  public stop(): void {
    if (this.sourceNode) {
      this.sourceNode.stop()
      this.sourceNode = null
    }
    
    this.startTime = 0
    this.pauseTime = 0
    
    console.log(`DJ Deck ${this.id} stopped`)
  }

  /**
   * Seek to position
   */
  public seek(time: number): void {
    const wasPlaying = this.sourceNode !== null
    
    this.stop()
    this.pauseTime = Math.max(0, Math.min(time, this.getDuration()))
    
    if (wasPlaying) {
      this.play()
    }
    
    console.log(`DJ Deck ${this.id} seeked to ${this.pauseTime}s`)
  }

  /**
   * Set tempo (playback rate)
   */
  public setTempo(tempo: number): void {
    this.playbackRate = Math.max(0.5, Math.min(2.0, tempo))
    
    if (this.sourceNode) {
      this.sourceNode.playbackRate.setValueAtTime(
        this.playbackRate,
        this.getCurrentTime()
      )
    }
    
    // Update parameter
    this.setParameter('tempo', this.playbackRate)
    
    console.log(`DJ Deck ${this.id} tempo set to ${this.playbackRate}`)
  }

  /**
   * Get current playback position
   */
  public getCurrentTime(): number {
    if (!this.audioContext) return 0
    
    if (this.sourceNode) {
      return (this.audioContext.currentTime - this.startTime) * this.playbackRate
    }
    
    return this.pauseTime
  }

  /**
   * Get track duration
   */
  public getDuration(): number {
    return this.loadedTrack?.audioBuffer.duration || 0
  }

  /**
   * Check if track is playing
   */
  public isPlaying(): boolean {
    return this.sourceNode !== null
  }

  /**
   * Check if track is loaded
   */
  public isTrackLoaded(): boolean {
    return this.isLoaded
  }

  /**
   * Get waveform data for visualization
   */
  public getWaveformData(samples: number = 1000): Float32Array | null {
    return this.loadedTrack?.waveformData || null
  }

  /**
   * Get peak data for detailed waveform
   */
  public getPeakData(): Float32Array | null {
    return this.loadedTrack?.peakData || null
  }

  /**
   * Add cue point
   */
  public addCuePoint(time: number, label?: string, color?: string): CuePoint {
    const cuePoint: CuePoint = {
      id: `cue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      time: Math.max(0, Math.min(time, this.getDuration())),
      label,
      color: color || '#f59e0b'
    }

    this.cuePoints.push(cuePoint)
    this.cuePoints.sort((a, b) => a.time - b.time)

    console.log(`Cue point added at ${cuePoint.time}s in DJ Deck ${this.id}`)
    return cuePoint
  }

  /**
   * Remove cue point
   */
  public removeCuePoint(cueId: string): void {
    const index = this.cuePoints.findIndex(cue => cue.id === cueId)
    if (index >= 0) {
      this.cuePoints.splice(index, 1)
      console.log(`Cue point ${cueId} removed from DJ Deck ${this.id}`)
    }
  }

  /**
   * Jump to cue point
   */
  public jumpToCue(cueId: string): void {
    const cuePoint = this.cuePoints.find(cue => cue.id === cueId)
    if (cuePoint) {
      this.seek(cuePoint.time)
      console.log(`Jumped to cue ${cuePoint.label || cueId} at ${cuePoint.time}s`)
    }
  }

  /**
   * Get all cue points
   */
  public getCuePoints(): CuePoint[] {
    return [...this.cuePoints]
  }

  /**
   * Set loop region
   */
  public setLoopRegion(start: number, end: number): void {
    const duration = this.getDuration()
    this.loopRegion = {
      id: `loop_${Date.now()}`,
      start: Math.max(0, Math.min(start, duration)),
      end: Math.max(start, Math.min(end, duration)),
      enabled: true
    }

    console.log(`Loop region set: ${this.loopRegion.start}s - ${this.loopRegion.end}s`)
  }

  /**
   * Enable/disable looping
   */
  public setLooping(enabled: boolean): void {
    this.isLooping = enabled

    // Update current source node if playing
    if (this.sourceNode && this.loopRegion) {
      this.sourceNode.loop = enabled
      if (enabled) {
        this.sourceNode.loopStart = this.loopRegion.start
        this.sourceNode.loopEnd = this.loopRegion.end
      }
    }

    console.log(`Looping ${enabled ? 'enabled' : 'disabled'} in DJ Deck ${this.id}`)
  }

  /**
   * Clear loop region
   */
  public clearLoop(): void {
    this.loopRegion = null
    this.isLooping = false

    if (this.sourceNode) {
      this.sourceNode.loop = false
    }

    console.log(`Loop cleared in DJ Deck ${this.id}`)
  }

  /**
   * Sync to target BPM
   */
  public async syncToBPM(targetBpm: number): Promise<void> {
    if (!this.beatInfo) {
      console.warn(`Cannot sync: no beat info available for DJ Deck ${this.id}`)
      return
    }

    const currentBpm = this.beatInfo.bpm * this.playbackRate
    const tempoRatio = targetBpm / this.beatInfo.bpm

    this.setTempo(tempoRatio)

    console.log(`Synced DJ Deck ${this.id} from ${currentBpm.toFixed(1)} to ${targetBpm} BPM`)
  }

  /**
   * Get current BPM
   */
  public getCurrentBPM(): number {
    if (!this.beatInfo) return 0
    return this.beatInfo.bpm * this.playbackRate
  }

  /**
   * Get beat info
   */
  public getBeatInfo(): BeatInfo | null {
    return this.beatInfo
  }

  /**
   * Get real-time BPM from beat detector
   */
  public getRealTimeBPM(): number {
    return this.beatDetector.getCurrentBPM()
  }

  /**
   * Set pitch (in semitones)
   */
  public setPitch(pitch: number): void {
    this.pitchShift = Math.max(-12, Math.min(12, pitch))

    // Convert semitones to playback rate adjustment
    const pitchRatio = Math.pow(2, this.pitchShift / 12)
    const finalPlaybackRate = this.playbackRate * pitchRatio

    if (this.sourceNode) {
      this.sourceNode.playbackRate.setValueAtTime(
        finalPlaybackRate,
        this.getCurrentTime()
      )
    }

    // Update parameter
    this.setParameter('pitch', this.pitchShift)

    console.log(`DJ Deck ${this.id} pitch set to ${this.pitchShift} semitones`)
  }

  /**
   * Apply pitch bend
   */
  public pitchBend(direction: 'up' | 'down', amount: number = 0.1): void {
    const bendAmount = direction === 'up' ? amount : -amount
    const newPitch = this.pitchShift + bendAmount
    this.setPitch(newPitch)
  }

  /**
   * Override dispose to clean up audio resources
   */
  public dispose(): void {
    this.stop()
    this.beatDetector.stopRealTimeDetection()
    this.trackLoader.clearAll()
    this.loadedTrack = null
    this.beatInfo = null
    this.cuePoints = []
    this.loopRegion = null
    this.isLoaded = false
    super.dispose()
  }

  /**
   * Get extended info including track details
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()

    return {
      ...baseInfo,
      isLoaded: this.isLoaded,
      isPlaying: this.isPlaying(),
      currentTime: this.getCurrentTime(),
      duration: this.getDuration(),
      playbackRate: this.playbackRate,
      pitchShift: this.pitchShift,
      isLooping: this.isLooping,
      track: this.loadedTrack ? {
        id: this.loadedTrack.id,
        name: this.loadedTrack.name,
        duration: this.loadedTrack.audioBuffer.duration,
        sampleRate: this.loadedTrack.audioBuffer.sampleRate,
        channels: this.loadedTrack.audioBuffer.numberOfChannels,
        metadata: this.loadedTrack.metadata
      } : null,
      beatInfo: this.beatInfo,
      cuePoints: this.cuePoints,
      loopRegion: this.loopRegion,
      realTimeBPM: this.getRealTimeBPM()
    }
  }
}
