/**
 * EffectAudioNode - Professional audio effects processor
 * Supports reverb, delay, filter, distortion, and other effects
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'

export type EffectType = 'reverb' | 'delay' | 'filter' | 'distortion' | 'chorus' | 'flanger' | 'phaser'

export interface EffectAudioNodeData {
  effectType: EffectType
  enabled: boolean
  wetness: number // 0-1, dry/wet mix
  parameters: Record<string, number>
}

export class EffectAudioNode extends BaseAudioNode {
  private inputGainNode: GainNode | null = null
  private dryGainNode: GainNode | null = null
  private wetGainNode: GainNode | null = null
  private outputGainNode: GainNode | null = null
  
  // Effect-specific nodes
  private effectNodes: AudioNode[] = []
  private convolver: ConvolverNode | null = null
  private delayNode: DelayNode | null = null
  private filterNode: BiquadFilterNode | null = null
  private waveShaperNode: WaveShaperNode | null = null
  
  private effectData: EffectAudioNodeData

  constructor(config: AudioNodeConfig & { data?: EffectAudioNodeData }) {
    const effectConfig: AudioNodeConfig = {
      ...config,
      inputs: 1,
      outputs: 1,
      parameters: {
        wetness: {
          name: 'wetness',
          value: config.data?.wetness || 0.3,
          min: 0,
          max: 1,
          defaultValue: 0.3,
          unit: 'ratio',
          automatable: true
        }
      }
    }

    super(effectConfig)
    
    this.effectData = {
      effectType: config.data?.effectType || 'reverb',
      enabled: config.data?.enabled !== false,
      wetness: config.data?.wetness || 0.3,
      parameters: config.data?.parameters || {}
    }
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // Create basic routing nodes
    this.inputGainNode = this.audioContext.createGain()
    this.dryGainNode = this.audioContext.createGain()
    this.wetGainNode = this.audioContext.createGain()
    this.outputGainNode = this.audioContext.createGain()
    
    // Create effect-specific processing chain
    await this.createEffectChain()
    
    // Set up routing
    this.setupRouting()
    
    // Set input/output nodes
    this.inputNodes[0] = this.inputGainNode
    this.outputNodes[0] = this.outputGainNode
    
    console.log(`Effect ${this.id} (${this.effectData.effectType}) created`)
  }

  /**
   * Create effect-specific processing chain
   */
  private async createEffectChain(): Promise<void> {
    if (!this.audioContext) return

    switch (this.effectData.effectType) {
      case 'reverb':
        await this.createReverbEffect()
        break
      case 'delay':
        this.createDelayEffect()
        break
      case 'filter':
        this.createFilterEffect()
        break
      case 'distortion':
        this.createDistortionEffect()
        break
      case 'chorus':
        this.createChorusEffect()
        break
      case 'flanger':
        this.createFlangerEffect()
        break
      case 'phaser':
        this.createPhaserEffect()
        break
    }
  }

  /**
   * Create reverb effect using convolution
   */
  private async createReverbEffect(): Promise<void> {
    if (!this.audioContext) return

    this.convolver = this.audioContext.createConvolver()
    
    // Create impulse response for reverb
    const impulseResponse = this.createReverbImpulse(
      this.effectData.parameters.roomSize || 0.5,
      this.effectData.parameters.decay || 0.7,
      this.effectData.parameters.damping || 0.3
    )
    
    this.convolver.buffer = impulseResponse
    this.effectNodes = [this.convolver]
  }

  /**
   * Create delay effect
   */
  private createDelayEffect(): void {
    if (!this.audioContext) return

    this.delayNode = this.audioContext.createDelay(1.0) // Max 1 second delay
    this.delayNode.delayTime.value = this.effectData.parameters.time || 0.25
    
    // Add feedback
    const feedbackGain = this.audioContext.createGain()
    feedbackGain.gain.value = this.effectData.parameters.feedback || 0.3
    
    this.delayNode.connect(feedbackGain)
    feedbackGain.connect(this.delayNode)
    
    this.effectNodes = [this.delayNode, feedbackGain]
  }

  /**
   * Create filter effect
   */
  private createFilterEffect(): void {
    if (!this.audioContext) return

    this.filterNode = this.audioContext.createBiquadFilter()
    this.filterNode.type = (this.effectData.parameters.type as BiquadFilterType) || 'lowpass'
    this.filterNode.frequency.value = this.effectData.parameters.frequency || 1000
    this.filterNode.Q.value = this.effectData.parameters.resonance || 1
    
    this.effectNodes = [this.filterNode]
  }

  /**
   * Create distortion effect
   */
  private createDistortionEffect(): void {
    if (!this.audioContext) return

    this.waveShaperNode = this.audioContext.createWaveShaper()
    
    const amount = this.effectData.parameters.amount || 50
    const samples = 44100
    const curve = new Float32Array(samples)
    const deg = Math.PI / 180
    
    for (let i = 0; i < samples; i++) {
      const x = (i * 2) / samples - 1
      curve[i] = ((3 + amount) * x * 20 * deg) / (Math.PI + amount * Math.abs(x))
    }
    
    this.waveShaperNode.curve = curve
    this.waveShaperNode.oversample = '4x'
    
    this.effectNodes = [this.waveShaperNode]
  }

  /**
   * Create chorus effect
   */
  private createChorusEffect(): void {
    if (!this.audioContext) return

    const delayTime = this.effectData.parameters.delayTime || 0.02
    const depth = this.effectData.parameters.depth || 0.005
    const rate = this.effectData.parameters.rate || 1.5

    const delay = this.audioContext.createDelay(0.1)
    const lfo = this.audioContext.createOscillator()
    const lfoGain = this.audioContext.createGain()

    delay.delayTime.value = delayTime
    lfo.frequency.value = rate
    lfoGain.gain.value = depth

    lfo.connect(lfoGain)
    lfoGain.connect(delay.delayTime)
    lfo.start()

    this.effectNodes = [delay, lfo, lfoGain]
  }

  /**
   * Create flanger effect
   */
  private createFlangerEffect(): void {
    if (!this.audioContext) return

    const delayTime = this.effectData.parameters.delayTime || 0.005
    const depth = this.effectData.parameters.depth || 0.002
    const rate = this.effectData.parameters.rate || 0.25
    const feedback = this.effectData.parameters.feedback || 0.5

    const delay = this.audioContext.createDelay(0.02)
    const lfo = this.audioContext.createOscillator()
    const lfoGain = this.audioContext.createGain()
    const feedbackGain = this.audioContext.createGain()

    delay.delayTime.value = delayTime
    lfo.frequency.value = rate
    lfoGain.gain.value = depth
    feedbackGain.gain.value = feedback

    lfo.connect(lfoGain)
    lfoGain.connect(delay.delayTime)
    delay.connect(feedbackGain)
    feedbackGain.connect(delay)
    lfo.start()

    this.effectNodes = [delay, lfo, lfoGain, feedbackGain]
  }

  /**
   * Create phaser effect
   */
  private createPhaserEffect(): void {
    if (!this.audioContext) return

    const stages = this.effectData.parameters.stages || 4
    const rate = this.effectData.parameters.rate || 0.5
    const depth = this.effectData.parameters.depth || 1000
    const feedback = this.effectData.parameters.feedback || 0.3

    const allpassFilters: BiquadFilterNode[] = []
    const lfo = this.audioContext.createOscillator()
    const lfoGain = this.audioContext.createGain()
    const feedbackGain = this.audioContext.createGain()

    lfo.frequency.value = rate
    lfoGain.gain.value = depth
    feedbackGain.gain.value = feedback

    // Create allpass filter chain
    for (let i = 0; i < stages; i++) {
      const filter = this.audioContext.createBiquadFilter()
      filter.type = 'allpass'
      filter.frequency.value = 1000 + (i * 200)
      allpassFilters.push(filter)
      
      lfo.connect(lfoGain)
      lfoGain.connect(filter.frequency)
    }

    // Connect filters in series
    for (let i = 0; i < allpassFilters.length - 1; i++) {
      allpassFilters[i].connect(allpassFilters[i + 1])
    }

    // Add feedback
    if (allpassFilters.length > 0) {
      allpassFilters[allpassFilters.length - 1].connect(feedbackGain)
      feedbackGain.connect(allpassFilters[0])
    }

    lfo.start()
    this.effectNodes = [lfo, lfoGain, feedbackGain, ...allpassFilters]
  }

  /**
   * Set up audio routing
   */
  private setupRouting(): void {
    if (!this.inputGainNode || !this.dryGainNode || !this.wetGainNode || !this.outputGainNode) return

    // Dry path: input -> dry gain -> output
    this.inputGainNode.connect(this.dryGainNode)
    this.dryGainNode.connect(this.outputGainNode)

    // Wet path: input -> effect chain -> wet gain -> output
    if (this.effectNodes.length > 0) {
      this.inputGainNode.connect(this.effectNodes[0])
      
      // Connect effect chain
      for (let i = 0; i < this.effectNodes.length - 1; i++) {
        if (this.effectNodes[i] && this.effectNodes[i + 1]) {
          this.effectNodes[i].connect(this.effectNodes[i + 1])
        }
      }
      
      // Connect last effect to wet gain
      const lastEffect = this.effectNodes[this.effectNodes.length - 1]
      if (lastEffect) {
        lastEffect.connect(this.wetGainNode)
      }
    }

    this.wetGainNode.connect(this.outputGainNode)
    
    // Update wet/dry mix
    this.updateWetDryMix()
  }

  /**
   * Initialize parameter mappings
   */
  protected initializeParameters(): void {
    // Wetness parameter is handled manually
    this.updateWetDryMix()
  }

  /**
   * Update wet/dry mix based on wetness parameter
   */
  private updateWetDryMix(): void {
    if (!this.dryGainNode || !this.wetGainNode) return

    const currentTime = this.getCurrentTime()
    const wetness = this.effectData.wetness
    const dryness = 1 - wetness

    this.dryGainNode.gain.setValueAtTime(dryness, currentTime)
    this.wetGainNode.gain.setValueAtTime(wetness, currentTime)
  }

  /**
   * Set effect wetness (dry/wet mix)
   */
  public setWetness(wetness: number): void {
    this.effectData.wetness = Math.max(0, Math.min(1, wetness))
    this.setParameter('wetness', this.effectData.wetness)
    this.updateWetDryMix()
  }

  /**
   * Set effect parameter
   */
  public setEffectParameter(name: string, value: number): void {
    this.effectData.parameters[name] = value
    this.updateEffectParameter(name, value)
  }

  /**
   * Update specific effect parameter
   */
  private updateEffectParameter(name: string, value: number): void {
    const currentTime = this.getCurrentTime()

    switch (this.effectData.effectType) {
      case 'delay':
        if (name === 'time' && this.delayNode) {
          this.delayNode.delayTime.setValueAtTime(value, currentTime)
        }
        break
        
      case 'filter':
        if (name === 'frequency' && this.filterNode) {
          this.filterNode.frequency.setValueAtTime(value, currentTime)
        } else if (name === 'resonance' && this.filterNode) {
          this.filterNode.Q.setValueAtTime(value, currentTime)
        }
        break
    }
  }

  /**
   * Create reverb impulse response
   */
  private createReverbImpulse(roomSize: number, decay: number, damping: number): AudioBuffer {
    if (!this.audioContext) throw new Error('AudioContext not available')

    const sampleRate = this.audioContext.sampleRate
    const length = Math.floor(sampleRate * roomSize * 4) // Up to 4 seconds
    const impulse = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel)
      
      for (let i = 0; i < length; i++) {
        const n = length - i
        const envelope = Math.pow(n / length, decay * 10)
        const noise = (Math.random() * 2 - 1) * envelope
        
        // Apply damping (high frequency rolloff)
        const dampingFactor = 1 - (damping * (i / length))
        channelData[i] = noise * dampingFactor
      }
    }

    return impulse
  }

  /**
   * Get effect data
   */
  public getEffectData(): EffectAudioNodeData {
    return { ...this.effectData }
  }

  /**
   * Get extended info
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()
    
    return {
      ...baseInfo,
      effectData: this.effectData,
      effectNodes: this.effectNodes.length,
      wetGain: this.wetGainNode?.gain.value,
      dryGain: this.dryGainNode?.gain.value
    }
  }
}
