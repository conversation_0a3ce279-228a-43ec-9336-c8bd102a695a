/**
 * MasterOutputAudioNode - Final mixing stage with level meters and recording
 * Provides master volume, cue monitoring, recording, and output level analysis
 */

import { BaseAudioNode, AudioNodeConfig, AudioParameter } from '../base-audio-node'

export interface MasterOutputAudioNodeData {
  masterVolume: number
  cueVolume: number
  isRecording: boolean
  isLive: boolean
  limiterEnabled: boolean
  limiterThreshold: number
  monitoringEnabled: boolean
}

export interface LevelMeterData {
  peak: number
  rms: number
  clip: boolean
}

export class MasterOutputAudioNode extends BaseAudioNode {
  private masterGainNode: GainNode | null = null
  private cueGainNode: GainNode | null = null
  private limiterNode: DynamicsCompressorNode | null = null
  private analyserNode: AnalyserNode | null = null
  private cueAnalyserNode: AnalyserNode | null = null
  
  // Recording
  private mediaRecorder: MediaRecorder | null = null
  private recordingStream: MediaStream | null = null
  private recordingDestination: MediaStreamAudioDestinationNode | null = null
  private recordedChunks: Blob[] = []
  
  // Level monitoring
  private levelMeterData: { main: LevelMeterData; cue: LevelMeterData } = {
    main: { peak: 0, rms: 0, clip: false },
    cue: { peak: 0, rms: 0, clip: false }
  }
  private levelUpdateInterval: NodeJS.Timeout | null = null
  
  private masterData: MasterOutputAudioNodeData

  constructor(config: AudioNodeConfig & { data?: MasterOutputAudioNodeData }) {
    const masterConfig: AudioNodeConfig = {
      ...config,
      inputs: 2, // Main input and cue input
      outputs: 1, // Master output
      parameters: {
        masterVolume: {
          name: 'masterVolume',
          value: config.data?.masterVolume || 0.8,
          min: 0,
          max: 1,
          defaultValue: 0.8,
          unit: 'linear',
          automatable: true
        },
        cueVolume: {
          name: 'cueVolume',
          value: config.data?.cueVolume || 0.5,
          min: 0,
          max: 1,
          defaultValue: 0.5,
          unit: 'linear',
          automatable: true
        },
        limiterThreshold: {
          name: 'limiterThreshold',
          value: config.data?.limiterThreshold || -3,
          min: -20,
          max: 0,
          defaultValue: -3,
          unit: 'dB',
          automatable: true
        }
      }
    }

    super(masterConfig)
    
    this.masterData = {
      masterVolume: config.data?.masterVolume || 0.8,
      cueVolume: config.data?.cueVolume || 0.5,
      isRecording: config.data?.isRecording || false,
      isLive: config.data?.isLive || false,
      limiterEnabled: config.data?.limiterEnabled || true,
      limiterThreshold: config.data?.limiterThreshold || -3,
      monitoringEnabled: config.data?.monitoringEnabled || true
    }
  }

  /**
   * Create audio processing nodes
   */
  protected async createAudioNodes(): Promise<void> {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // Create master gain control
    this.masterGainNode = this.audioContext.createGain()
    this.masterGainNode.gain.value = this.masterData.masterVolume
    
    // Create cue gain control
    this.cueGainNode = this.audioContext.createGain()
    this.cueGainNode.gain.value = this.masterData.cueVolume
    
    // Create limiter (compressor with high ratio)
    this.limiterNode = this.audioContext.createDynamicsCompressor()
    this.limiterNode.threshold.value = this.masterData.limiterThreshold
    this.limiterNode.knee.value = 0
    this.limiterNode.ratio.value = 20
    this.limiterNode.attack.value = 0.003
    this.limiterNode.release.value = 0.01
    
    // Create analysers for level monitoring
    this.analyserNode = this.audioContext.createAnalyser()
    this.analyserNode.fftSize = 256
    this.analyserNode.smoothingTimeConstant = 0.3
    
    this.cueAnalyserNode = this.audioContext.createAnalyser()
    this.cueAnalyserNode.fftSize = 256
    this.cueAnalyserNode.smoothingTimeConstant = 0.3
    
    // Set up audio routing
    this.setupAudioRouting()
    
    // Set input/output nodes
    this.inputNodes[0] = this.masterGainNode // Main input
    this.inputNodes[1] = this.cueGainNode // Cue input
    this.outputNodes[0] = this.limiterNode // Master output
    
    // Start level monitoring
    if (this.masterData.monitoringEnabled) {
      this.startLevelMonitoring()
    }
    
    console.log(`Master Output ${this.id} created`)
  }

  /**
   * Set up audio routing chain
   */
  private setupAudioRouting(): void {
    if (!this.masterGainNode || !this.limiterNode || !this.analyserNode) return

    // Main signal chain
    if (this.masterData.limiterEnabled) {
      this.masterGainNode.connect(this.limiterNode)
      this.limiterNode.connect(this.analyserNode)
    } else {
      this.masterGainNode.connect(this.analyserNode)
    }
    
    // Connect to audio context destination (speakers)
    this.analyserNode.connect(this.audioContext!.destination)
    
    // Cue monitoring chain
    if (this.cueGainNode && this.cueAnalyserNode) {
      this.cueGainNode.connect(this.cueAnalyserNode)
      // Cue output would typically go to headphones (separate audio device)
    }
  }

  /**
   * Initialize parameter mappings
   */
  protected initializeParameters(): void {
    if (!this.masterGainNode || !this.cueGainNode || !this.limiterNode) return

    this.parameterNodes.set('masterVolume', this.masterGainNode.gain)
    this.parameterNodes.set('cueVolume', this.cueGainNode.gain)
    this.parameterNodes.set('limiterThreshold', this.limiterNode.threshold)
  }

  /**
   * Set master volume
   */
  public setMasterVolume(volume: number): void {
    this.masterData.masterVolume = Math.max(0, Math.min(1, volume))
    this.setParameter('masterVolume', this.masterData.masterVolume)
  }

  /**
   * Set cue volume
   */
  public setCueVolume(volume: number): void {
    this.masterData.cueVolume = Math.max(0, Math.min(1, volume))
    this.setParameter('cueVolume', this.masterData.cueVolume)
  }

  /**
   * Enable/disable limiter
   */
  public setLimiterEnabled(enabled: boolean): void {
    this.masterData.limiterEnabled = enabled
    this.setupAudioRouting() // Reconnect audio chain
  }

  /**
   * Set limiter threshold
   */
  public setLimiterThreshold(thresholdDb: number): void {
    this.masterData.limiterThreshold = Math.max(-20, Math.min(0, thresholdDb))
    this.setParameter('limiterThreshold', this.masterData.limiterThreshold)
  }

  /**
   * Start recording
   */
  public async startRecording(): Promise<void> {
    if (!this.audioContext || this.masterData.isRecording) return

    try {
      // Create recording destination
      this.recordingDestination = this.audioContext.createMediaStreamDestination()
      
      // Connect master output to recording destination
      if (this.analyserNode) {
        this.analyserNode.connect(this.recordingDestination)
      }
      
      this.recordingStream = this.recordingDestination.stream
      
      // Create MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.recordingStream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      this.recordedChunks = []
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data)
        }
      }
      
      this.mediaRecorder.start(1000) // Record in 1-second chunks
      this.masterData.isRecording = true
      
      console.log(`Recording started on Master Output ${this.id}`)
    } catch (error) {
      console.error('Failed to start recording:', error)
      throw error
    }
  }

  /**
   * Stop recording and return recorded audio
   */
  public async stopRecording(): Promise<Blob | null> {
    if (!this.mediaRecorder || !this.masterData.isRecording) return null

    return new Promise((resolve) => {
      this.mediaRecorder!.onstop = () => {
        const recordedBlob = new Blob(this.recordedChunks, { type: 'audio/webm' })
        this.recordedChunks = []
        this.masterData.isRecording = false
        
        // Cleanup
        if (this.recordingDestination) {
          this.recordingDestination.disconnect()
          this.recordingDestination = null
        }
        this.recordingStream = null
        this.mediaRecorder = null
        
        console.log(`Recording stopped on Master Output ${this.id}`)
        resolve(recordedBlob)
      }
      
      this.mediaRecorder!.stop()
    })
  }

  /**
   * Start level monitoring
   */
  private startLevelMonitoring(): void {
    if (this.levelUpdateInterval) return

    this.levelUpdateInterval = setInterval(() => {
      this.updateLevelMeters()
    }, 50) // Update 20 times per second
  }

  /**
   * Stop level monitoring
   */
  private stopLevelMonitoring(): void {
    if (this.levelUpdateInterval) {
      clearInterval(this.levelUpdateInterval)
      this.levelUpdateInterval = null
    }
  }

  /**
   * Update level meter data
   */
  private updateLevelMeters(): void {
    // Update main output levels
    if (this.analyserNode) {
      this.levelMeterData.main = this.calculateLevels(this.analyserNode)
    }
    
    // Update cue output levels
    if (this.cueAnalyserNode) {
      this.levelMeterData.cue = this.calculateLevels(this.cueAnalyserNode)
    }
  }

  /**
   * Calculate peak and RMS levels from analyser
   */
  private calculateLevels(analyser: AnalyserNode): LevelMeterData {
    const bufferLength = analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    analyser.getByteTimeDomainData(dataArray)
    
    let peak = 0
    let sum = 0
    
    for (let i = 0; i < bufferLength; i++) {
      const sample = (dataArray[i] - 128) / 128 // Convert to -1 to 1 range
      const abs = Math.abs(sample)
      
      if (abs > peak) {
        peak = abs
      }
      
      sum += sample * sample
    }
    
    const rms = Math.sqrt(sum / bufferLength)
    const clip = peak > 0.95 // Consider clipping at 95%
    
    return { peak, rms, clip }
  }

  /**
   * Get current level meter data
   */
  public getLevelMeterData(): { main: LevelMeterData; cue: LevelMeterData } {
    return {
      main: { ...this.levelMeterData.main },
      cue: { ...this.levelMeterData.cue }
    }
  }

  /**
   * Set live mode (affects processing)
   */
  public setLiveMode(isLive: boolean): void {
    this.masterData.isLive = isLive
    
    if (isLive) {
      // Optimize for live performance
      if (this.limiterNode) {
        this.limiterNode.attack.value = 0.001 // Faster attack for live
        this.limiterNode.release.value = 0.005 // Faster release for live
      }
    } else {
      // Optimize for recording/production
      if (this.limiterNode) {
        this.limiterNode.attack.value = 0.003
        this.limiterNode.release.value = 0.01
      }
    }
    
    console.log(`Master Output ${this.id} set to ${isLive ? 'live' : 'studio'} mode`)
  }

  /**
   * Get master data
   */
  public getMasterData(): MasterOutputAudioNodeData {
    return { ...this.masterData }
  }

  /**
   * Check if currently recording
   */
  public isRecording(): boolean {
    return this.masterData.isRecording
  }

  /**
   * Get recording duration
   */
  public getRecordingDuration(): number {
    if (!this.mediaRecorder || !this.masterData.isRecording) return 0
    
    // This would need to be tracked separately in a real implementation
    return 0
  }

  /**
   * Override dispose to clean up resources
   */
  public dispose(): void {
    this.stopLevelMonitoring()
    
    if (this.masterData.isRecording) {
      this.stopRecording()
    }
    
    if (this.recordingDestination) {
      this.recordingDestination.disconnect()
    }
    
    super.dispose()
  }

  /**
   * Get extended info
   */
  public getInfo(): object {
    const baseInfo = super.getInfo()
    
    return {
      ...baseInfo,
      masterData: this.masterData,
      levelMeterData: this.levelMeterData,
      limiterSettings: this.limiterNode ? {
        threshold: this.limiterNode.threshold.value,
        ratio: this.limiterNode.ratio.value,
        attack: this.limiterNode.attack.value,
        release: this.limiterNode.release.value
      } : null
    }
  }
}
