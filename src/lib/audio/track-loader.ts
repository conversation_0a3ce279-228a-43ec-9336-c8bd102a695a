/**
 * TrackLoader - <PERSON>les audio file loading, decoding, and metadata extraction
 * Supports drag-drop, file upload, and URL loading with progress tracking
 */

import { loadAudioFile } from './audio-utils'

export interface TrackMetadata {
  title?: string
  artist?: string
  album?: string
  duration: number
  sampleRate: number
  channels: number
  bitrate?: number
  format?: string
  bpm?: number
  key?: string
}

export interface LoadedTrack {
  id: string
  name: string
  url: string
  audioBuffer: AudioBuffer
  metadata: TrackMetadata
  waveformData?: Float32Array
  peakData?: Float32Array
  loadedAt: Date
}

export interface LoadProgress {
  trackId: string
  stage: 'loading' | 'decoding' | 'analyzing' | 'complete' | 'error'
  progress: number // 0-100
  message: string
  error?: string
}

export type LoadProgressCallback = (progress: LoadProgress) => void

export class TrackLoader {
  private audioContext: AudioContext
  private loadedTracks: Map<string, LoadedTrack> = new Map()
  private loadingTracks: Map<string, AbortController> = new Map()

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext
  }

  /**
   * Load track from File object
   */
  public async loadFromFile(
    file: File,
    onProgress?: LoadProgressCallback
  ): Promise<LoadedTrack> {
    const trackId = this.generateTrackId()
    const abortController = new AbortController()
    this.loadingTracks.set(trackId, abortController)

    try {
      // Validate file type
      if (!this.isAudioFile(file)) {
        throw new Error(`Unsupported file type: ${file.type}`)
      }

      onProgress?.({
        trackId,
        stage: 'loading',
        progress: 0,
        message: 'Loading file...'
      })

      // Read file as ArrayBuffer
      const arrayBuffer = await this.readFileAsArrayBuffer(file, (progress) => {
        onProgress?.({
          trackId,
          stage: 'loading',
          progress: progress * 30, // 0-30%
          message: `Loading ${file.name}...`
        })
      })

      if (abortController.signal.aborted) {
        throw new Error('Loading cancelled')
      }

      onProgress?.({
        trackId,
        stage: 'decoding',
        progress: 30,
        message: 'Decoding audio...'
      })

      // Decode audio data
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)

      onProgress?.({
        trackId,
        stage: 'analyzing',
        progress: 70,
        message: 'Analyzing audio...'
      })

      // Extract metadata
      const metadata = this.extractMetadata(audioBuffer, file)

      // Generate waveform data
      const waveformData = this.generateWaveformData(audioBuffer)
      const peakData = this.generatePeakData(audioBuffer)

      onProgress?.({
        trackId,
        stage: 'complete',
        progress: 100,
        message: 'Track loaded successfully'
      })

      const track: LoadedTrack = {
        id: trackId,
        name: file.name,
        url: URL.createObjectURL(file),
        audioBuffer,
        metadata,
        waveformData,
        peakData,
        loadedAt: new Date()
      }

      this.loadedTracks.set(trackId, track)
      this.loadingTracks.delete(trackId)

      return track

    } catch (error) {
      this.loadingTracks.delete(trackId)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      onProgress?.({
        trackId,
        stage: 'error',
        progress: 0,
        message: 'Failed to load track',
        error: errorMessage
      })

      throw error
    }
  }

  /**
   * Load track from URL
   */
  public async loadFromUrl(
    url: string,
    name?: string,
    onProgress?: LoadProgressCallback
  ): Promise<LoadedTrack> {
    const trackId = this.generateTrackId()
    const abortController = new AbortController()
    this.loadingTracks.set(trackId, abortController)

    try {
      onProgress?.({
        trackId,
        stage: 'loading',
        progress: 0,
        message: 'Fetching audio from URL...'
      })

      const response = await fetch(url, {
        signal: abortController.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()

      onProgress?.({
        trackId,
        stage: 'decoding',
        progress: 50,
        message: 'Decoding audio...'
      })

      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)

      onProgress?.({
        trackId,
        stage: 'analyzing',
        progress: 80,
        message: 'Analyzing audio...'
      })

      const metadata = this.extractMetadata(audioBuffer)
      const waveformData = this.generateWaveformData(audioBuffer)
      const peakData = this.generatePeakData(audioBuffer)

      onProgress?.({
        trackId,
        stage: 'complete',
        progress: 100,
        message: 'Track loaded successfully'
      })

      const track: LoadedTrack = {
        id: trackId,
        name: name || this.extractNameFromUrl(url),
        url,
        audioBuffer,
        metadata,
        waveformData,
        peakData,
        loadedAt: new Date()
      }

      this.loadedTracks.set(trackId, track)
      this.loadingTracks.delete(trackId)

      return track

    } catch (error) {
      this.loadingTracks.delete(trackId)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      onProgress?.({
        trackId,
        stage: 'error',
        progress: 0,
        message: 'Failed to load track from URL',
        error: errorMessage
      })

      throw error
    }
  }

  /**
   * Cancel loading track
   */
  public cancelLoading(trackId: string): void {
    const abortController = this.loadingTracks.get(trackId)
    if (abortController) {
      abortController.abort()
      this.loadingTracks.delete(trackId)
    }
  }

  /**
   * Get loaded track by ID
   */
  public getTrack(trackId: string): LoadedTrack | undefined {
    return this.loadedTracks.get(trackId)
  }

  /**
   * Get all loaded tracks
   */
  public getAllTracks(): LoadedTrack[] {
    return Array.from(this.loadedTracks.values())
  }

  /**
   * Remove track from memory
   */
  public removeTrack(trackId: string): void {
    const track = this.loadedTracks.get(trackId)
    if (track) {
      // Revoke object URL to free memory
      if (track.url.startsWith('blob:')) {
        URL.revokeObjectURL(track.url)
      }
      this.loadedTracks.delete(trackId)
    }
  }

  /**
   * Clear all tracks
   */
  public clearAll(): void {
    this.loadedTracks.forEach(track => {
      if (track.url.startsWith('blob:')) {
        URL.revokeObjectURL(track.url)
      }
    })
    this.loadedTracks.clear()
  }

  /**
   * Check if file is supported audio format
   */
  private isAudioFile(file: File): boolean {
    const supportedTypes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/wave',
      'audio/x-wav',
      'audio/ogg',
      'audio/webm',
      'audio/mp4',
      'audio/m4a',
      'audio/aac',
      'audio/flac'
    ]

    return supportedTypes.includes(file.type) || 
           /\.(mp3|wav|ogg|webm|mp4|m4a|aac|flac)$/i.test(file.name)
  }

  /**
   * Read file as ArrayBuffer with progress
   */
  private readFileAsArrayBuffer(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(reader.result)
        } else {
          reject(new Error('Failed to read file as ArrayBuffer'))
        }
      }
      
      reader.onerror = () => reject(reader.error)
      
      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = event.loaded / event.total
          onProgress?.(progress)
        }
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * Extract metadata from audio buffer and file
   */
  private extractMetadata(audioBuffer: AudioBuffer, file?: File): TrackMetadata {
    return {
      title: file?.name.replace(/\.[^/.]+$/, '') || 'Unknown',
      duration: audioBuffer.duration,
      sampleRate: audioBuffer.sampleRate,
      channels: audioBuffer.numberOfChannels,
      format: file?.type || 'unknown'
    }
  }

  /**
   * Generate waveform data for visualization
   */
  private generateWaveformData(audioBuffer: AudioBuffer, samples: number = 2000): Float32Array {
    const channelData = audioBuffer.getChannelData(0)
    const blockSize = Math.floor(channelData.length / samples)
    const waveformData = new Float32Array(samples)
    
    for (let i = 0; i < samples; i++) {
      const start = i * blockSize
      const end = Math.min(start + blockSize, channelData.length)
      
      let max = 0
      for (let j = start; j < end; j++) {
        max = Math.max(max, Math.abs(channelData[j]))
      }
      
      waveformData[i] = max
    }
    
    return waveformData
  }

  /**
   * Generate peak data for detailed waveform
   */
  private generatePeakData(audioBuffer: AudioBuffer, samples: number = 10000): Float32Array {
    return this.generateWaveformData(audioBuffer, samples)
  }

  /**
   * Extract name from URL
   */
  private extractNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      const filename = pathname.split('/').pop() || 'Unknown Track'
      return decodeURIComponent(filename)
    } catch {
      return 'Unknown Track'
    }
  }

  /**
   * Generate unique track ID
   */
  private generateTrackId(): string {
    return `track_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}
