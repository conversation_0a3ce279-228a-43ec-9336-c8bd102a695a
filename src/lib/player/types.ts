export enum MediaType {
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
}

export interface MediaSource {
  id: string
  url: string
  type: MediaType
  format: string
  quality?: 'low' | 'medium' | 'high'
  bitrate?: number
  resolution?: string
}

export interface MediaMetadata {
  title?: string
  artist?: string
  album?: string
  duration?: number
  thumbnail?: string
  description?: string
  tags?: string[]
  moods?: string[]
}

export interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  buffered: TimeRanges | null
  volume: number
  isMuted: boolean
  playbackRate: number
  quality: string
  error: string | null
}

export interface PlayerConfig {
  autoplay: boolean
  muted: boolean
  loop: boolean
  controls: boolean
  preload: 'none' | 'metadata' | 'auto'
  crossOrigin?: 'anonymous' | 'use-credentials'
  playsInline: boolean
}

export interface PlayerEvents {
  onPlay?: () => void
  onPause?: () => void
  onTimeUpdate?: (currentTime: number) => void
  onDurationChange?: (duration: number) => void
  onVolumeChange?: (volume: number, muted: boolean) => void
  onError?: (error: string) => void
  onLoadStart?: () => void
  onLoadedMetadata?: () => void
  onLoadedData?: () => void
  onCanPlay?: () => void
  onCanPlayThrough?: () => void
  onEnded?: () => void
  onSeeking?: () => void
  onSeeked?: () => void
  onWaiting?: () => void
  onProgress?: (buffered: TimeRanges) => void
}