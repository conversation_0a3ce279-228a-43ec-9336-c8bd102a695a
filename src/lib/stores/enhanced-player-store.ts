"use client"

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface MediaSource {
  id: string
  url: string
  type: 'video' | 'audio' | 'image'
  format: string
}

interface TrackMetadata {
  id: string
  title?: string
  artist?: string
  duration: number
  thumbnail?: string
  moods?: string[]
}

interface QueueItem {
  id: string
  artist: string
  source: MediaSource
  metadata: TrackMetadata
}

interface PlayerState {
  // Current playback
  currentTrack: QueueItem | null
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  globalMuted: boolean
  
  // Queue management
  queue: QueueItem[]
  currentIndex: number
  shuffleEnabled: boolean
  repeatMode: 'none' | 'one' | 'all'
  
  // Player instances
  activeInstances: Map<string, HTMLVideoElement | HTMLAudioElement>
}

interface PlayerActions {
  // Playback controls
  play: () => void
  pause: () => void
  pauseAll: () => void
  togglePlay: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  setGlobalMuted: (muted: boolean) => void
  
  // Queue management
  addToQueue: (item: QueueItem) => void
  removeFromQueue: (id: string) => void
  clearQueue: () => void
  playNext: () => void
  playPrevious: () => void
  jumpToTrack: (index: number) => void
  
  // Player instance management
  registerInstance: (id: string, element: HTMLVideoElement | HTMLAudioElement) => void
  unregisterInstance: (id: string) => void
  
  // Settings
  toggleShuffle: () => void
  setRepeatMode: (mode: 'none' | 'one' | 'all') => void
  
  // State updates
  updateCurrentTime: (time: number) => void
  updateDuration: (duration: number) => void
  setCurrentTrack: (track: QueueItem | null) => void
}

type PlayerStore = PlayerState & PlayerActions

export const usePlayerStore = create<PlayerStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentTrack: null,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1,
      isMuted: false,
      globalMuted: true, // Start muted for autoplay compliance
      queue: [],
      currentIndex: -1,
      shuffleEnabled: false,
      repeatMode: 'none',
      activeInstances: new Map(),

      // Playback controls
      play: () => {
        const { currentTrack, activeInstances } = get()
        if (currentTrack) {
          const instance = activeInstances.get(currentTrack.id)
          if (instance) {
            instance.play().catch(console.error)
          }
        }
        set({ isPlaying: true })
      },

      pause: () => {
        const { currentTrack, activeInstances } = get()
        if (currentTrack) {
          const instance = activeInstances.get(currentTrack.id)
          if (instance) {
            instance.pause()
          }
        }
        set({ isPlaying: false })
      },

      pauseAll: () => {
        const { activeInstances } = get()
        activeInstances.forEach((instance) => {
          instance.pause()
        })
        set({ isPlaying: false })
      },

      togglePlay: () => {
        const { isPlaying } = get()
        if (isPlaying) {
          get().pause()
        } else {
          get().play()
        }
      },

      seek: (time: number) => {
        const { currentTrack, activeInstances } = get()
        if (currentTrack) {
          const instance = activeInstances.get(currentTrack.id)
          if (instance) {
            instance.currentTime = time
          }
        }
        set({ currentTime: time })
      },

      setVolume: (volume: number) => {
        const { activeInstances } = get()
        activeInstances.forEach((instance) => {
          instance.volume = volume
        })
        set({ volume, isMuted: volume === 0 })
      },

      toggleMute: () => {
        const { isMuted, volume, activeInstances } = get()
        const newMuted = !isMuted
        activeInstances.forEach((instance) => {
          instance.muted = newMuted
        })
        set({ isMuted: newMuted })
      },

      setGlobalMuted: (muted: boolean) => {
        const { activeInstances } = get()
        activeInstances.forEach((instance) => {
          instance.muted = muted
        })
        set({ globalMuted: muted, isMuted: muted })
      },

      // Queue management
      addToQueue: (item: QueueItem) => {
        set((state) => ({
          queue: [...state.queue, item]
        }))
      },

      removeFromQueue: (id: string) => {
        set((state) => ({
          queue: state.queue.filter(item => item.id !== id)
        }))
      },

      clearQueue: () => {
        set({ queue: [], currentIndex: -1, currentTrack: null })
      },

      playNext: () => {
        const { queue, currentIndex, shuffleEnabled } = get()
        if (queue.length === 0) return

        let nextIndex: number
        if (shuffleEnabled) {
          nextIndex = Math.floor(Math.random() * queue.length)
        } else {
          nextIndex = (currentIndex + 1) % queue.length
        }

        set({
          currentIndex: nextIndex,
          currentTrack: queue[nextIndex]
        })
        get().play()
      },

      playPrevious: () => {
        const { queue, currentIndex } = get()
        if (queue.length === 0) return

        const prevIndex = currentIndex > 0 ? currentIndex - 1 : queue.length - 1
        set({
          currentIndex: prevIndex,
          currentTrack: queue[prevIndex]
        })
        get().play()
      },

      jumpToTrack: (index: number) => {
        const { queue } = get()
        if (index >= 0 && index < queue.length) {
          set({
            currentIndex: index,
            currentTrack: queue[index]
          })
          get().play()
        }
      },

      // Player instance management
      registerInstance: (id: string, element: HTMLVideoElement | HTMLAudioElement) => {
        set((state) => {
          const newInstances = new Map(state.activeInstances)
          newInstances.set(id, element)
          
          // Apply current settings
          element.volume = state.volume
          element.muted = state.globalMuted

          return { activeInstances: newInstances }
        })
      },

      unregisterInstance: (id: string) => {
        set((state) => {
          const newInstances = new Map(state.activeInstances)
          newInstances.delete(id)
          return { activeInstances: newInstances }
        })
      },

      // Settings
      toggleShuffle: () => {
        set((state) => ({ shuffleEnabled: !state.shuffleEnabled }))
      },

      setRepeatMode: (mode: 'none' | 'one' | 'all') => {
        set({ repeatMode: mode })
      },

      // State updates
      updateCurrentTime: (time: number) => {
        set({ currentTime: time })
      },

      updateDuration: (duration: number) => {
        set({ duration })
      },

      setCurrentTrack: (track: QueueItem | null) => {
        set({ currentTrack: track })
      },
    }),
    {
      name: 'hvppy-player-store',
    }
  )
)