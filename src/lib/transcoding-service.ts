import { prisma } from "@/lib/prisma";
import { TranscodingStatus } from "@prisma/client";
import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import { storage } from "@/lib/appwrite";
import { ID } from "appwrite";
import fs from "fs";
import path from "path";
import os from "os";

// Set the path to the ffmpeg binary
ffmpeg.setFfmpegPath(ffmpegStatic || "");

interface TranscodingJob {
  postId: string;
  originalFileUrl: string; // This will be the Appwrite file ID
  callbackUrl: string;
}

const TEMP_DIR = path.join(os.tmpdir(), "hvppy-transcode");

// Ensure temp directory exists
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

// Helper to download file from Appwrite
async function downloadFile(fileId: string, outputPath: string): Promise<void> {
  const fileBuffer = await storage.getFileDownload(
    process.env.APPWRITE_BUCKET_ID as string,
    fileId
  );
  fs.writeFileSync(outputPath, Buffer.from(fileBuffer));
}

// Helper to upload file to Appwrite
async function uploadFile(filePath: string, fileName: string): Promise<string> {
  const fileBuffer = fs.readFileSync(filePath);
  const uploadedFile = await storage.createFile(
    process.env.APPWRITE_BUCKET_ID as string,
    ID.unique(),
    new File([fileBuffer], fileName, { type: "video/mp4" }) // Assuming video/mp4 for renditions
  );
  return uploadedFile.$id;
}

export async function sendTranscodingJob(job: TranscodingJob) {
  console.log(`[Transcoding Service] Starting job for postId: ${job.postId}`);

  const originalFileId = job.originalFileUrl; // Assuming originalFileUrl is the Appwrite file ID
  const originalFilePath = path.join(TEMP_DIR, `${job.postId}_original.mp4`); // Assuming MP4 for simplicity

  try {
    // 1. Download original file from Appwrite
    await downloadFile(originalFileId, originalFilePath);
    console.log(`[Transcoding Service] Downloaded original file: ${originalFilePath}`);

    const renditions: { [key: string]: string } = {};
    const outputPromises: Promise<void>[] = [];

    // Define transcoding profiles
    const profiles = [
      { name: "low", resolution: "640x360", bitrate: "500k" },
      { name: "medium", resolution: "854x480", bitrate: "1000k" },
      { name: "high", resolution: "1280x720", bitrate: "2500k" },
    ];

    for (const profile of profiles) {
      const outputFileName = `${job.postId}_${profile.name}.mp4`;
      const outputFilePath = path.join(TEMP_DIR, outputFileName);

      const promise = new Promise<void>((resolve, reject) => {
        ffmpeg(originalFilePath)
          .output(outputFilePath)
          .videoCodec("libx264")
          .audioCodec("aac")
          .size(profile.resolution)
          .videoBitrate(profile.bitrate)
          .on("end", async () => {
            console.log(`[Transcoding Service] Finished transcoding ${profile.name} for ${job.postId}`);
            try {
              const uploadedFileId = await uploadFile(outputFilePath, outputFileName);
              renditions[profile.name] = `${process.env.NEXTAUTH_URL}/api/media/stream/${uploadedFileId}`;
              resolve();
            } catch (uploadError) {
              console.error(`[Transcoding Service] Error uploading ${profile.name} rendition:`, uploadError);
              reject(uploadError);
            }
          })
          .on("error", (err) => {
            console.error(`[Transcoding Service] Error transcoding ${profile.name} for ${job.postId}:`, err);
            reject(err);
          })
          .run();
      });
      outputPromises.push(promise);
    }

    await Promise.all(outputPromises);

    // 3. Send callback to our API with COMPLETED status and renditions
    await fetch(job.callbackUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": process.env.TRANSCODING_SERVICE_API_KEY as string,
      },
      body: JSON.stringify({
        postId: job.postId,
        status: TranscodingStatus.COMPLETED,
        renditions: renditions,
        message: "Transcoding successful",
      }),
    });
    console.log(`[Transcoding Service] Callback sent for postId: ${job.postId}`);
  } catch (error: any) {
    console.error(`[Transcoding Service] Error during transcoding job for postId: ${job.postId}:`, error);
    // Send callback with FAILED status
    try {
      await fetch(job.callbackUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": process.env.TRANSCODING_SERVICE_API_KEY as string,
        },
        body: JSON.stringify({
          postId: job.postId,
          status: TranscodingStatus.FAILED,
          message: `Transcoding failed: ${error.message || error}`,
        }),
      });
    } catch (callbackError) {
      console.error(`[Transcoding Service] Error sending FAILED callback for postId: ${job.postId}`, callbackError);
    }
  } finally {
    // Clean up temporary files
    if (fs.existsSync(originalFilePath)) {
      fs.unlinkSync(originalFilePath);
    }
    for (const profile of [
      { name: "low" },
      { name: "medium" },
      { name: "high" },
    ]) {
      const outputFilePath = path.join(TEMP_DIR, `${job.postId}_${profile.name}.mp4`);
      if (fs.existsSync(outputFilePath)) {
        fs.unlinkSync(outputFilePath);
      }
    }
    console.log(`[Transcoding Service] Cleaned up temporary files for postId: ${job.postId}`);
  }
}