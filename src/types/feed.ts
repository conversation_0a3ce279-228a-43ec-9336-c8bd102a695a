import { ContentType, ReactionType } from './index'

export enum FeedType {
  DISCOVER = 'DISCOVER',
  FOLLOWING = 'FOLLOWING',
  TRENDING = 'TRENDING',
  MOOD = 'MOOD',
  CREATOR = 'CREATOR',
  PERSONA = 'PERSONA',
  LIVE = 'LIVE',
}

export enum InteractionType {
  LIKE = 'LIKE',
  LOVE = 'LOVE',
  FIRE = 'FIRE',
  MIND_BLOWN = 'MIND_BLOWN',
  VIBE = 'VIBE',
  MOOD_MATCH = 'MOOD_MATCH',
  COMMENT = 'COMMENT',
  SHARE = 'SHARE',
  MEMORY = 'MEMORY',
  VIEW = 'VIEW',
}

export interface User {
  id: string
  appwriteId: string
  email: string
  username?: string
  displayName?: string
  avatarUrl?: string
  role: string
  isVerified: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Creator {
  id: string
  userId: string
  user: User
  name?: string
  stageName?: string
  genre: string[]
  avatarUrl?: string
  totalFollowers: number
  totalViews: number
  totalLikes: number
  createdAt: Date
  updatedAt: Date
  personas: Persona[]
}

export interface Persona {
  id: string
  creatorId: string
  creator: Creator
  name: string
  description?: string
  avatar?: string
  coverImage?: string
  mood: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Reaction {
  id: string
  userId: string
  user: User
  postId: string
  type: ReactionType
  mood?: string
  createdAt: Date
}

export interface Memory {
  id: string
  userId: string
  user: User
  postId: string
  createdAt: Date
}

export interface Comment {
  id: string
  userId: string
  user: User
  postId: string
  content: string
  createdAt: Date
  updatedAt: Date
}

export interface Post {
  id: string
  userId: string
  user: User
  creatorId?: string
  creator?: Creator
  personaId?: string
  persona?: Persona
  title?: string
  content: string
  description?: string
  contentType: ContentType
  contentUrl?: string
  mediaUrls: string[]
  thumbnailUrl?: string
  transcodingStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED'
  renditions?: { [key: string]: string }
  moderationStatus: 'PENDING' | 'APPROVED' | 'REJECTED'
  moderationNotes?: string
  moods: string[]
  aiAnalysis?: any
  viewCount: number
  likeCount: number
  shareCount: number
  reactions: Reaction[]
  memories: Memory[]
  comments: Comment[]
  creatorNotes?: string
  supporterActions?: any
  isPublic: boolean
  isExperimental: boolean
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

export interface FeedItem {
  id: string
  post: Post
  score?: number
  reason?: string
  metadata?: any
}

export interface InteractionData {
  postId: string
  type: InteractionType
  mood?: string
  timestamp: Date
  metadata?: any
}

export interface FeedFilters {
  moods?: string[]
  contentTypes?: ContentType[]
  creators?: string[]
  personas?: string[]
  timeRange?: {
    start: Date
    end: Date
  }
  minEngagement?: number
  isExperimental?: boolean
}

export interface FeedPreferences {
  autoPlay: boolean
  muteByDefault: boolean
  showCaptions: boolean
  preferredQuality: 'low' | 'medium' | 'high' | 'auto'
  enableHapticFeedback: boolean
  preloadDistance: number
  offloadDistance: number
}

export interface VideoPlayerState {
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  isFullscreen: boolean
  quality: string
  buffered: TimeRanges | null
  error: string | null
}

export interface VerticalFeedContainerProps {
  feedType: FeedType
  filters?: FeedFilters
  onItemChange?: (item: FeedItem, index: number) => void
  onInteraction?: (interaction: InteractionData) => void
  className?: string
  autoPlay?: boolean
}

export interface ContentCardProps {
  item: FeedItem
  isActive: boolean
  autoPlay?: boolean
  onInteraction?: (interaction: InteractionData) => void
  className?: string
}

export interface FeedControlsProps {
  item: FeedItem
  isLiked: boolean
  isSaved: boolean
  onLike: () => void
  onSave: () => void
  onShare: () => void
  onComment: () => void
  className?: string
}

export interface MoodSelectorProps {
  selectedMoods: string[]
  onMoodChange: (moods: string[]) => void
  moodCounts?: Record<string, number>
  className?: string
}

export interface CreatorInfoProps {
  creator: Creator
  persona?: Persona
  isFollowing: boolean
  onFollow: () => void
  className?: string
}

export interface FeedNavigationProps {
  currentFeed: FeedType
  onFeedChange: (feedType: FeedType) => void
  className?: string
}