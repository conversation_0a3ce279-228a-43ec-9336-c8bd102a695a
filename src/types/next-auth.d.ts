import NextAuth, { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";
import { UserRole } from "@prisma/client";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      role?: UserRole; // Add role to user in session
      twoFactorEnabled?: boolean; // Add twoFactorEnabled to user in session
    } & DefaultSession["user"];
  }

  interface User {
    role?: UserRole; // Add role to user in User type
    twoFactorEnabled?: boolean; // Add twoFactorEnabled to user in User type
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role?: UserRole; // Add role to JWT
    twoFactorEnabled?: boolean; // Add twoFactorEnabled to JWT
  }
}